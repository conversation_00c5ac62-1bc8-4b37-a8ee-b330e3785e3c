import React from 'react';
import { useGraph } from '@react-three/fiber';
import { useGLTF } from '@react-three/drei';
import { SkeletonUtils } from 'three-stdlib';
import { useLoader } from '@react-three/fiber';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader';

export function Model(props) {
  const { scene } = useGLTF('/avatar_female.glb');
  const fbx = useLoader(FBXLoader, '/idle_sasthra_female.fbx');
  const clone = React.useMemo(() => SkeletonUtils.clone(scene), [scene]);
  const { nodes, materials } = useGraph(clone);
  return (
    <group {...props} dispose={null} scale={10}>
      <primitive object={fbx} />
      <primitive object={nodes.mixamorigHips} />
      <primitive object={nodes.Ctrl_Master} />
      <primitive object={nodes.Ctrl_ArmPole_IK_Left} />
      <primitive object={nodes.Ctrl_Hand_IK_Left} />
      <primitive object={nodes.Ctrl_ArmPole_IK_Right} />
      <primitive object={nodes.Ctrl_Hand_IK_Right} />
      <primitive object={nodes.Ctrl_Foot_IK_Left} />
      <primitive object={nodes.Ctrl_LegPole_IK_Left} />
      <primitive object={nodes.Ctrl_Foot_IK_Right} />
      <primitive object={nodes.Ctrl_LegPole_IK_Right} />
      <skinnedMesh
        name="outfit_outfit_0008"
        geometry={nodes.outfit_outfit_0008.geometry}
        material={materials.AvatarBody}
        skeleton={nodes.outfit_outfit_0008.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_1"
        geometry={nodes.outfit_outfit_0008_1.geometry}
        material={materials['Hair.003']}
        skeleton={nodes.outfit_outfit_0008_1.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_1.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_1.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_2"
        geometry={nodes.outfit_outfit_0008_2.geometry}
        material={materials['Hair.002']}
        skeleton={nodes.outfit_outfit_0008_2.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_2.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_2.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_3"
        geometry={nodes.outfit_outfit_0008_3.geometry}
        material={materials.outfit}
        skeleton={nodes.outfit_outfit_0008_3.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_3.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_3.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_4"
        geometry={nodes.outfit_outfit_0008_4.geometry}
        material={materials.Std_Skin_Head}
        skeleton={nodes.outfit_outfit_0008_4.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_4.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_4.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_5"
        geometry={nodes.outfit_outfit_0008_5.geometry}
        material={materials.Std_Upper_Teeth}
        skeleton={nodes.outfit_outfit_0008_5.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_5.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_5.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_6"
        geometry={nodes.outfit_outfit_0008_6.geometry}
        material={materials.Std_Lower_Teeth}
        skeleton={nodes.outfit_outfit_0008_6.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_6.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_6.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_7"
        geometry={nodes.outfit_outfit_0008_7.geometry}
        material={materials.Std_Eyelash}
        skeleton={nodes.outfit_outfit_0008_7.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_7.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_7.morphTargetInfluences}
      />
      {/* <skinnedMesh
        name="outfit_outfit_0008_8"
        geometry={nodes.outfit_outfit_0008_8.geometry}
        material={materials.Scalp_Transparency}
        skeleton={nodes.outfit_outfit_0008_8.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_8.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_8.morphTargetInfluences}
      /> */}
      <skinnedMesh
        name="outfit_outfit_0008_9"
        geometry={nodes.outfit_outfit_0008_9.geometry}
        material={materials.Std_Eye_Occlusion_R}
        skeleton={nodes.outfit_outfit_0008_9.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_9.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_9.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_10"
        geometry={nodes.outfit_outfit_0008_10.geometry}
        material={materials.Std_Eye_Occlusion_R}
        skeleton={nodes.outfit_outfit_0008_10.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_10.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_10.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_11"
        geometry={nodes.outfit_outfit_0008_11.geometry}
        material={materials.Std_Eye_R}
        skeleton={nodes.outfit_outfit_0008_11.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_11.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_11.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_12"
        geometry={nodes.outfit_outfit_0008_12.geometry}
        material={materials.Std_Cornea_R}
        skeleton={nodes.outfit_outfit_0008_12.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_12.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_12.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_13"
        geometry={nodes.outfit_outfit_0008_13.geometry}
        material={materials.Std_Eye_L}
        skeleton={nodes.outfit_outfit_0008_13.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_13.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_13.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_14"
        geometry={nodes.outfit_outfit_0008_14.geometry}
        material={materials.Std_Cornea_R}
        skeleton={nodes.outfit_outfit_0008_14.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_14.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_14.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_15"
        geometry={nodes.outfit_outfit_0008_15.geometry}
        material={materials.Std_Tearline_R}
        skeleton={nodes.outfit_outfit_0008_15.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_15.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_15.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_16"
        geometry={nodes.outfit_outfit_0008_16.geometry}
        material={materials.Std_Tearline_R}
        skeleton={nodes.outfit_outfit_0008_16.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_16.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_16.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_17"
        geometry={nodes.outfit_outfit_0008_17.geometry}
        material={materials.Female_Brow_Transparency}
        skeleton={nodes.outfit_outfit_0008_17.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_17.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_17.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_18"
        geometry={nodes.outfit_outfit_0008_18.geometry}
        material={materials.Female_Brow_Base_Transparency}
        skeleton={nodes.outfit_outfit_0008_18.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_18.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_18.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_19"
        geometry={nodes.outfit_outfit_0008_19.geometry}
        material={materials.Silver}
        skeleton={nodes.outfit_outfit_0008_19.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_19.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_19.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_20"
        geometry={nodes.outfit_outfit_0008_20.geometry}
        material={materials.Std_Skin_Body}
        skeleton={nodes.outfit_outfit_0008_20.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_20.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_20.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_21"
        geometry={nodes.outfit_outfit_0008_21.geometry}
        material={materials.Std_Skin_Arm}
        skeleton={nodes.outfit_outfit_0008_21.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_21.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_21.morphTargetInfluences}
      />
      <skinnedMesh
        name="outfit_outfit_0008_22"
        geometry={nodes.outfit_outfit_0008_22.geometry}
        material={materials.Std_Tongue}
        skeleton={nodes.outfit_outfit_0008_22.skeleton}
        morphTargetDictionary={nodes.outfit_outfit_0008_22.morphTargetDictionary}
        morphTargetInfluences={nodes.outfit_outfit_0008_22.morphTargetInfluences}
      />
    </group>
  );
}

useGLTF.preload('/avatar_female.glb');
