import React from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App.jsx';
import { BrowserRouter } from 'react-router';
import { Provider } from 'react-redux';
import { store } from './redux/store/store.js';
import 'katex/dist/katex.min.css';

// Register service workers
if ('serviceWorker' in navigator) {
  // Register Firebase messaging service worker
  navigator.serviceWorker
    .register('/firebase-messaging-sw.js')
    .then((registration) => {
      console.log('Firebase Service Worker registered:', registration);
    })
    .catch((error) => {
      console.error('Firebase Service Worker registration failed:', error);
    });

  // Register streaming service worker
  navigator.serviceWorker
    .register('/streaming-sw.js')
    .then((registration) => {
      console.log('Streaming Service Worker registered:', registration);
    })
    .catch((error) => {
      console.error('Streaming Service Worker registration failed:', error);
    });
}

createRoot(document.getElementById('root')).render(
  <Provider store={store}>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </Provider>
);
