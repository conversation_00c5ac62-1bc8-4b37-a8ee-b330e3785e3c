import { CenterTraineeDashboardApi } from '../../../../redux/api/api';

const initialState = {
  centerStudentsFacultyData: null
};

export const centerStudentsFacultySlice = CenterTraineeDashboardApi.injectEndpoints({
  endpoints: (builder) => ({
    getCenterStudentsFaculty: builder.query({
      query: () => ({
        url: '/faculty-dashboard',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Center Students Faculty Data:', response);

        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CenterTraineeDashboard']
    })
  })
});

export const { useLazyGetCenterStudentsFacultyQuery } = centerStudentsFacultySlice;
