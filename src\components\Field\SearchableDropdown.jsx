import { useState, useEffect } from 'react';
import Select from 'react-select';
import PropTypes from 'prop-types';

const SearchableDropdown = ({
  options, // Default value to prevent undefined errors
  label,
  placeholder,
  onChange,
  required,
  className = '',
  value,
  disabled
}) => {
  const [selectedOption, setSelectedOption] = useState(null);

  useEffect(() => {
    if (value && Array.isArray(options)) {
      const selected = options.find((opt) => opt.id === value);
      setSelectedOption(selected ? { value: selected.id, label: selected.name } : null);
    } else {
      setSelectedOption(null);
    }
  }, [value, options]);

  const handleChange = (selected) => {
    if (!selected) {
      setSelectedOption(null);
      if (onChange) onChange(null);
      return;
    }

    setSelectedOption(selected);
    if (onChange) onChange({ id: selected.value, name: selected.label });
  };

  const formattedOptions = Array.isArray(options)
    ? options.map((opt) => ({
        value: opt.id,
        label: opt.name
      }))
    : [];

  const customStyles = {
    control: (provided, state) => ({
      ...provided,
      backgroundColor: disabled ? '#e5e7eb' : 'white',
      borderColor: state.isFocused ? 'gray' : '#e5e7eb',
      borderWidth: 2,
      padding: '0.10rem',
      fontSize: '1rem',
      boxShadow: state.isFocused ? '0 0 5px rgba(0, 0, 255, 0.5)' : 'none',
      cursor: disabled ? 'not-allowed' : 'default'
    })
  };

  return (
    <div className={`w-full ${className}`}>
      {label && (
        <label className="tracking-wide block mb-1 font-semibold">
          {label}
          {required && <span className="text-lg font-bold text-red-600 ml-1">*</span>}
        </label>
      )}

      <Select
        options={formattedOptions}
        styles={customStyles}
        value={selectedOption}
        onChange={handleChange}
        placeholder={placeholder}
        isDisabled={disabled}
        isSearchable={true}
        isClearable={true}
        className="text-black"
      />
    </div>
  );
};

SearchableDropdown.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired
    })
  ),
  label: PropTypes.string,
  placeholder: PropTypes.string,
  onChange: PropTypes.func,
  required: PropTypes.bool,
  className: PropTypes.string,
  value: PropTypes.string,
  disabled: PropTypes.bool
};

export default SearchableDropdown;
