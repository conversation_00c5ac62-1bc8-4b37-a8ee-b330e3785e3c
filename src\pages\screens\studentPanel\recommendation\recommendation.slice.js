import { createSlice } from '@reduxjs/toolkit';
import { recommendationApi } from '../../../../redux/api/api';

const initialState = {
  recommendationData: null,
  recommendationPendingData: null,
  completedRecommendations: null
};

export const recommendationApiSlice = recommendationApi.injectEndpoints({
  endpoints: (builder) => ({
    getRecommendations: builder.query({
      query: (query) => {
        return `/missions/playlist/${query.user_id}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),
    getCompletedRecommendations: builder.query({
      query: (query) => {
        return `/missions/history/${query.user_id}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),
    getRecommendationsById: builder.query({
      query: (query) => {
        return `/missions/content/${query.mission_id}?user_id=${query.user_id}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),
    submitRecommendations: builder.mutation({
      query: (body) => ({
        url: `/missions/submit`,
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Login']
    })
  })
});

const recommendationSlice = createSlice({
  name: 'recommendation',
  initialState,
  reducers: {
    setRecommendationData: (state, action) => {
      state.recommendationData = action.payload;
    },
    clearRecommendationData: (state) => {
      state.recommendationData = null;
    },
    setRecommendationPendingData: (state, action) => {
      state.recommendationPendingData = action.payload;
    },
    clearRecommendationPendingData: (state) => {
      state.recommendationPendingData = null;
    },
    setCompletedRecommendations: (state, action) => {
      state.completedRecommendations = action.payload;
    },
    clearCompletedRecommendations: (state) => {
      state.completedRecommendations = null;
    }
  }
});

export const {
  useLazyGetRecommendationsQuery,
  useLazyGetRecommendationsByIdQuery,
  useLazyGetCompletedRecommendationsQuery,
  useSubmitRecommendationsMutation
} = recommendationApiSlice;
export const {
  setRecommendationData,
  clearRecommendationData,
  setRecommendationPendingData,
  clearRecommendationPendingData,
  setCompletedRecommendations,
  clearCompletedRecommendations
} = recommendationSlice.actions;
export const selectRecommendationData = (state) => state.recommendation.recommendationData;
export default recommendationSlice.reducer;
