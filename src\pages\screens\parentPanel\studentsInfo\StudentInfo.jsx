'use client';

import { useEffect } from 'react';
import { useLazyStudentDetailsServiceQuery } from './studentInfo.slice';

const StudentInfo = () => {
  const [triggerStudentDetails, { data, isLoading, isError, error }] =
    useLazyStudentDetailsServiceQuery();

  useEffect(() => {
    triggerStudentDetails();
  }, [triggerStudentDetails]);

  const student = data?.student;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#10e7dc] mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading student information...</p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h3 className="text-red-800 font-semibold text-lg mb-2">
            Error Loading Student Information
          </h3>
          <p className="text-red-600">
            {error?.data?.message || 'Unable to fetch student details. Please try again.'}
          </p>
        </div>
      </div>
    );
  }

  if (!student) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
          <div className="text-gray-400 text-4xl mb-4">👤</div>
          <p className="text-gray-600 font-medium">No student information available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div
        className="relative overflow-hidden rounded-2xl shadow-2xl"
        style={{
          background: `var(--color-parents)`
        }}
      >
        <div className="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-10 rounded-full translate-y-12 -translate-x-12"></div>

        <div className="relative z-10 p-8">
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-white bg-opacity-20 rounded-full mb-4">
              <span className="text-2xl">🎓</span>
            </div>
            <h2 className="text-3xl font-bold text-white mb-2">Student Profile</h2>
            <div className="w-24 h-1 bg-white bg-opacity-50 mx-auto rounded-full"></div>
          </div>

          <div className="bg-white bg-opacity-95 backdrop-blur-sm rounded-xl p-8 shadow-lg">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-800 border-b-2 border-[var(--color-parents)] pb-2 mb-4">
                  Personal Information
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-[var(--color-parents)] rounded-full"></div>
                    <div>
                      <span className="text-sm text-gray-500 block">Full Name</span>
                      <span className="text-gray-900 font-medium">
                        {student.first_name} {student.last_name}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-[var(--color-parents)] rounded-full"></div>
                    <div>
                      <span className="text-sm text-gray-500 block">Student ID</span>
                      <span className="text-gray-900 font-medium">{student.id || 'N/A'}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-[var(--color-parents)] rounded-full"></div>
                    <div>
                      <span className="text-sm text-gray-500 block">Email Address</span>
                      <a href={`mailto:${student.student_email}`}>
                        <span className="text-gray-900 font-medium text-sm sm:text-base md:text-lg">
                          {student.student_email || 'N/A'}
                        </span>
                      </a>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-[var(--color-parents)] rounded-full"></div>
                    <div>
                      <span className="text-sm text-gray-500 block">Phone Number</span>
                      <span className="text-gray-900 font-medium">{student.phone || 'N/A'}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-[var(--color-parents)] rounded-full"></div>
                    <div>
                      <span className="text-sm text-gray-500 block">Date of Birth</span>
                      <span className="text-gray-900 font-medium">
                        {student.dob ? new Date(student.dob).toLocaleDateString() : 'N/A'}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-[var(--color-parents)] rounded-full"></div>
                    <div>
                      <span className="text-sm text-gray-500 block">Religion</span>
                      <span className="text-gray-900 font-medium">{student.religion || 'N/A'}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-800 border-b-2 border-[var(--color-parents)] pb-2 mb-4">
                  Academic Records
                </h3>
                <div className="space-y-6">
                  <div className="bg-[var(--color-parents)] p-4 rounded-lg text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-sm opacity-90 block">10th Grade Marks</span>
                        <span className="text-2xl font-bold">{student.marks_10th || 'N/A'}</span>
                      </div>
                      <div className="text-3xl opacity-80">📚</div>
                    </div>
                  </div>
                  <div className="bg-[var(--color-parents)] p-4 rounded-lg text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-sm opacity-90 block">12th Grade Marks</span>
                        <span className="text-2xl font-bold">{student.marks_12th || 'N/A'}</span>
                      </div>
                      <div className="text-3xl opacity-80">🎯</div>
                    </div>
                  </div>
                </div>
                <div className="mt-8 p-4 bg-gray-50 rounded-lg border-l-4 border-[var(--color-parents)]">
                  <p className="text-sm text-gray-600 italic">
                    "Trust your child’s journey, even if it doesn’t look like yours..."
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentInfo;
