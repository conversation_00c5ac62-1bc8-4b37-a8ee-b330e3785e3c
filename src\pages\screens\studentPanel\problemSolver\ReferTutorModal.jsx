import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaTimes,
  FaChalkboardTeacher,
  FaGraduationCap,
  FaStar,
  FaClock,
  FaCalendarAlt,
  FaPhone,
  FaVideo
} from 'react-icons/fa';

const ReferTutorModal = ({ isOpen, onClose, setFeedbackMessage }) => {
  const [tutorModalStep, setTutorModalStep] = useState(0);
  const [selectedSubject, setSelectedSubject] = useState('');
  const [floatingElements, setFloatingElements] = useState([]);

  const subjects = [
    { name: 'Mathematics', icon: '📐', color: 'from-blue-500 to-cyan-500' },
    { name: 'Physics', icon: '⚛️', color: 'from-purple-500 to-pink-500' },
    { name: 'Chemistry', icon: '🧪', color: 'from-green-500 to-teal-500' },
    { name: 'Biology', icon: '🧬', color: 'from-orange-500 to-red-500' },
    { name: 'Computer Science', icon: '💻', color: 'from-indigo-500 to-purple-500' },
    { name: 'English', icon: '📚', color: 'from-pink-500 to-rose-500' }
  ];

  useEffect(() => {
    if (isOpen) {
      const elements = Array.from({ length: 15 }, (_, i) => ({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: Math.random() * 20 + 10,
        delay: Math.random() * 2,
        duration: Math.random() * 3 + 2,
        icon: [FaGraduationCap, FaChalkboardTeacher, FaStar][Math.floor(Math.random() * 3)]
      }));
      setFloatingElements(elements);
    }
  }, [isOpen]);

  const handleClose = () => {
    onClose();
    setTimeout(() => {
      setTutorModalStep(0);
      setSelectedSubject('');
    }, 300);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          onClick={handleClose}
        >
          {floatingElements.map((element) => {
            const IconComponent = element.icon;
            return (
              <motion.div
                key={element.id}
                className="absolute text-white/10 pointer-events-none"
                style={{
                  left: `${element.x}%`,
                  top: `${element.y}%`,
                  fontSize: `${element.size}px`
                }}
                animate={{
                  y: [0, -20, 0],
                  rotate: [0, 360],
                  scale: [1, 1.2, 1]
                }}
                transition={{
                  duration: element.duration,
                  delay: element.delay,
                  repeat: Number.POSITIVE_INFINITY,
                  ease: 'easeInOut'
                }}
              >
                <IconComponent />
              </motion.div>
            );
          })}
          <motion.div
            initial={{ scale: 0.5, opacity: 0, y: 100 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.5, opacity: 0, y: 100 }}
            transition={{
              type: 'spring',
              damping: 25,
              stiffness: 300,
              duration: 0.6
            }}
            className="bg-white rounded-3xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 p-6 text-white overflow-hidden">
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
                animate={{ x: [-100, 400] }}
                transition={{ duration: 3, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
              />
              <div className="relative z-10 flex justify-between items-center">
                <div className="flex items-center space-x-3">
                  <motion.div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <FaChalkboardTeacher className="text-2xl" />
                  </motion.div>
                  <div>
                    <h3 className="text-2xl font-bold">Connect with Expert Tutors</h3>
                    <p className="text-white/80 text-sm">
                      Get personalized help from qualified educators
                    </p>
                  </div>
                </div>
                <motion.button
                  whileHover={{ scale: 1.1, rotate: 90 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleClose}
                  className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300 backdrop-blur-sm"
                >
                  <FaTimes className="text-lg" />
                </motion.button>
              </div>
            </div>
            <div className="p-8 max-h-[80vh] max-w-[80vw] overflow-y-auto">
              <AnimatePresence mode="wait">
                {tutorModalStep === 0 && (
                  <motion.div
                    key="step0"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -50 }}
                    className="space-y-6"
                  >
                    <div className="text-center space-y-4 overflow-scroll">
                      <motion.div
                        animate={{
                          scale: [1, 1.1, 1],
                          rotate: [0, 5, -5, 0]
                        }}
                        transition={{ duration: 4, repeat: Number.POSITIVE_INFINITY }}
                        className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto shadow-2xl"
                      >
                        <FaGraduationCap className="text-white text-3xl" />
                      </motion.div>
                      <h4 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        Choose Your Subject
                      </h4>
                      <p className="text-gray-600 text-lg">
                        Select the subject you need help with to connect with the right tutor
                      </p>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      {subjects.map((subject, index) => (
                        <motion.button
                          key={subject.name}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          whileHover={{ scale: 1.05, y: -5 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => {
                            setSelectedSubject(subject.name);
                            setTutorModalStep(1);
                          }}
                          className={`p-4 rounded-2xl bg-gradient-to-r ${subject.color} text-white shadow-lg hover:shadow-xl transition-all duration-300 group`}
                        >
                          <div className="flex flex-col items-center space-y-2">
                            <motion.span
                              className="text-3xl"
                              animate={{ rotate: [0, 10, -10, 0] }}
                              transition={{
                                duration: 2,
                                repeat: Number.POSITIVE_INFINITY,
                                delay: index * 0.2
                              }}
                            >
                              {subject.icon}
                            </motion.span>
                            <span className="font-semibold text-sm group-hover:scale-110 transition-transform duration-300">
                              {subject.name}
                            </span>
                          </div>
                        </motion.button>
                      ))}
                    </div>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.8 }}
                      className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100"
                    >
                      <div className="flex items-center space-x-3 mb-4">
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                          className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center"
                        >
                          <FaStar className="text-white text-sm" />
                        </motion.div>
                        <h5 className="font-bold text-gray-800">Why Choose Our Tutors?</h5>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm text-gray-700">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                          <span>Verified Experts</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span>24/7 Availability</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          <span>Personalized Learning</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                          <span>Instant Connect</span>
                        </div>
                      </div>
                    </motion.div>
                  </motion.div>
                )}
                {tutorModalStep === 1 && (
                  <motion.div
                    key="step1"
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -50 }}
                    className="space-y-6"
                  >
                    <div className="text-center space-y-4">
                      <motion.div
                        animate={{
                          scale: [1, 1.1, 1],
                          boxShadow: [
                            '0 0 20px rgba(59, 130, 246, 0.3)',
                            '0 0 40px rgba(139, 92, 246, 0.5)',
                            '0 0 20px rgba(59, 130, 246, 0.3)'
                          ]
                        }}
                        transition={{ duration: 3, repeat: Number.POSITIVE_INFINITY }}
                        className="w-24 h-24 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mx-auto"
                      >
                        <FaVideo className="text-white text-3xl" />
                      </motion.div>
                      <h4 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                        {selectedSubject} Tutor
                      </h4>
                      <p className="text-gray-600 text-lg">
                        Great choice! Our expert {selectedSubject.toLowerCase()} tutors are ready to
                        help you succeed.
                      </p>
                    </div>
                    <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-100">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <FaClock className="text-green-600 text-xl" />
                            <span className="font-semibold text-gray-800">Available Now</span>
                          </div>
                          <motion.div
                            animate={{ scale: [1, 1.2, 1] }}
                            transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY }}
                            className="w-3 h-3 bg-green-500 rounded-full"
                          />
                        </div>
                        <div className="flex items-center space-x-3">
                          <FaCalendarAlt className="text-blue-600 text-xl" />
                          <span className="text-gray-700">Schedule for later</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <FaPhone className="text-purple-600 text-xl" />
                          <span className="text-gray-700">Voice & Video Call Support</span>
                        </div>
                      </div>
                    </div>
                    <div className="bg-yellow-50 border border-yellow-200 rounded-2xl p-6">
                      <div className="flex items-center space-x-3 mb-3">
                        <motion.div
                          animate={{ rotate: [0, 10, -10, 0] }}
                          transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY }}
                        >
                          🚀
                        </motion.div>
                        <h5 className="font-bold text-yellow-800">Coming Soon!</h5>
                      </div>
                      <p className="text-yellow-700 text-sm">
                        We're putting the finishing touches on our tutor connection system. You'll
                        be able to connect with expert {selectedSubject.toLowerCase()} tutors very
                        soon!
                      </p>
                    </div>
                    <div className="flex space-x-3">
                      <motion.button
                        whileHover={{ scale: 1.05, x: -5 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setTutorModalStep(0)}
                        className="flex-1 py-3 px-6 bg-gray-200 text-gray-700 rounded-2xl font-semibold hover:bg-gray-300 transition-all duration-300"
                      >
                        ← Back
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => {
                          setFeedbackMessage(
                            `Great! We'll notify you when ${selectedSubject} tutors are available.`
                          );
                          handleClose();
                        }}
                        className="flex-1 py-3 px-6 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                      >
                        Notify Me! 🔔
                      </motion.button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ReferTutorModal;
