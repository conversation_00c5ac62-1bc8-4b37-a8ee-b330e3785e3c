/**
 * ULTIMATE LiveKit Background Protection Utility
 * Based on LiveKit documentation and best practices
 * Ensures streaming continues during tab switching
 */

class LiveKitBackgroundProtection {
  constructor() {
    this.isActive = false;
    this.room = null;
    this.intervals = {
      websocketKeepAlive: null,
      trackMonitor: null,
      connectionMonitor: null,
      visibilityMonitor: null
    };
    this.lastVisibilityChange = Date.now();
    this.backgroundStartTime = null;
    
    // Bind methods
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
    this.handlePageHide = this.handlePageHide.bind(this);
    this.handlePageShow = this.handlePageShow.bind(this);
    this.handleBeforeUnload = this.handleBeforeUnload.bind(this);
  }

  // Start protection for a LiveKit room
  start(room) {
    if (!room) {
      console.error('❌ No room provided to LiveKit background protection');
      return;
    }

    console.log('🛡️ Starting ULTIMATE LiveKit background protection...');
    
    this.room = room;
    this.isActive = true;

    // 1. Set up page visibility monitoring
    this.setupVisibilityMonitoring();

    // 2. Start aggressive WebSocket keep-alive
    this.startWebSocketKeepAlive();

    // 3. Start track health monitoring
    this.startTrackMonitoring();

    // 4. Start connection state monitoring
    this.startConnectionMonitoring();

    // 5. Start visibility-based monitoring
    this.startVisibilityBasedMonitoring();

    console.log('✅ ULTIMATE LiveKit background protection started');
  }

  // Stop all protection mechanisms
  stop() {
    console.log('🛑 Stopping LiveKit background protection...');
    
    this.isActive = false;
    this.room = null;

    // Clear all intervals
    Object.keys(this.intervals).forEach(key => {
      if (this.intervals[key]) {
        clearInterval(this.intervals[key]);
        this.intervals[key] = null;
      }
    });

    // Remove event listeners
    this.removeVisibilityMonitoring();

    console.log('✅ LiveKit background protection stopped');
  }

  // Set up page visibility monitoring
  setupVisibilityMonitoring() {
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    window.addEventListener('pagehide', this.handlePageHide);
    window.addEventListener('pageshow', this.handlePageShow);
    window.addEventListener('beforeunload', this.handleBeforeUnload);
    window.addEventListener('focus', this.handlePageShow);
    window.addEventListener('blur', this.handlePageHide);
  }

  // Remove visibility monitoring
  removeVisibilityMonitoring() {
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('pagehide', this.handlePageHide);
    window.removeEventListener('pageshow', this.handlePageShow);
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
    window.removeEventListener('focus', this.handlePageShow);
    window.removeEventListener('blur', this.handlePageHide);
  }

  // Handle visibility change
  handleVisibilityChange() {
    const isHidden = document.hidden;
    this.lastVisibilityChange = Date.now();
    
    console.log('👁️ Page visibility changed:', isHidden ? 'HIDDEN' : 'VISIBLE');
    
    if (isHidden) {
      this.backgroundStartTime = Date.now();
      this.activateBackgroundMode();
    } else {
      this.backgroundStartTime = null;
      this.activateForegroundMode();
    }
  }

  // Handle page hide
  handlePageHide() {
    console.log('📴 Page hide event - activating emergency background mode');
    this.activateBackgroundMode();
  }

  // Handle page show
  handlePageShow() {
    console.log('🔍 Page show event - activating foreground mode');
    this.activateForegroundMode();
  }

  // Handle before unload
  handleBeforeUnload(e) {
    if (this.isActive && this.room && this.room.state === 'connected') {
      e.preventDefault();
      e.returnValue = 'Streaming is active. Are you sure you want to leave?';
      return e.returnValue;
    }
  }

  // Activate background mode with aggressive protection
  activateBackgroundMode() {
    console.log('🛡️ Activating AGGRESSIVE background protection...');
    
    // Increase WebSocket keep-alive frequency
    this.startWebSocketKeepAlive(2000); // Every 2 seconds in background
    
    // Increase monitoring frequency
    this.startConnectionMonitoring(5000); // Every 5 seconds in background
  }

  // Activate foreground mode with normal protection
  activateForegroundMode() {
    console.log('✅ Activating normal foreground protection...');
    
    // Normal WebSocket keep-alive frequency
    this.startWebSocketKeepAlive(30000); // Every 30 seconds in foreground
    
    // Normal monitoring frequency
    this.startConnectionMonitoring(30000); // Every 30 seconds in foreground
    
    // Check if reconnection is needed
    if (this.room && this.room.state === 'disconnected') {
      console.log('🔄 Room disconnected, triggering reconnection...');
      this.triggerReconnection();
    }
  }

  // Start aggressive WebSocket keep-alive
  startWebSocketKeepAlive(interval = 30000) {
    if (this.intervals.websocketKeepAlive) {
      clearInterval(this.intervals.websocketKeepAlive);
    }

    this.intervals.websocketKeepAlive = setInterval(() => {
      if (!this.isActive || !this.room) return;

      try {
        // Access the WebSocket through LiveKit's engine
        const ws = this.room.engine?.client?.ws;
        
        if (ws && ws.readyState === WebSocket.OPEN) {
          // Send keep-alive ping
          const pingMessage = JSON.stringify({
            type: 'ping',
            timestamp: Date.now(),
            background: document.hidden,
            backgroundDuration: this.backgroundStartTime ? Date.now() - this.backgroundStartTime : 0
          });
          
          ws.send(pingMessage);
          console.log('💓 AGGRESSIVE WebSocket keep-alive sent (Background:', document.hidden, ')');
        } else {
          console.warn('⚠️ WebSocket not available or not open:', ws?.readyState);
        }
      } catch (error) {
        console.error('❌ Failed to send WebSocket keep-alive:', error);
      }
    }, interval);
  }

  // Start track health monitoring
  startTrackMonitoring() {
    if (this.intervals.trackMonitor) {
      clearInterval(this.intervals.trackMonitor);
    }

    this.intervals.trackMonitor = setInterval(() => {
      if (!this.isActive || !this.room) return;

      try {
        // Check local tracks
        const localParticipant = this.room.localParticipant;

        if (localParticipant && localParticipant.videoTracks && localParticipant.audioTracks) {
          // Check video tracks safely
          if (localParticipant.videoTracks.size > 0) {
            localParticipant.videoTracks.forEach((publication) => {
              const track = publication.track;
              if (track && track.mediaStreamTrack) {
                const readyState = track.mediaStreamTrack.readyState;
                if (readyState !== 'live') {
                  console.warn('⚠️ Video track not live:', readyState);
                  try {
                    track.restart();
                  } catch (error) {
                    console.error('❌ Failed to restart video track:', error);
                  }
                }
              }
            });
          }

          // Check audio tracks safely
          if (localParticipant.audioTracks.size > 0) {
            localParticipant.audioTracks.forEach((publication) => {
              const track = publication.track;
              if (track && track.mediaStreamTrack) {
                const readyState = track.mediaStreamTrack.readyState;
                if (readyState !== 'live') {
                  console.warn('⚠️ Audio track not live:', readyState);
                  try {
                    track.restart();
                  } catch (error) {
                    console.error('❌ Failed to restart audio track:', error);
                  }
                }
              }
            });
          }
        }
      } catch (error) {
        console.error('❌ Track monitoring error:', error);
      }
    }, 15000); // Every 15 seconds
  }

  // Start connection state monitoring
  startConnectionMonitoring(interval = 30000) {
    if (this.intervals.connectionMonitor) {
      clearInterval(this.intervals.connectionMonitor);
    }

    this.intervals.connectionMonitor = setInterval(() => {
      if (!this.isActive || !this.room) return;

      const state = this.room.state;
      console.log('🔍 Connection state check:', state, 'Background:', document.hidden);

      if (state === 'disconnected') {
        console.log('⚠️ Room disconnected, triggering reconnection...');
        this.triggerReconnection();
      }
    }, interval);
  }

  // Start visibility-based monitoring
  startVisibilityBasedMonitoring() {
    if (this.intervals.visibilityMonitor) {
      clearInterval(this.intervals.visibilityMonitor);
    }

    this.intervals.visibilityMonitor = setInterval(() => {
      if (!this.isActive) return;

      const now = Date.now();
      const timeSinceVisibilityChange = now - this.lastVisibilityChange;

      // If in background for more than 30 seconds, increase protection
      if (document.hidden && timeSinceVisibilityChange > 30000) {
        console.log('🚨 Long background session detected, increasing protection...');
        this.startWebSocketKeepAlive(1000); // Every 1 second for long background sessions
      }
    }, 10000); // Check every 10 seconds
  }

  // Trigger reconnection (to be implemented by the calling code)
  triggerReconnection() {
    console.log('🔄 Triggering reconnection from background protection...');
    
    // Dispatch custom event for the main component to handle
    window.dispatchEvent(new CustomEvent('livekit-reconnection-required', {
      detail: {
        reason: 'background-protection',
        timestamp: Date.now()
      }
    }));
  }
}

// Create singleton instance
const livekitBackgroundProtection = new LiveKitBackgroundProtection();

export default livekitBackgroundProtection;
