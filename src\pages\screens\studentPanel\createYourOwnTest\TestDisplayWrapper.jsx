import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import DynamicTestDisplay from './DynamicTestDisplay';

const TestDisplayWrapper = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading delay
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-indigo-100 flex items-center justify-center">
      {isLoading ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="flex flex-col items-center justify-center h-screen w-full bg-white/90 backdrop-blur-lg"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1.5, repeat: Infinity, ease: 'linear' }}
            className="w-16 h-16 border-4 border-[var(--color-student)] border-t-transparent rounded-full"
          />
          <motion.p
            className="mt-4 text-xl font-medium text-gray-700"
            initial={{ y: 10, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            Preparing Your Test...
          </motion.p>
        </motion.div>
      ) : (
        <DynamicTestDisplay />
      )}
    </div>
  );
};

export default TestDisplayWrapper;
