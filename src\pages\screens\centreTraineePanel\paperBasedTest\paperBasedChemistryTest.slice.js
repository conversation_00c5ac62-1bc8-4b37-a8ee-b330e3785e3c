import { PaperBasedChemistryTestApi } from '../../../../redux/api/api';

export const PaperBasedChemistryTestApiSlice = PaperBasedChemistryTestApi.injectEndpoints({
  endpoints: (builder) => ({
    paperBasedChemistryTestStartTest: builder.mutation({
      query: (body) => ({
        url: '/generate-paper',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Paper Based Chemistry Test Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['PaperBasedChemistryTest']
    })
  })
});

export const { usePaperBasedChemistryTestStartTestMutation } = PaperBasedChemistryTestApiSlice;
