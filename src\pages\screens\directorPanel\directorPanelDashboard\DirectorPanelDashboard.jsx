'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  useDirectorListCenterServiceQuery,
  useDirectorInboxServiceQuery
} from '../addCenter/addCenter.Slice';
import { useDirectorListKotaTeacherServiceQuery } from '../addKotaTeachers/addKotaTeacher.Slice';
import Toastify from '../../../../components/PopUp/Toastify';
import {
  FiUsers,
  FiInbox,
  FiMapPin,
  FiClock,
  FiAlertCircle,
  FiActivity,
  FiCheckCircle
} from 'react-icons/fi';
import { FaGraduationCap } from 'react-icons/fa6';

const Dashboard = () => {
  const {
    data: kotaTeachers = [],
    isLoading: teachersLoading,
    isError: teachersError,
    error: teachersErrorData
  } = useDirectorListKotaTeacherServiceQuery();
  const {
    data: centers = [],
    isLoading: centersLoading,
    isError: centersError,
    error: centersErrorData
  } = useDirectorListCenterServiceQuery();
  const {
    data: inboxRequests = [],
    isLoading: inboxLoading,
    isError: inboxError,
    error: inboxErrorData
  } = useDirectorInboxServiceQuery();
  const [res, setRes] = useState(null);

  // Safety check for arrays
  const teachersArray = Array.isArray(kotaTeachers) ? kotaTeachers : [];
  const centersArray = Array.isArray(centers) ? centers : [];
  const inboxArray = Array.isArray(inboxRequests) ? inboxRequests : [];

  // Sample recent activities (this could be fetched from an API in a real application)
  const recentActivities = [
    {
      id: 1,
      message: 'New student registration request received from Center A',
      time: '2 hours ago'
    },
    { id: 2, message: 'Teacher John Doe mapped to Center B', time: '4 hours ago' },
    { id: 3, message: 'Faculty approval completed for Jane Smith', time: '1 day ago' }
  ];

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: 'easeOut' }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.3 }
    },
    hover: {
      scale: 1.02,
      boxShadow: '0px 10px 30px rgba(0, 0, 0, 0.1)',
      transition: { duration: 0.2 }
    }
  };

  // Handle errors
  useEffect(() => {
    if (teachersError || centersError || inboxError) {
      const errorMessage =
        teachersErrorData?.message ||
        centersErrorData?.message ||
        inboxErrorData?.message ||
        'Failed to load dashboard data';
      setRes({ status: 'error', message: errorMessage });
    }
  }, [
    teachersError,
    centersError,
    inboxError,
    teachersErrorData,
    centersErrorData,
    inboxErrorData
  ]);

  if (teachersLoading || centersLoading || inboxLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8 flex items-center justify-center">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="inline-block w-12 h-12 border-4 border-gray-300 border-t-[var(--color-director)] rounded-full mb-4"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
          />
          <p className="text-gray-600 text-lg">Loading dashboard...</p>
        </motion.div>
      </div>
    );
  }

  if (teachersError || centersError || inboxError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8 flex items-center justify-center">
        <motion.div
          className="text-center bg-white rounded-3xl p-8 shadow-2xl"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <FiAlertCircle className="mx-auto text-6xl text-red-500 mb-4" />
          <h3 className="text-xl font-bold text-gray-900 mb-2">Error Loading Dashboard</h3>
          <p className="text-red-500">
            {teachersErrorData?.message ||
              centersErrorData?.message ||
              inboxErrorData?.message ||
              'Failed to load data'}
          </p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8">
      <Toastify res={res} resClear={() => setRes(null)} />

      <motion.div
        className="max-w-7xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header Section */}
        <motion.div className="text-center mb-8" variants={itemVariants}>
          <motion.div
            className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4"
            style={{
              background: `linear-gradient(135deg, var(--color-director), var(--color-director))`,
              boxShadow: '0px 10px 30px rgba(0, 0, 0, 0.1)'
            }}
            whileHover={{ scale: 1.1, rotate: 5 }}
          >
            <FiCheckCircle className="text-2xl" style={{ color: 'white' }} />
          </motion.div>
          <motion.h1 className="text-4xl font-bold mb-2" style={{ color: 'var(--color-director)' }}>
            Director Dashboard
          </motion.h1>
          <motion.p className="text-gray-600 text-lg">
            Overview of centers, teachers, and pending requests
          </motion.p>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Total Centers */}
          <motion.div
            className="bg-white rounded-3xl shadow-lg p-6"
            variants={cardVariants}
            whileHover="hover"
            style={{
              background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
              border: '1px solid rgba(0, 0, 0, 0.05)'
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Total Centers</h3>
                <p className="text-3xl font-bold" style={{ color: 'var(--color-director)' }}>
                  {centersArray.length}
                </p>
                <p className="text-gray-600 text-sm mt-1">Registered centers</p>
              </div>
              <FiMapPin className="text-4xl text-gray-300" />
            </div>
          </motion.div>

          {/* Total Teachers */}
          <motion.div
            className="bg-white rounded-3xl shadow-lg p-6"
            variants={cardVariants}
            whileHover="hover"
            style={{
              background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
              border: '1px solid rgba(0, 0, 0, 0.05)'
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Total Teachers</h3>
                <p className="text-3xl font-bold" style={{ color: 'var(--color-director)' }}>
                  {teachersArray.length}
                </p>
                <p className="text-gray-600 text-sm mt-1">Kota teachers</p>
              </div>
              <FaGraduationCap className="text-4xl text-gray-300" />
            </div>
          </motion.div>

          {/* Pending Requests */}
          <motion.div
            className="bg-white rounded-3xl shadow-lg p-6"
            variants={cardVariants}
            whileHover="hover"
            style={{
              background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
              border: '1px solid rgba(0, 0, 0, 0.05)'
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Pending Requests</h3>
                <p className="text-3xl font-bold" style={{ color: 'var(--color-director)' }}>
                  {inboxArray.length}
                </p>
                <p className="text-gray-600 text-sm mt-1">Approval requests</p>
              </div>
              <FiInbox className="text-4xl text-gray-300" />
            </div>
          </motion.div>
        </motion.div>

        {/* Recent Activities Section */}
        <motion.div
          className="bg-white rounded-3xl shadow-2xl overflow-hidden"
          variants={itemVariants}
          style={{
            background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
            border: '1px solid rgba(0, 0, 0, 0.05)'
          }}
        >
          <div
            className="px-8 py-6 text-white"
            style={{
              background: `linear-gradient(135deg, var(--color-director), var(--color-director)dd)`
            }}
          >
            <h3 className="text-3xl font-bold, text-black">Recent Activities</h3>
            <p className="text-black text-opacity-80 mt-1">Latest updates and actions</p>
          </div>
          <div className="p-8">
            {recentActivities.length === 0 ? (
              <div className="text-center py-12">
                <FiActivity className="mx-auto text-6xl text-gray-300 mb-4" />
                <p className="text-gray-600 text-lg">No recent activities</p>
              </div>
            ) : (
              <motion.div className="space-y-4" variants={containerVariants}>
                <AnimatePresence>
                  {recentActivities.map((activity, index) => (
                    <motion.div
                      key={activity.id}
                      className="flex items-center border-b border-gray-100 py-3"
                      variants={itemVariants}
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                      transition={{ delay: index * 0.1 }}
                    >
                      <FiActivity className="text-2xl text-gray-400 mr-4" />
                      <div className="flex-1">
                        <p className="text-gray-900">{activity.message}</p>
                        <p className="text-sm text-gray-500">{activity.time}</p>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </motion.div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Dashboard;
