import { motion, AnimatePresence } from 'framer-motion';
import { Lightbulb, Globe, BrainCircuit, BookOpen, Users, ChevronLeft } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router';

const AboutUs = () => {
  const navigate = useNavigate();
  // Auto-scrolling vision banner state
  const [currentVisionIndex, setCurrentVisionIndex] = useState(0);
  const visionItems = [
    {
      icon: <Lightbulb className="text-amber-500" />,
      title: 'Empowering Dreams',
      description:
        'Enabling every Indian student to achieve their aspirations through personalized learning'
    },
    {
      icon: <Globe className="text-emerald-500" />,
      title: 'Bridging Divides',
      description: 'Closing the educational gap between rural and urban India with technology'
    },
    {
      icon: <BrainCircuit className="text-blue-500" />,
      title: 'AI-Powered Learning',
      description: "Harnessing artificial intelligence to adapt to each student's needs"
    },
    {
      icon: <Users className="text-purple-500" />,
      title: 'Community Building',
      description: 'Fostering a network of passionate learners, educators, and mentors'
    },
    {
      icon: <BookOpen className="text-rose-500" />,
      title: 'Accessible Education',
      description: 'Making quality learning affordable and inclusive for all economic backgrounds'
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentVisionIndex((prev) => (prev + 1) % visionItems.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  // Mission statement auto-scroll
  const missionLines = [
    'To unlock the potential of every Indian student',
    'Through AI-powered, personalized learning experiences',
    'That adapt to individual needs',
    'Supported by expert educators',
    'Accessible across all economic backgrounds'
  ];
  const [currentMissionLine, setCurrentMissionLine] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentMissionLine((prev) => (prev + 1) % missionLines.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen overflow-hidden bg-stone-50 relative">
      {/* Back Button */}
      <motion.button
        whileHover={{ x: -5 }}
        whileTap={{ scale: 0.95 }}
        className="absolute top-6 left-6 flex items-center hover:cursor-pointer hover:text-black text-blue-600 z-10"
        onClick={() => navigate('/')}
      >
        <ChevronLeft className="mr-1 " />
        Back
      </motion.button>

      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-blue-50 to-stone-100">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              About <span className="text-[var(--color-student)]">Sasthra</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Revolutionizing education through AI-driven personalization
            </p>
          </motion.div>

          <div className="flex flex-col lg:flex-row items-center gap-12">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2, duration: 0.8 }}
              className="lg:w-1/2"
            >
              <img
                src="https://images.shiksha.com/mediadata/images/articles/1738065245phpLxdz48.jpeg"
                alt="Sasthra learning platform"
                className="rounded-xl shadow-2xl border-8 border-white"
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              className="lg:w-1/2"
            >
              <h2 className="text-3xl font-bold text-[var(--color-teacher)] mb-6">Our Mission</h2>

              {/* Auto-scrolling mission banner */}
              <motion.div className="bg-[var(--color-student)] p-8 rounded-xl shadow-lg mb-8 border-l-4 border-blue-500 h-25 flex items-center justify-center">
                <AnimatePresence mode="wait">
                  <motion.p
                    key={currentMissionLine}
                    initial={{ y: 50, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: -50, opacity: 0 }}
                    transition={{ duration: 0.5 }}
                    className="text-lg text-white text-center"
                  >
                    {missionLines[currentMissionLine]}
                  </motion.p>
                </AnimatePresence>
              </motion.div>

              <div className="flex items-center space-x-4">
                <motion.img
                  whileHover={{ rotate: 10 }}
                  src="https://static.vecteezy.com/system/resources/thumbnails/047/818/573/small/stethoscope-symbol-healthe-logo-silhouette-on-white-background-vector.jpg"
                  alt="Indian student learning"
                  className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-md"
                />
                <motion.img
                  whileHover={{ rotate: -10 }}
                  src="https://i.etsystatic.com/18311546/r/il/5dfbc3/**********/il_600x600.**********_diz9.jpg"
                  alt="AI technology"
                  className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-md"
                />
                <motion.img
                  whileHover={{ rotate: 5 }}
                  src="https://easy-peasy.ai/cdn-cgi/image/quality=80,format=auto,width=700/https://fdczvxmwwjwpwbeeqcth.supabase.co/storage/v1/object/public/images/84b0f8e0-def7-4267-a4ce-93c6f51ade9a/479b8d49-5f00-46c6-b6ee-97aacdbb7bde.png"
                  alt="Mentorship"
                  className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-md"
                />
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Vision Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Vision</h2>
            <div className="w-24 h-1 bg-[var(--color-student)] mx-auto"></div>
          </motion.div>

          {/* Auto-scrolling vision banner */}

          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="relative h-48 overflow-hidden bg-[var(--color-teacher)] rounded-2xl shadow-2xl mb-12"
          >
            {/* Animated background elements */}
            <motion.div
              className="absolute inset-0 opacity-10"
              animate={{
                backgroundPosition: ['0% 0%', '100% 100%']
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                ease: 'linear'
              }}
              style={{
                backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',
                backgroundSize: '40px 40px'
              }}
            />

            {/* Floating particles */}
            {[...Array(8)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute rounded-full bg-white/20"
                initial={{
                  x: Math.random() * 100,
                  y: Math.random() * 100,
                  width: Math.random() * 10 + 5,
                  height: Math.random() * 10 + 5
                }}
                animate={{
                  y: [0, 100, 0],
                  x: [0, 50, 0],
                  opacity: [0.2, 0.8, 0.2]
                }}
                transition={{
                  duration: Math.random() * 10 + 10,
                  repeat: Infinity,
                  repeatType: 'reverse'
                }}
              />
            ))}

            <div className="relative h-full flex items-center justify-center">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentVisionIndex}
                  initial={{ x: 100, opacity: 0, scale: 0.8 }}
                  animate={{ x: 0, opacity: 1, scale: 1 }}
                  exit={{ x: -100, opacity: 0, scale: 0.8 }}
                  transition={{
                    type: 'spring',
                    stiffness: 100,
                    damping: 10
                  }}
                  className="flex flex-col md:flex-row items-center p-6 max-w-4xl mx-auto"
                >
                  {/* Icon with floating animation */}
                  <motion.div
                    whileHover={{ scale: 1.2, rotate: 10 }}
                    className="mb-4 md:mb-0 md:mr-8 p-4 bg-white/20 rounded-full backdrop-blur-sm"
                  >
                    <motion.div
                      animate={{
                        rotate: [0, 10, -10, 0],
                        y: [0, 5, -5, 0]
                      }}
                      transition={{
                        duration: 8,
                        repeat: Infinity,
                        ease: 'easeInOut'
                      }}
                    >
                      {visionItems[currentVisionIndex].icon}
                    </motion.div>
                  </motion.div>

                  <div className="text-center md:text-left">
                    <motion.h3
                      className="text-2xl md:text-3xl font-bold text-white mb-2"
                      initial={{ y: 20 }}
                      animate={{ y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      {visionItems[currentVisionIndex].title}
                    </motion.h3>
                    <motion.p
                      className="text-white/90 text-lg"
                      initial={{ y: 20 }}
                      animate={{ y: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      {visionItems[currentVisionIndex].description}
                    </motion.p>
                  </div>
                </motion.div>
              </AnimatePresence>

              {/* Progress indicators */}
              <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
                {visionItems.map((_, index) => (
                  <motion.button
                    key={index}
                    onClick={() => setCurrentVisionIndex(index)}
                    className={`w-3 h-3 rounded-full ${currentVisionIndex === index ? 'bg-white' : 'bg-white/30'}`}
                    whileHover={{ scale: 1.2 }}
                    whileTap={{ scale: 0.9 }}
                  />
                ))}
              </div>
            </div>
          </motion.div>

          {/* Static vision cards */}
          <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-6">
            {visionItems.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -10 }}
                className="bg-stone-50 p-6 rounded-xl shadow-md border-t-4 border-blue-400"
              >
                <div className="text-4xl mb-4">{item.icon}</div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">{item.title}</h3>
                <p className="text-gray-600">{item.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Impact Section */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-emerald-50">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="flex flex-col lg:flex-row items-center gap-12"
          >
            <div className="lg:w-1/2">
              <motion.img
                whileHover={{ scale: 1.02 }}
                src="https://img.freepik.com/premium-photo/indian-teachers-indian-students-indian-teachers-day_978786-46992.jpg?semt=ais_items_boosted&w=740"
                alt="Sasthra impact"
                className="rounded-xl shadow-2xl"
              />
            </div>

            <div className="lg:w-1/2">
              <h2 className="text-3xl font-bold text-[var(--color-teacher)] mb-6">
                Transforming Indian Education
              </h2>

              <motion.div
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                viewport={{ once: true }}
                transition={{ delay: 0.2 }}
                className="space-y-6"
              >
                <div className="flex items-start">
                  <div className="bg-blue-100 p-3 rounded-full mr-4">
                    <BookOpen className="text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800">
                      Personalized Learning Paths
                    </h3>
                    <p className="text-gray-600">
                      Our AI creates custom study plans based on each student's strengths and
                      weaknesses
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-emerald-100 p-3 rounded-full mr-4">
                    <Globe className="text-emerald-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800">Rural Reach Initiative</h3>
                    <p className="text-gray-600">
                      Special programs designed for students in remote areas with limited resources
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="bg-amber-100 p-3 rounded-full mr-4">
                    <Users className="text-amber-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800">Mentor Network</h3>
                    <p className="text-gray-600">
                      Connecting students with top educators and industry professionals across India
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default AboutUs;
