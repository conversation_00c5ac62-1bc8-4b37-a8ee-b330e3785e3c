import { createSlice } from '@reduxjs/toolkit';
import { directorA<PERSON> } from '../../../../redux/api/api'; // Adjust path based on your structure

const initialState = {
  subjects: [],
  selectedSubject: null
};

// Extend directorApi with endpoints
export const addSubjectSlice = directorApi.injectEndpoints({
  endpoints: (builder) => ({
    // Create a new subject
    addSubject: builder.mutation({
      query: (body) => ({
        url: '/add-subject',
        method: 'POST',
        body
      }),
      transformResponse: (response) => {
        console.log('Add Subject Response:', response);
        return {
          ...response,
          subject_id: response.subject_id // Explicitly map subject_id
        };
      },
      transformErrorResponse: ({ status, data }) => {
        console.log('Add Subject Error:', { status, data });
        return { status, data };
      },
      invalidatesTags: ['Subjects']
    }),
    // Update an existing subject
    updateSubject: builder.mutation({
      query: ({ subjectId, body }) => ({
        url: '/update-subject',
        method: 'PUT',
        body: { subject_id: subjectId, ...body } // Ensure subject_id is in body
      }),
      transformResponse: (response) => {
        console.log('Update Subject Response:', response);
        return {
          ...response,
          subject_id: response.subject_id || subjectId // Preserve subject_id
        };
      },
      transformErrorResponse: ({ status, data }) => {
        console.log('Update Subject Error:', { status, data });
        return { status, data };
      },
      invalidatesTags: ['Subjects']
    }),
    // List all subjects
    listSubjects: builder.query({
      query: (params) => ({
        url: '/list-subjects',
        method: 'GET',
        params
      }),
      transformResponse: (response) => {
        console.log('List Subjects Response:', response);
        return (
          response.subjects.map((subject) => ({
            ...subject,
            subject_id: subject.subject_id || subject.id // Ensure subject_id is available
          })) || []
        );
      },
      transformErrorResponse: ({ status, data }) => {
        console.log('List Subjects Error:', { status, data });
        return { status, data };
      },
      providesTags: ['Subjects']
    }),
    // Delete a subject
    deleteSubject: builder.mutation({
      query: (subjectId) => ({
        url: '/delete-subject',
        method: 'DELETE',
        body: { subject_id: subjectId } // Ensure subject_id is in body
      }),
      transformResponse: (response) => {
        console.log('Delete Subject Response:', response);
        return response.message || 'Subject deleted';
      },
      transformErrorResponse: ({ status, data }) => {
        console.log('Delete Subject Error:', { status, data });
        return { status, data };
      },
      invalidatesTags: ['Subjects']
    })
  })
});

// Create Redux slice
const subjectSlice = createSlice({
  name: 'subjects',
  initialState,
  reducers: {
    setSubjects: (state, action) => {
      state.subjects = action.payload;
    },
    setSelectedSubject: (state, action) => {
      state.selectedSubject = action.payload;
    }
  }
});

export const { setSubjects, setSelectedSubject } = subjectSlice.actions;
export default subjectSlice.reducer;

// Export RTK Query hooks
export const {
  useAddSubjectMutation,
  useUpdateSubjectMutation,
  useListSubjectsQuery,
  useDeleteSubjectMutation
} = addSubjectSlice;
