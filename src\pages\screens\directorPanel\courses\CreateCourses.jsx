'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useDispatch } from 'react-redux';
import {
  useCreateCoursesServiceMutation,
  useUpdateCoursesServiceMutation,
  useDeleteCoursesServiceMutation,
  useListCoursesServiceQuery
} from './addCourses.Slice';
import Toastify from '../../../../components/PopUp/Toastify';
import {
  FiBook,
  FiEdit,
  FiTrash2,
  FiX,
  FiBookOpen,
  FiFileText,
  FiEye,
  FiRefreshCw,
  FiPlus,
  FiSave,
  FiAlertCircle,
  FiUsers
} from 'react-icons/fi';
import { FaGraduationCap } from 'react-icons/fa6';

const CreateCourses = () => {
  const dispatch = useDispatch();
  const {
    data: coursesData,
    isLoading: isFetchingCourses,
    isError: isFetchError,
    error: fetchError,
    refetch
  } = useListCoursesServiceQuery();

  const [createCourse] = useCreateCoursesServiceMutation();
  const [updateCourse] = useUpdateCoursesServiceMutation();
  const [deleteCourse] = useDeleteCoursesServiceMutation();

  const [createFormData, setCreateFormData] = useState({ course_name: '', description: '' });
  const [updateFormData, setUpdateFormData] = useState({
    course_id: '',
    course_name: '',
    description: ''
  });
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [res, setRes] = useState(null);
  const [loadingCourseId, setLoadingCourseId] = useState(null);
  const [showDescription, setShowDescription] = useState(false);
  const [showAddForm, setShowAddForm] = useState(true);
  const [editMode, setEditMode] = useState(false);

  // Validation functions
  const validateCourseName = (name) =>
    !name
      ? 'Course name is required'
      : name.length < 3
        ? 'Course name must be at least 3 characters long'
        : '';

  const validateDescription = (description) =>
    !description
      ? 'Description is required'
      : description.length < 10
        ? 'Description must be at least 10 characters long'
        : '';

  const [createErrors, setCreateErrors] = useState({ course_name: '', description: '' });
  const [updateErrors, setUpdateErrors] = useState({ course_name: '', description: '' });

  const handleCreateChange = (e) => {
    const { name, value } = e.target;
    setCreateFormData((prev) => ({ ...prev, [name]: value }));
    setCreateErrors((prev) => ({
      ...prev,
      [name]: name === 'course_name' ? validateCourseName(value) : validateDescription(value)
    }));
  };

  const handleUpdateChange = (e) => {
    const { name, value } = e.target;
    setUpdateFormData((prev) => ({ ...prev, [name]: value }));
    setUpdateErrors((prev) => ({
      ...prev,
      [name]: name === 'course_name' ? validateCourseName(value) : validateDescription(value)
    }));
  };

  const validateForm = (formData) => {
    const errors = {
      course_name: validateCourseName(formData.course_name),
      description: validateDescription(formData.description)
    };
    return { errors, isValid: Object.values(errors).every((error) => !error) };
  };

  const handleCreateSubmit = async (e) => {
    e.preventDefault();
    const { errors, isValid } = validateForm(createFormData);
    setCreateErrors(errors);

    if (!isValid) {
      setRes({ status: 'error', message: 'Please fix the errors in the form.' });
      return;
    }

    setLoadingCourseId('create');
    try {
      const response = await createCourse(createFormData).unwrap();
      console.log('Create Course Success:', response);
      setCreateFormData({ course_name: '', description: '' });
      setCreateErrors({ course_name: '', description: '' });
      setRes({ status: 'success', message: 'Course created successfully!' });
    } catch (err) {
      console.log('Create Course Error:', err);
      setRes({ status: 'error', message: err?.message || 'Failed to create course.' });
    } finally {
      setLoadingCourseId(null);
    }
  };

  const handleUpdateSubmit = async (e) => {
    e.preventDefault();
    const { errors, isValid } = validateForm(updateFormData);
    setUpdateErrors(errors);

    if (!isValid) {
      setRes({ status: 'error', message: 'Please fix the errors in the form.' });
      return;
    }

    const { course_id, course_name, description } = updateFormData;

    if (!course_id) {
      setRes({ status: 'error', message: 'Course ID is missing.' });
      return;
    }

    setLoadingCourseId(course_id);
    try {
      const response = await updateCourse({ course_id, course_name, description }).unwrap();
      console.log('Update Course Success:', response);
      setUpdateFormData({ course_id: '', course_name: '', description: '' });
      setUpdateErrors({ course_name: '', description: '' });
      setSelectedCourse(null);
      setEditMode(false);
      setRes({ status: 'success', message: 'Course updated successfully!' });
    } catch (err) {
      console.log('Update Course Error:', err);
      setRes({ status: 'error', message: err?.message || 'Failed to update course.' });
    } finally {
      setLoadingCourseId(null);
    }
  };

  const handleDelete = async (course_id) => {
    console.log('Attempting to delete course with ID:', course_id);
    if (!course_id) {
      setRes({ status: 'error', message: 'Course ID is missing.' });
      return;
    }

    setLoadingCourseId(course_id);
    try {
      const response = await deleteCourse({ course_id }).unwrap();
      console.log('Delete Course Success:', response);
      setRes({ status: 'success', message: 'Course deleted successfully!' });
    } catch (err) {
      console.log('Delete Course Error:', err);
      setRes({ status: 'error', message: err?.message || 'Failed to delete course.' });
    } finally {
      setLoadingCourseId(null);
    }
  };

  const selectCourseForUpdate = (course) => {
    console.log('Selected Course for Update:', course);
    if (!course.course_id) {
      setRes({ status: 'error', message: 'Selected course has no valid ID.' });
      return;
    }

    setSelectedCourse(course);
    setUpdateFormData({
      course_id: course.course_id,
      course_name: course.course_name,
      description: course.description || ''
    });
    setUpdateErrors({ course_name: '', description: '' });
    setEditMode(true);
    setShowAddForm(false);
  };

  const cancelUpdate = () => {
    setSelectedCourse(null);
    setUpdateFormData({ course_id: '', course_name: '', description: '' });
    setUpdateErrors({ course_name: '', description: '' });
    setEditMode(false);
    setShowAddForm(true);
  };

  const toggleFormView = () => {
    setShowAddForm(!showAddForm);
    if (editMode) {
      cancelUpdate();
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: 'easeOut', staggerChildren: 0.1 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 15 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.3, ease: 'easeOut' } }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.98 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.2 }
    },
    hover: {
      scale: 1.01,
      boxShadow: '0px 8px 25px rgba(125, 30, 28, 0.08)',
      transition: { duration: 0.2 }
    }
  };

  const buttonVariants = {
    hover: { scale: 1.02, transition: { duration: 0.2 } },
    tap: { scale: 0.98 }
  };

  const coursesArray = Array.isArray(coursesData) ? coursesData : [];

  if (isFetchError) {
    return (
      <div className="min-h-screen bg-gray-50 p-4 md:p-8 flex items-center justify-center">
        <motion.div
          className="text-center bg-white rounded-lg p-8 shadow-sm border border-red-100 max-w-md"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <FiAlertCircle className="mx-auto text-4xl text-red-500 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Courses</h3>
          <p className="text-red-600 text-sm mb-6">
            {fetchError?.message || 'Failed to load courses'}
          </p>
          <motion.button
            onClick={refetch}
            className="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md flex items-center justify-center space-x-2 mx-auto text-sm font-medium transition-colors"
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            <FiRefreshCw size={16} />
            <span>Retry</span>
          </motion.button>
        </motion.div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen bg-gray-50 p-4 md:p-8"
      style={{
        '--color-director': '#7d1e1c',
        '--color-director-light': 'rgba(125, 30, 28, 0.05)',
        '--color-director-medium': 'rgba(125, 30, 28, 0.1)',
        '--color-director-dark': 'rgba(125, 30, 28, 0.15)',
        '--color-director-border': 'rgba(125, 30, 28, 0.2)',
        '--color-director-hover': 'rgba(125, 30, 28, 0.9)'
      }}
    >
      <Toastify res={res} resClear={() => setRes(null)} />

      <motion.div
        className="max-w-6xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Minimal Header Section */}
        <motion.div className="text-center mb-8" variants={itemVariants}>
          <motion.div
            className="inline-flex items-center justify-center w-12 h-12 rounded-lg mb-4"
            style={{ backgroundColor: 'var(--color-director-medium)' }}
            whileHover={{ scale: 1.05 }}
          >
            <FaGraduationCap className="text-xl" style={{ color: 'var(--color-director)' }} />
          </motion.div>
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">Course Management</h1>
          <p className="text-gray-600 text-sm max-w-md mx-auto">
            Create and manage your educational courses
          </p>

          {/* Subtle Toggle Button */}
        </motion.div>

        {/* Forms Section */}
        <AnimatePresence mode="wait">
          {(showAddForm || editMode) && (
            <motion.div
              className="mb-8"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              {/* Create Course Form */}
              {showAddForm && !editMode && (
                <motion.div
                  className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden max-w-2xl mx-auto"
                  variants={itemVariants}
                  layout
                >
                  <div
                    className="px-6 py-4 border-b"
                    style={{
                      backgroundColor: 'var(--color-director-light)',
                      borderColor: 'var(--color-director-border)'
                    }}
                  >
                    <div className="flex items-center">
                      <FiPlus className="mr-3 text-lg" style={{ color: 'var(--color-director)' }} />
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">Create New Course</h3>
                        <p className="text-gray-600 text-sm mt-1">
                          Add a new course to your curriculum
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="p-6">
                    <form onSubmit={handleCreateSubmit} className="space-y-5">
                      {/* Course Name Input */}
                      <motion.div className="space-y-2" variants={itemVariants}>
                        <label className="flex items-center text-sm font-medium text-gray-700">
                          <FiBookOpen
                            className="mr-2 text-sm"
                            style={{ color: 'var(--color-director)' }}
                          />
                          Course Name
                        </label>
                        <div className="relative">
                          <motion.input
                            type="text"
                            name="course_name"
                            placeholder="Enter course name"
                            value={createFormData.course_name}
                            onChange={handleCreateChange}
                            className={`w-full border rounded-md px-3 py-2 text-sm focus:outline-none transition-all duration-200 ${
                              createErrors.course_name
                                ? 'border-red-300 bg-red-50 focus:border-red-400'
                                : 'border-gray-300 focus:border-[var(--color-director)] bg-white'
                            }`}
                            required
                            whileFocus={{ scale: 1.005 }}
                          />
                          {createErrors.course_name && (
                            <FiAlertCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 text-red-400 text-sm" />
                          )}
                        </div>
                        {createErrors.course_name && (
                          <motion.p
                            className="text-red-600 text-xs flex items-center mt-1"
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                          >
                            <FiAlertCircle className="mr-1" size={12} />
                            {createErrors.course_name}
                          </motion.p>
                        )}
                      </motion.div>

                      {/* Description Input */}
                      <motion.div className="space-y-2" variants={itemVariants}>
                        <label className="flex items-center text-sm font-medium text-gray-700">
                          <FiFileText
                            className="mr-2 text-sm"
                            style={{ color: 'var(--color-director)' }}
                          />
                          Course Description
                        </label>
                        <div className="relative">
                          <motion.textarea
                            name="description"
                            placeholder="Enter course description"
                            value={createFormData.description}
                            onChange={handleCreateChange}
                            className={`w-full border rounded-md px-3 py-2 text-sm focus:outline-none transition-all duration-200 resize-none ${
                              createErrors.description
                                ? 'border-red-300 bg-red-50 focus:border-red-400'
                                : 'border-gray-300 focus:border-[var(--color-director)] bg-white'
                            }`}
                            rows="3"
                            required
                            whileFocus={{ scale: 1.005 }}
                          />
                          {createErrors.description && (
                            <FiAlertCircle className="absolute right-3 top-3 text-red-400 text-sm" />
                          )}
                        </div>
                        {createErrors.description && (
                          <motion.p
                            className="text-red-600 text-xs flex items-center mt-1"
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                          >
                            <FiAlertCircle className="mr-1" size={12} />
                            {createErrors.description}
                          </motion.p>
                        )}
                      </motion.div>

                      {/* Submit Button */}
                      <motion.div className="pt-4" variants={itemVariants}>
                        <motion.button
                          type="submit"
                          disabled={loadingCourseId === 'create'}
                          className="w-full text-white px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                          style={{
                            backgroundColor:
                              loadingCourseId === 'create'
                                ? 'var(--color-director-border)'
                                : 'var(--color-director)'
                          }}
                          variants={buttonVariants}
                          whileHover="hover"
                          whileTap="tap"
                        >
                          {loadingCourseId === 'create' ? (
                            <>
                              <motion.div
                                className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                                animate={{ rotate: 360 }}
                                transition={{
                                  duration: 1,
                                  repeat: Number.POSITIVE_INFINITY,
                                  ease: 'linear'
                                }}
                              />
                              <span>Creating...</span>
                            </>
                          ) : (
                            <>
                              <FiPlus size={16} />
                              <span>Create Course</span>
                            </>
                          )}
                        </motion.button>
                      </motion.div>
                    </form>
                  </div>
                </motion.div>
              )}

              {/* Update Course Form */}
              {editMode && selectedCourse && (
                <motion.div
                  className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden max-w-2xl mx-auto"
                  variants={itemVariants}
                  layout
                >
                  <div
                    className="px-6 py-4 border-b"
                    style={{
                      backgroundColor: 'var(--color-director-light)',
                      borderColor: 'var(--color-director-border)'
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <FiEdit
                          className="mr-3 text-lg"
                          style={{ color: 'var(--color-director)' }}
                        />
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">Edit Course</h3>
                          <p className="text-gray-600 text-sm mt-1">
                            Modify: {selectedCourse.course_name}
                          </p>
                        </div>
                      </div>
                      <motion.button
                        onClick={cancelUpdate}
                        className="p-2 hover:bg-gray-100 rounded-md transition-colors"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <FiX className="text-lg text-gray-500" />
                      </motion.button>
                    </div>
                  </div>

                  <div className="p-6">
                    <form onSubmit={handleUpdateSubmit} className="space-y-5">
                      {/* Course Name Input */}
                      <motion.div className="space-y-2" variants={itemVariants}>
                        <label className="flex items-center text-sm font-medium text-gray-700">
                          <FiBookOpen
                            className="mr-2 text-sm"
                            style={{ color: 'var(--color-director)' }}
                          />
                          Course Name
                        </label>
                        <div className="relative">
                          <motion.input
                            type="text"
                            name="course_name"
                            placeholder="Enter course name"
                            value={updateFormData.course_name}
                            onChange={handleUpdateChange}
                            className={`w-full border rounded-md px-3 py-2 text-sm focus:outline-none transition-all duration-200 ${
                              updateErrors.course_name
                                ? 'border-red-300 bg-red-50 focus:border-red-400'
                                : 'border-gray-300 focus:border-[var(--color-director)] bg-white'
                            }`}
                            required
                            whileFocus={{ scale: 1.005 }}
                          />
                        </div>
                        {updateErrors.course_name && (
                          <motion.p
                            className="text-red-600 text-xs flex items-center mt-1"
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                          >
                            <FiAlertCircle className="mr-1" size={12} />
                            {updateErrors.course_name}
                          </motion.p>
                        )}
                      </motion.div>

                      {/* Description Input */}
                      <motion.div className="space-y-2" variants={itemVariants}>
                        <label className="flex items-center text-sm font-medium text-gray-700">
                          <FiFileText
                            className="mr-2 text-sm"
                            style={{ color: 'var(--color-director)' }}
                          />
                          Course Description
                        </label>
                        <div className="relative">
                          <motion.textarea
                            name="description"
                            placeholder="Enter course description"
                            value={updateFormData.description}
                            onChange={handleUpdateChange}
                            className={`w-full border rounded-md px-3 py-2 text-sm focus:outline-none transition-all duration-200 resize-none ${
                              updateErrors.description
                                ? 'border-red-300 bg-red-50 focus:border-red-400'
                                : 'border-gray-300 focus:border-[var(--color-director)] bg-white'
                            }`}
                            rows="3"
                            required
                            whileFocus={{ scale: 1.005 }}
                          />
                        </div>
                        {updateErrors.description && (
                          <motion.p
                            className="text-red-600 text-xs flex items-center mt-1"
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                          >
                            <FiAlertCircle className="mr-1" size={12} />
                            {updateErrors.description}
                          </motion.p>
                        )}
                      </motion.div>

                      {/* Action Buttons */}
                      <motion.div className="pt-4 flex gap-3" variants={itemVariants}>
                        <motion.button
                          type="submit"
                          disabled={loadingCourseId === updateFormData.course_id}
                          className="flex-1 text-white px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                          style={{
                            backgroundColor:
                              loadingCourseId === updateFormData.course_id
                                ? 'var(--color-director-border)'
                                : 'var(--color-director)'
                          }}
                          variants={buttonVariants}
                          whileHover="hover"
                          whileTap="tap"
                        >
                          {loadingCourseId === updateFormData.course_id ? (
                            <>
                              <motion.div
                                className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                                animate={{ rotate: 360 }}
                                transition={{
                                  duration: 1,
                                  repeat: Number.POSITIVE_INFINITY,
                                  ease: 'linear'
                                }}
                              />
                              <span>Updating...</span>
                            </>
                          ) : (
                            <>
                              <FiSave size={16} />
                              <span>Save Changes</span>
                            </>
                          )}
                        </motion.button>

                        <motion.button
                          type="button"
                          onClick={cancelUpdate}
                          className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md font-medium text-sm transition-all duration-200 flex items-center justify-center space-x-2"
                          variants={buttonVariants}
                          whileHover="hover"
                          whileTap="tap"
                        >
                          <FiX size={16} />
                          <span>Cancel</span>
                        </motion.button>
                      </motion.div>
                    </form>
                  </div>
                </motion.div>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Course List */}
        <motion.div
          className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
          variants={itemVariants}
        >
          <div
            className="px-6 py-4 border-b"
            style={{
              backgroundColor: 'var(--color-director-light)',
              borderColor: 'var(--color-director-border)'
            }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FaGraduationCap
                  className="mr-3 text-lg"
                  style={{ color: 'var(--color-director)' }}
                />
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Course Directory</h3>
                  <p className="text-gray-600 text-sm mt-1">
                    {coursesArray.length} course{coursesArray.length !== 1 ? 's' : ''} available
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-semibold" style={{ color: 'var(--color-director)' }}>
                  {coursesArray.length}
                </div>
                <div className="text-xs text-gray-500">Total</div>
              </div>
            </div>
          </div>

          <div className="p-6">
            {isFetchingCourses ? (
              <div className="text-center py-12">
                <motion.div
                  className="inline-block w-8 h-8 border-2 border-gray-300 rounded-full mb-4"
                  style={{ borderTopColor: 'var(--color-director)' }}
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
                />
                <h3 className="text-lg font-medium text-gray-700 mb-1">Loading Courses</h3>
                <p className="text-gray-500 text-sm">Please wait...</p>
              </div>
            ) : coursesArray.length === 0 ? (
              <div className="text-center py-12">
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <FaGraduationCap className="mx-auto text-5xl text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Courses Found</h3>
                  <p className="text-gray-600 text-sm mb-6 max-w-sm mx-auto">
                    Start building your curriculum by creating your first course.
                  </p>
                  <motion.button
                    onClick={() => setShowAddForm(true)}
                    className="inline-flex items-center px-4 py-2 text-white rounded-md font-medium text-sm transition-all duration-200"
                    style={{ backgroundColor: 'var(--color-director)' }}
                    variants={buttonVariants}
                    whileHover="hover"
                    whileTap="tap"
                  >
                    <FiPlus className="mr-2" size={16} />
                    Create First Course
                  </motion.button>
                </motion.div>
              </div>
            ) : (
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                variants={containerVariants}
              >
                <AnimatePresence>
                  {coursesArray.map((course, index) => (
                    <motion.div
                      key={course.course_id || `course-${Math.random()}`}
                      className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200"
                      variants={cardVariants}
                      whileHover="hover"
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                      transition={{ delay: index * 0.05 }}
                    >
                      {/* Card Header */}
                      <div
                        className="p-4 border-b"
                        style={{
                          backgroundColor: 'var(--color-director-light)',
                          borderColor: 'var(--color-director-border)'
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div
                              className="w-8 h-8 rounded-md flex items-center justify-center mr-3"
                              style={{ backgroundColor: 'var(--color-director-medium)' }}
                            >
                              <FiBook
                                className="text-sm"
                                style={{ color: 'var(--color-director)' }}
                              />
                            </div>
                            <div>
                              <h4 className="font-medium text-gray-900 text-sm truncate">
                                {course.course_name || 'Unnamed Course'}
                              </h4>
                              <p className="text-gray-500 text-xs">
                                ID: {course.course_id || 'N/A'}
                              </p>
                            </div>
                          </div>
                          <div className="text-gray-400">
                            <FiUsers size={16} />
                          </div>
                        </div>
                      </div>

                      {/* Card Body */}
                      <div className="p-4">
                        <div className="space-y-3 mb-4">
                          <div className="flex items-start text-xs text-gray-600">
                            <FiFileText
                              className="mr-2 mt-0.5 flex-shrink-0"
                              style={{ color: 'var(--color-director)' }}
                            />
                            <span className="line-clamp-2 leading-relaxed">
                              {course.description || 'No description available'}
                            </span>
                          </div>

                          <div
                            className="flex items-center text-xs text-gray-600 p-2 rounded-md"
                            style={{ backgroundColor: 'var(--color-director-light)' }}
                          >
                            <FaGraduationCap
                              className="mr-2"
                              style={{ color: 'var(--color-director)' }}
                            />
                            <span className="font-medium">Status:</span>
                            <span className="ml-1 text-green-600 font-medium">Active</span>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex space-x-2">
                          <motion.button
                            onClick={() => selectCourseForUpdate(course)}
                            className="flex-1 text-white px-3 py-2 rounded-md font-medium text-xs transition-all duration-200 flex items-center justify-center space-x-1"
                            style={{ backgroundColor: 'var(--color-director)' }}
                            variants={buttonVariants}
                            whileHover="hover"
                            whileTap="tap"
                          >
                            <FiEdit size={14} />
                            <span>Edit</span>
                          </motion.button>

                          <motion.button
                            onClick={() => handleDelete(course.course_id)}
                            disabled={loadingCourseId === course.course_id || !course.course_id}
                            className="flex-1 bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-md font-medium text-xs transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1"
                            variants={buttonVariants}
                            whileHover="hover"
                            whileTap="tap"
                          >
                            {loadingCourseId === course.course_id ? (
                              <>
                                <motion.div
                                  className="w-3 h-3 border-2 border-white border-t-transparent rounded-full"
                                  animate={{ rotate: 360 }}
                                  transition={{
                                    duration: 1,
                                    repeat: Number.POSITIVE_INFINITY,
                                    ease: 'linear'
                                  }}
                                />
                                <span>Deleting...</span>
                              </>
                            ) : (
                              <>
                                <FiTrash2 size={14} />
                                <span>Delete</span>
                              </>
                            )}
                          </motion.button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </motion.div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default CreateCourses;
