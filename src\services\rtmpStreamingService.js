import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile, toBlobURL } from '@ffmpeg/util';

class RTMPStreamingService {
  constructor() {
    this.ffmpeg = new FFmpeg();
    this.isLoaded = false;
    this.isStreaming = false;
    this.mediaRecorder = null;
    this.chunks = [];
    this.streamingInterval = null;
    this.rtmpUrl = null;
    this.streamKey = null;
    this.isInitializing = false;
  }

  async initialize() {
    if (this.isLoaded || this.isInitializing) return;
    
    this.isInitializing = true;
    
    try {
      console.log('🔄 Initializing FFmpeg for RTMP streaming...');
      
      const baseURL = 'https://unpkg.com/@ffmpeg/core@0.12.6/dist/umd';
      const coreURL = await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript');
      const wasmURL = await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm');
      
      await this.ffmpeg.load({
        coreURL,
        wasmURL,
      });
      
      this.isLoaded = true;
      this.isInitializing = false;
      console.log('✅ FFmpeg loaded successfully for RTMP streaming');
    } catch (error) {
      this.isInitializing = false;
      console.error('❌ Failed to load FFmpeg:', error);
      throw error;
    }
  }

  async startRTMPStream(mediaStream, rtmpUrl, streamKey) {
    if (!this.isLoaded) {
      await this.initialize();
    }

    try {
      console.log('🚀 Starting RTMP stream...');
      this.isStreaming = true;
      this.rtmpUrl = rtmpUrl;
      this.streamKey = streamKey;
      const fullRtmpUrl = `${rtmpUrl}/${streamKey}`;
      
      // Create MediaRecorder to capture stream
      const options = {
        mimeType: 'video/webm;codecs=vp8,opus',
        videoBitsPerSecond: 2500000,
        audioBitsPerSecond: 128000
      };

      // Fallback for browsers that don't support the preferred codec
      if (!MediaRecorder.isTypeSupported(options.mimeType)) {
        options.mimeType = 'video/webm';
      }

      this.mediaRecorder = new MediaRecorder(mediaStream, options);
      this.chunks = [];
      
      this.mediaRecorder.ondataavailable = async (event) => {
        if (event.data.size > 0 && this.isStreaming) {
          this.chunks.push(event.data);
          await this.processChunk(event.data, fullRtmpUrl);
        }
      };

      this.mediaRecorder.onerror = (event) => {
        console.error('❌ MediaRecorder error:', event.error);
        this.stopRTMPStream();
      };

      this.mediaRecorder.onstop = () => {
        console.log('📹 MediaRecorder stopped');
      };

      // Start recording with 2-second intervals for better streaming
      this.mediaRecorder.start(2000);
      
      console.log('✅ RTMP stream started successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to start RTMP stream:', error);
      this.isStreaming = false;
      return false;
    }
  }

  async processChunk(chunk, rtmpUrl) {
    if (!this.isStreaming) return;
    
    try {
      const inputName = `input_${Date.now()}.webm`;
      const outputName = `output_${Date.now()}.flv`;
      
      // Write input file
      await this.ffmpeg.writeFile(inputName, await fetchFile(chunk));
      
      // Convert WebM to FLV for RTMP streaming
      await this.ffmpeg.exec([
        '-i', inputName,
        '-c:v', 'libx264',
        '-preset', 'ultrafast',
        '-tune', 'zerolatency',
        '-g', '30',
        '-keyint_min', '30',
        '-sc_threshold', '0',
        '-c:a', 'aac',
        '-ar', '44100',
        '-b:a', '128k',
        '-f', 'flv',
        outputName
      ]);
      
      // Read the output file
      const data = await this.ffmpeg.readFile(outputName);
      
      // Send to RTMP server (this is a simplified approach)
      // In a real implementation, you'd need a WebSocket or WebRTC data channel
      // to send this data to your backend for RTMP publishing
      await this.sendToRTMPServer(data, rtmpUrl);
      
      // Clean up files
      await this.ffmpeg.deleteFile(inputName);
      await this.ffmpeg.deleteFile(outputName);
      
    } catch (error) {
      console.error('❌ Error processing chunk:', error);
    }
  }

  async sendToRTMPServer(data, rtmpUrl) {
    // This is a placeholder for sending data to RTMP server
    // In practice, you'd send this via WebSocket to your backend
    // which would then push to the actual RTMP endpoint
    console.log(`📡 Sending ${data.length} bytes to RTMP server`);
    
    // For now, we'll use a different approach with WebRTC DataChannel
    // or WebSocket to send the processed data to the backend
  }

  stopRTMPStream() {
    console.log('🛑 Stopping RTMP stream...');
    
    if (this.mediaRecorder && this.isStreaming) {
      this.mediaRecorder.stop();
    }
    
    if (this.streamingInterval) {
      clearInterval(this.streamingInterval);
      this.streamingInterval = null;
    }
    
    this.isStreaming = false;
    this.chunks = [];
    this.rtmpUrl = null;
    this.streamKey = null;
    
    console.log('✅ RTMP stream stopped');
  }

  getStreamingStatus() {
    return {
      isLoaded: this.isLoaded,
      isStreaming: this.isStreaming,
      isInitializing: this.isInitializing,
      rtmpUrl: this.rtmpUrl,
      streamKey: this.streamKey ? '***' + this.streamKey.slice(-4) : null
    };
  }

  // Alternative approach using Canvas and WebSocket
  async startCanvasRTMPStream(mediaStream, rtmpUrl, streamKey, websocketUrl) {
    try {
      console.log('🎨 Starting Canvas-based RTMP stream...');
      
      // Create video element to display the stream
      const video = document.createElement('video');
      video.srcObject = mediaStream;
      video.play();
      
      // Create canvas for frame capture
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      // Set canvas dimensions
      video.addEventListener('loadedmetadata', () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
      });
      
      // WebSocket connection to backend for RTMP relay
      const ws = new WebSocket(websocketUrl);
      
      ws.onopen = () => {
        console.log('🔗 WebSocket connected for RTMP streaming');
        
        // Send RTMP configuration
        ws.send(JSON.stringify({
          type: 'rtmp_config',
          rtmpUrl: rtmpUrl,
          streamKey: streamKey,
          width: canvas.width,
          height: canvas.height
        }));
        
        // Start frame capture
        this.startFrameCapture(video, canvas, ctx, ws);
      };
      
      ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
      };
      
      ws.onclose = () => {
        console.log('🔌 WebSocket disconnected');
        this.stopRTMPStream();
      };
      
      this.websocket = ws;
      this.isStreaming = true;
      
      return true;
    } catch (error) {
      console.error('❌ Failed to start Canvas RTMP stream:', error);
      return false;
    }
  }

  startFrameCapture(video, canvas, ctx, websocket) {
    const captureFrame = () => {
      if (!this.isStreaming) return;
      
      try {
        // Draw video frame to canvas
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        
        // Convert canvas to blob
        canvas.toBlob((blob) => {
          if (blob && this.isStreaming) {
            // Send frame data via WebSocket
            websocket.send(blob);
          }
        }, 'image/jpeg', 0.8);
        
        // Schedule next frame (30 FPS)
        setTimeout(captureFrame, 1000 / 30);
      } catch (error) {
        console.error('❌ Frame capture error:', error);
      }
    };
    
    captureFrame();
  }
}

export default new RTMPStreamingService();
