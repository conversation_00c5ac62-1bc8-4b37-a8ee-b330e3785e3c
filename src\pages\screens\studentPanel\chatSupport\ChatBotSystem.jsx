'use client';

import { useState, useEffect } from 'react';
import ChatSupport from './ChatSupport';
import EnhancedBotIcon from './BotIcon'; // Adjust path if needed

const ChatBotSystem = () => {
  const [isChatOpen, setIsChatOpen] = useState(false);

  const handleOpenChat = () => {
    console.log('Opening chat, setting isChatOpen to true');
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    console.log('Closing chat, setting isChatOpen to false');
    setIsChatOpen(false);
  };

  // Debug isChatOpen changes
  useEffect(() => {
    console.log('ChatBotSystem: isChatOpen =', isChatOpen);
  }, [isChatOpen]);

  // Log EnhancedBotIcon rendering
  useEffect(() => {
    console.log('ChatBotSystem: Rendering EnhancedBotIcon with isVisible =', !isChatOpen);
  }, [isChatOpen]);

  return (
    <>
      <EnhancedBotIcon
        key={`bot-icon-${isChatOpen}`} // Ensure re-render
        onClick={handleOpenChat}
        isVisible={!isChatOpen}
        hasNotification={false}
        isActive={false}
        messageCount={0}
        isTyping={false}
        className="debug-bot-icon"
      />
      <ChatSupport isVisible={isChatOpen} onClose={handleCloseChat} />
    </>
  );
};

export default ChatBotSystem;
