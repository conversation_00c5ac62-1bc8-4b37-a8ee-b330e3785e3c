import { useRef, useEffect, Suspense, useState, useCallback, useMemo } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera } from '@react-three/drei';
import { Avatar } from './Avatar';
import * as THREE from 'three';

// Browser detection for WebGL context settings
const getBrowserInfo = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  const isChrome =
    userAgent.includes('chrome') && !userAgent.includes('edg') && !userAgent.includes('brave');
  const isEdge = userAgent.includes('edg');
  const isBrave =
    userAgent.includes('brave') ||
    (userAgent.includes('chrome') && !window.chrome?.runtime?.onConnect);
  const isFirefox = userAgent.includes('firefox');

  return {
    isChrome,
    isEdge,
    isBrave,
    isFirefox,
    isProblematicBrowser: isChrome || isEdge,
    browserName: isChrome
      ? 'Chrome'
      : isEdge
        ? 'Edge'
        : isBrave
          ? 'Brave'
          : isFirefox
            ? 'Firefox'
            : 'Unknown'
  };
};

// Built-in Performance Monitor Component
const PerformanceMonitor = ({ onPerformanceChange, showStats }) => {
  const [fps, setFps] = useState(60);
  const [frameTime, setFrameTime] = useState(16);
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const fpsHistory = useRef([]);

  useEffect(() => {
    let animationId;

    const updateStats = () => {
      const now = performance.now();
      const delta = now - lastTime.current;

      frameCount.current++;

      if (frameCount.current % 60 === 0) {
        // Update every 60 frames
        const currentFps = Math.round(1000 / (delta / 60));
        const currentFrameTime = Math.round(delta / 60);

        setFps(currentFps);
        setFrameTime(currentFrameTime);

        // Keep FPS history for performance analysis
        fpsHistory.current.push(currentFps);
        if (fpsHistory.current.length > 10) {
          fpsHistory.current.shift();
        }

        // Calculate average FPS
        const avgFps = fpsHistory.current.reduce((a, b) => a + b, 0) / fpsHistory.current.length;

        if (onPerformanceChange) {
          onPerformanceChange({
            fps: currentFps,
            avgFps: Math.round(avgFps),
            frameTime: currentFrameTime,
            memoryUsage: performance.memory
              ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
              : 0
          });
        }

        frameCount.current = 0;
      }

      lastTime.current = now;
      animationId = requestAnimationFrame(updateStats);
    };

    updateStats();

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [onPerformanceChange]);

  if (!showStats) return null;

  return (
    <div
      style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '10px',
        borderRadius: '5px',
        fontSize: '12px',
        fontFamily: 'monospace',
        zIndex: 1000,
        minWidth: '120px'
      }}
    >
      <div>FPS: {fps}</div>
      <div>Frame: {frameTime}ms</div>
      {performance.memory && (
        <div>Memory: {Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)}MB</div>
      )}
    </div>
  );
};

// Built-in WebGL Troubleshoot Component
const WebGLTroubleshoot = ({ onClose }) => {
  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        background: 'rgba(0, 0, 0, 0.9)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000
      }}
    >
      <div
        style={{
          background: 'white',
          padding: '30px',
          borderRadius: '10px',
          maxWidth: '500px',
          maxHeight: '80vh',
          overflow: 'auto'
        }}
      >
        <h2 style={{ marginTop: 0, color: '#333' }}>WebGL Troubleshooting</h2>
        <div style={{ color: '#666', lineHeight: '1.6' }}>
          <h3>For Chrome Users:</h3>
          <ol>
            <li>
              Type <code>chrome://settings/</code> in address bar
            </li>
            <li>Click "Advanced" → "System"</li>
            <li>Enable "Use hardware acceleration when available"</li>
            <li>Restart Chrome</li>
          </ol>

          <h3>Alternative Solutions:</h3>
          <ul>
            <li>Update your graphics drivers</li>
            <li>Try a different browser (Firefox, Edge)</li>
            <li>Disable browser extensions temporarily</li>
            <li>Clear browser cache and cookies</li>
          </ul>

          <h3>If problems persist:</h3>
          <p>Your device may not support WebGL. Try using a different device or contact support.</p>
        </div>

        <button
          onClick={onClose}
          style={{
            marginTop: '20px',
            padding: '10px 20px',
            background: '#4ecdc4',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          Close
        </button>
      </div>
    </div>
  );
};

// WebGL capability detection
const checkWebGLSupport = () => {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    return !!gl;
  } catch (e) {
    return false;
  }
};

// Error boundary component for WebGL errors
const WebGLErrorBoundary = ({ children, onError }) => {
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [showTroubleshoot, setShowTroubleshoot] = useState(false);

  useEffect(() => {
    const handleWebGLError = (event) => {
      console.error('WebGL Error:', event);
      setHasError(true);
      setErrorMessage('WebGL context lost. Please refresh the page or try a different browser.');
      if (onError) onError(event);
    };

    // Listen for WebGL context lost events
    window.addEventListener('webglcontextlost', handleWebGLError);
    window.addEventListener('webglcontextrestored', () => {
      setHasError(false);
      setErrorMessage('');
    });

    return () => {
      window.removeEventListener('webglcontextlost', handleWebGLError);
      window.removeEventListener('webglcontextrestored', () => {});
    };
  }, [onError]);

  if (hasError) {
    return (
      <>
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            color: '#ff6b6b',
            fontSize: '16px',
            fontWeight: 'bold',
            textAlign: 'center',
            zIndex: 10,
            background: 'rgba(0,0,0,0.8)',
            padding: '30px',
            borderRadius: '10px',
            border: '2px solid #ff6b6b',
            maxWidth: '400px'
          }}
        >
          <div>⚠️ 3D Graphics Error</div>
          <div style={{ fontSize: '14px', marginTop: '10px', opacity: 0.9 }}>{errorMessage}</div>
          <div style={{ fontSize: '12px', marginTop: '15px', opacity: 0.7 }}>
            This usually happens in Chrome when hardware acceleration is disabled.
          </div>
          <div
            style={{ display: 'flex', gap: '10px', marginTop: '15px', justifyContent: 'center' }}
          >
            <button
              onClick={() => setShowTroubleshoot(true)}
              style={{
                padding: '8px 16px',
                background: '#4ecdc4',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer'
              }}
            >
              Fix This
            </button>
            <button
              onClick={() => window.location.reload()}
              style={{
                padding: '8px 16px',
                background: '#ff6b6b',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer'
              }}
            >
              Refresh Page
            </button>
          </div>
        </div>
        {showTroubleshoot && <WebGLTroubleshoot onClose={() => setShowTroubleshoot(false)} />}
      </>
    );
  }

  return children;
};

// Loading component for Suspense fallback
const LoadingAvatar = () => (
  <div
    style={{
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      color: '#fff',
      fontSize: '18px',
      fontWeight: 'bold',
      textAlign: 'center',
      zIndex: 10,
      background: 'rgba(0,0,0,0.7)',
      padding: '20px',
      borderRadius: '10px'
    }}
  >
    <div>Loading Avatar...</div>
    <div style={{ fontSize: '14px', marginTop: '10px', opacity: 0.8 }}>
      Please wait while we load the 3D model
    </div>
  </div>
);

// WebGL not supported fallback
const WebGLNotSupported = () => (
  <div
    style={{
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      color: '#ffa500',
      fontSize: '16px',
      fontWeight: 'bold',
      textAlign: 'center',
      zIndex: 10,
      background: 'rgba(0,0,0,0.8)',
      padding: '30px',
      borderRadius: '10px',
      border: '2px solid #ffa500',
      maxWidth: '400px'
    }}
  >
    <div>⚠️ WebGL Not Supported</div>
    <div style={{ fontSize: '14px', marginTop: '10px', opacity: 0.9 }}>
      Your browser doesn't support WebGL, which is required for 3D graphics.
    </div>
    <div style={{ fontSize: '12px', marginTop: '15px', opacity: 0.7 }}>
      Please update your browser or enable hardware acceleration in browser settings.
    </div>
  </div>
);

const AvatarScene = ({ phonemes, audioBlob, isAudioPlaying }) => {
  const controlsRef = useRef();
  const [isLoading, setIsLoading] = useState(true);
  const [webglSupported, setWebglSupported] = useState(true);
  const [canvasReady, setCanvasReady] = useState(false);

  // Browser-specific optimizations
  const browserInfo = useMemo(() => getBrowserInfo(), []);
  const [performanceMode, setPerformanceMode] = useState(
    browserInfo.isProblematicBrowser ? 'high' : 'auto'
  );
  const [showPerformanceStats, setShowPerformanceStats] = useState(false);

  // Expression testing state
  const [currentExpression, setCurrentExpression] = useState('neutral');
  const [showExpressionButtons, setShowExpressionButtons] = useState(true);

  // Web Worker for scene optimizations
  const sceneWorker = useRef(null);
  const performanceData = useRef({
    fps: 60,
    frameTime: 16,
    memoryUsage: 0,
    lastUpdate: 0
  });

  // Initialize scene optimization worker
  useEffect(() => {
    const workerCode = `
      let performanceHistory = [];
      let optimizationLevel = 'medium';

      self.onmessage = function(e) {
        const { type, data } = e.data;

        switch(type) {
          case 'PERFORMANCE_UPDATE':
            performanceHistory.push(data);
            if (performanceHistory.length > 30) {
              performanceHistory.shift();
            }

            // Calculate performance trends
            const avgFps = performanceHistory.reduce((sum, p) => sum + p.fps, 0) / performanceHistory.length;
            const avgFrameTime = performanceHistory.reduce((sum, p) => sum + p.frameTime, 0) / performanceHistory.length;

            // Determine optimization level
            let newLevel = 'medium';
            if (avgFps < 25 || avgFrameTime > 40) {
              newLevel = 'aggressive';
            } else if (avgFps > 50 && avgFrameTime < 20) {
              newLevel = 'minimal';
            }

            if (newLevel !== optimizationLevel) {
              optimizationLevel = newLevel;
              self.postMessage({
                type: 'OPTIMIZATION_CHANGE',
                data: {
                  level: optimizationLevel,
                  recommendations: getOptimizationRecommendations(avgFps, avgFrameTime)
                }
              });
            }
            break;
        }
      };

      function getOptimizationRecommendations(fps, frameTime) {
        const recommendations = [];

        if (fps < 20) {
          recommendations.push('reduce_quality');
          recommendations.push('skip_frames');
          recommendations.push('reduce_shadows');
        } else if (fps < 30) {
          recommendations.push('moderate_optimization');
          recommendations.push('adaptive_quality');
        } else if (fps > 55) {
          recommendations.push('increase_quality');
          recommendations.push('enable_effects');
        }

        return recommendations;
      }
    `;

    const blob = new Blob([workerCode], { type: 'application/javascript' });
    sceneWorker.current = new Worker(URL.createObjectURL(blob));

    sceneWorker.current.onmessage = (e) => {
      const { type, data } = e.data;

      if (type === 'OPTIMIZATION_CHANGE') {
        console.log('Performance optimization level changed:', data.level);
        console.log('Recommendations:', data.recommendations);

        // Apply optimizations based on recommendations
        if (data.recommendations.includes('reduce_quality')) {
          setPerformanceMode('high');
        } else if (data.recommendations.includes('increase_quality')) {
          setPerformanceMode('high');
        } else {
          setPerformanceMode('medium');
        }
      }
    };

    return () => {
      if (sceneWorker.current) {
        sceneWorker.current.terminate();
      }
    };
  }, []);

  // Check WebGL support on mount
  useEffect(() => {
    const supported = checkWebGLSupport();
    setWebglSupported(supported);
    if (!supported) {
      console.warn('WebGL is not supported in this browser');
    }
  }, []);

  useEffect(() => {
    if (controlsRef.current) {
      controlsRef.current.object.position.set(0, 0.8, 2.5);
      controlsRef.current.target.set(0, 0.2, 0);
      controlsRef.current.update();
    }
  }, []);

  // Browser-specific loading with longer delays for Chrome/Edge
  useEffect(() => {
    const loadingTime = browserInfo.isProblematicBrowser ? 3000 : 2000; // Longer for Chrome/Edge
    const timer = setTimeout(() => {
      setIsLoading(false);
      setCanvasReady(true);
    }, loadingTime);

    console.log(`Loading optimized for ${browserInfo.browserName} (${loadingTime}ms)`);

    return () => clearTimeout(timer);
  }, [browserInfo]);

  // Enhanced performance monitoring with Web Worker integration
  const handlePerformanceChange = useCallback((perfData) => {
    performanceData.current = perfData;

    // Send performance data to worker for analysis
    if (sceneWorker.current) {
      sceneWorker.current.postMessage({
        type: 'PERFORMANCE_UPDATE',
        data: perfData
      });
    }

    // Immediate performance warnings
    if (perfData.fps < 15) {
      console.warn('Critical performance issue detected - FPS:', perfData.fps);
    } else if (perfData.fps < 25) {
      console.warn('Low performance detected - FPS:', perfData.fps);
    }
  }, []);

  // Handle Canvas errors with recovery
  const handleCanvasError = useCallback((error) => {
    console.error('Canvas Error:', error);
    setCanvasReady(false);

    // Attempt recovery after a delay
    setTimeout(() => {
      setCanvasReady(true);
    }, 1000);
  }, []);

  // Toggle performance stats (for debugging)
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (event.key === 'p' && event.ctrlKey) {
        setShowPerformanceStats((prev) => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  // If WebGL is not supported, show fallback
  if (!webglSupported) {
    return (
      <div style={{ width: '100%', height: '100%', position: 'relative', background: '#1a1a1a' }}>
        <WebGLNotSupported />
      </div>
    );
  }

  // Browser-specific Canvas configuration
  const getCanvasConfig = useCallback(() => {
    const baseConfig = {
      alpha: true,
      powerPreference: browserInfo.isProblematicBrowser ? 'default' : 'high-performance',
      failIfMajorPerformanceCaveat: false,
      preserveDrawingBuffer: false,
      stencil: false,
      depth: true
    };

    // Chrome/Edge specific optimizations
    if (browserInfo.isProblematicBrowser) {
      return {
        ...baseConfig,
        antialias: false,
        precision: 'lowp',
        logarithmicDepthBuffer: false,
        premultipliedAlpha: false,
        desynchronized: true // Better for Chrome/Edge
      };
    }

    switch (performanceMode) {
      case 'high':
        return {
          ...baseConfig,
          antialias: true,
          precision: 'highp',
          logarithmicDepthBuffer: true
        };
      case 'low':
        return {
          ...baseConfig,
          antialias: false,
          precision: 'lowp',
          logarithmicDepthBuffer: false
        };
      default: // medium/auto
        return {
          ...baseConfig,
          antialias: window.devicePixelRatio <= 1,
          precision: 'mediump',
          logarithmicDepthBuffer: false
        };
    }
  }, [performanceMode, browserInfo]);

  const getPerformanceConfig = useCallback(() => {
    // More conservative settings for Chrome/Edge
    if (browserInfo.isProblematicBrowser) {
      return { min: 0.1, max: 0.5, debounce: 500 };
    }

    switch (performanceMode) {
      case 'high':
        return { min: 0.5, max: 1.0, debounce: 100 };
      case 'low':
        return { min: 0.1, max: 0.6, debounce: 300 };
      default:
        return { min: 0.2, max: 1.0, debounce: 200 };
    }
  }, [performanceMode, browserInfo]);

  const getDPR = useCallback(() => {
    // Lower DPR for Chrome/Edge
    if (browserInfo.isProblematicBrowser) {
      return Math.min(window.devicePixelRatio, 1);
    }

    switch (performanceMode) {
      case 'high':
        return Math.min(window.devicePixelRatio, 3);
      case 'low':
        return Math.min(window.devicePixelRatio, 1);
      default:
        return Math.min(window.devicePixelRatio, 2);
    }
  }, [performanceMode, browserInfo]);

  return (
    <div style={{ width: '100%', height: '100%', position: 'relative' }}>
      <WebGLErrorBoundary onError={handleCanvasError}>
        {isLoading && <LoadingAvatar />}
        {canvasReady && (
          <Canvas
            shadows={performanceMode !== 'high'}
            gl={getCanvasConfig()}
            style={{ background: 'transparent' }}
            frameloop="always"
            performance={getPerformanceConfig()}
            dpr={getDPR()}
            onError={handleCanvasError}
            onCreated={({ gl, scene, camera }) => {
              // Browser-specific WebGL optimizations
              if (browserInfo.isProblematicBrowser) {
                // Conservative settings for Chrome/Edge
                gl.physicallyCorrectLights = false;
                gl.outputEncoding = THREE.LinearEncoding;
                gl.toneMapping = THREE.NoToneMapping;
                gl.toneMappingExposure = 1.0;
                gl.shadowMap.enabled = false;
                gl.shadowMap.type = THREE.BasicShadowMap;
                console.log(`Applied conservative WebGL settings for ${browserInfo.browserName}`);
              } else {
                // Advanced WebGL optimizations for other browsers
                gl.physicallyCorrectLights = performanceMode === 'high';
                gl.outputEncoding = THREE.sRGBEncoding;
                gl.toneMapping =
                  performanceMode === 'high'
                    ? THREE.ACESFilmicToneMapping
                    : THREE.LinearToneMapping;
                gl.toneMappingExposure = 1.0;
              }

              // Optimize rendering
              gl.setPixelRatio(getDPR());
              gl.setClearColor(0x000000, 0);

              // Scene optimizations
              scene.fog =
                performanceMode === 'high' && !browserInfo.isProblematicBrowser
                  ? new THREE.Fog(0x000000, 10, 50)
                  : null;

              // Camera optimizations
              camera.far =
                performanceMode === 'high' || browserInfo.isProblematicBrowser ? 100 : 1000;
              camera.updateProjectionMatrix();

              console.log(
                `Canvas initialized for ${browserInfo.browserName} with performance mode:`,
                performanceMode
              );
            }}
          >
            <PerspectiveCamera
              makeDefault
              rotation={[0, 90, 0]}
              position={[0, 0.4, 3]}
              fov={50}
              near={0.1}
              far={performanceMode === 'high' || browserInfo.isProblematicBrowser ? 100 : 1000}
            />

            {/* Browser-adaptive lighting */}
            <ambientLight
              intensity={
                browserInfo.isProblematicBrowser ? 0.9 : performanceMode === 'high' ? 0.8 : 1
              }
              color={new THREE.Color(0xffffff)}
            />
            <directionalLight
              position={[5, 5, 5]}
              intensity={
                browserInfo.isProblematicBrowser ? 1.2 : performanceMode === 'high' ? 1.5 : 2
              }
              castShadow={!browserInfo.isProblematicBrowser && performanceMode !== 'high'}
              color={new THREE.Color(0xffddaa)}
              shadow-mapSize-width={
                performanceMode === 'high' && !browserInfo.isProblematicBrowser ? 2048 : 1024
              }
              shadow-mapSize-height={
                performanceMode === 'high' && !browserInfo.isProblematicBrowser ? 2048 : 1024
              }
            />
            {performanceMode !== 'high' && !browserInfo.isProblematicBrowser && (
              <spotLight
                position={[-5, 2, 0]}
                intensity={0.8}
                angle={0.5}
                penumbra={2}
                color={new THREE.Color(0xaaddff)}
              />
            )}

            <Suspense fallback={null}>
              <Avatar
                phonemes={phonemes}
                audioBlob={audioBlob}
                isAudioPlaying={isAudioPlaying}
                animation="Idle"
                currentExpression={currentExpression}
              />
              <PerformanceMonitor
                onPerformanceChange={handlePerformanceChange}
                showStats={showPerformanceStats}
              />
            </Suspense>

            <OrbitControls
              ref={controlsRef}
              enablePan={false}
              enableZoom={false}
              minPolarAngle={Math.PI / 2.5}
              maxPolarAngle={Math.PI / 2}
              minAzimuthAngle={-Math.PI / 4}
              maxAzimuthAngle={Math.PI / 4}
              enableDamping={!browserInfo.isProblematicBrowser && performanceMode !== 'high'}
              dampingFactor={0.05}
            />
          </Canvas>
        )}
      </WebGLErrorBoundary>

      {showPerformanceStats && (
        <div
          style={{
            position: 'absolute',
            bottom: '10px',
            left: '10px',
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '10px',
            borderRadius: '5px',
            fontSize: '12px',
            zIndex: 1000,
            fontFamily: 'monospace'
          }}
        >
          <div>Browser: {browserInfo.browserName}</div>
          <div>Performance Mode: {performanceMode}</div>
          <div>Optimized: {browserInfo.isProblematicBrowser ? 'Yes' : 'No'}</div>
          <div>Press Ctrl+P to toggle stats</div>
          <div>FPS: {performanceData.current.fps}</div>
          <div>Frame Time: {performanceData.current.frameTime}ms</div>
        </div>
      )}

      {/* Temporary Expression Testing Buttons */}
      {/* {showExpressionButtons && (
        <div
          style={{
            position: 'absolute',
            top: '10px',
            left: '10px',
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '15px',
            borderRadius: '8px',
            fontSize: '14px',
            zIndex: 1000,
            fontFamily: 'Arial, sans-serif',
            maxWidth: '200px'
          }}>
          <div style={{ marginBottom: '10px', fontWeight: 'bold' }}>Expression Testing</div>
          <div style={{ marginBottom: '10px', fontSize: '12px' }}>Current: {currentExpression}</div>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '5px' }}>
            {[
              'neutral',
              'smile',
              'sad',
              'angry',
              'surprised',
              'blink',
              'confused',
              'excited',
              'disgust',
              'thinking',
              'laugh',
              'fear',
              'bored',
              'focus',
              'wink_left',
              'wink_right'
            ].map((expression) => (
              <button
                key={expression}
                onClick={() => setCurrentExpression(expression)}
                style={{
                  padding: '5px 8px',
                  background: currentExpression === expression ? '#4ecdc4' : '#555',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontSize: '11px',
                  transition: 'background 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (currentExpression !== expression) {
                    e.target.style.background = '#666';
                  }
                }}
                onMouseLeave={(e) => {
                  if (currentExpression !== expression) {
                    e.target.style.background = '#555';
                  }
                }}>
                {expression.replace('_', ' ')}
              </button>
            ))}
          </div>
          <button
            onClick={() => setShowExpressionButtons(false)}
            style={{
              marginTop: '10px',
              padding: '5px 10px',
              background: '#ff6b6b',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '11px',
              width: '100%'
            }}>
            Hide Expression Panel
          </button>
        </div>
      )} */}

      {/* Show Expression Panel Button (when hidden) */}
      {/* {!showExpressionButtons && (
        <button
          onClick={() => setShowExpressionButtons(true)}
          style={{
            position: 'absolute',
            top: '10px',
            left: '10px',
            padding: '8px 12px',
            background: '#4ecdc4',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '12px',
            zIndex: 1000
          }}>
          Show Expressions
        </button>
      )} */}
    </div>
  );
};
export default AvatarScene;
