import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  ChevronDown,
  ChevronUp,
  Filter,
  Search,
  BarChart2,
  User,
  Users,
  BookOpen,
  TrendingUp,
  Frown,
  Smile,
  MapPin
} from 'lucide-react';
import { useGetParentStudentAttendanceQuery } from './studentAttendance.slice';
import { useSelector } from 'react-redux';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 }
};

const cardVariants = {
  hover: {
    y: -5,
    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
  }
};

function StudentAttendance() {
  const user = useSelector((state) => state.auth.user);
  const [activeTab, setActiveTab] = useState('daily');
  const [expandedStudent, setExpandedStudent] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    month: new Date().getMonth(),
    date: ''
  });

  const { data, isLoading, error } = useGetParentStudentAttendanceQuery(user?.studentId);

  const toggleStudent = (studentId) => {
    setExpandedStudent(expandedStudent === studentId ? null : studentId);
  };

  if (isLoading) {
    return (
      <motion.div
        className="flex justify-center items-center h-screen"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ repeat: Infinity, duration: 1, ease: 'linear' }}
          className="rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
        />
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        className="flex justify-center items-center h-screen"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <motion.div
          className="bg-white rounded-xl shadow-lg p-8 w-96 text-center"
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
        >
          <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <motion.p
            className="text-red-700 font-medium text-lg"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            {error.data?.message || 'Failed to load attendance data'}
          </motion.p>
        </motion.div>
      </motion.div>
    );
  }

  if (!data?.students?.length) {
    return (
      <motion.div
        className="flex justify-center items-center h-screen"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <motion.div
          className="bg-white rounded-xl shadow-lg p-8 w-96 text-center"
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
        >
          <AlertCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <motion.p
            className="text-gray-600 font-medium text-lg"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            No attendance records found for your child
          </motion.p>
        </motion.div>
      </motion.div>
    );
  }

  const statusColors = {
    Present: 'bg-emerald-100 text-emerald-600',
    Absent: 'bg-rose-100 text-rose-600',
    late: 'bg-amber-100 text-amber-600',
    excused: 'bg-blue-100 text-blue-600',
    No: 'bg-gray-100 text-gray-600'
  };

  const statusIcons = {
    Present: <CheckCircle size={16} />,
    Absent: <XCircle size={16} />,
    late: <AlertCircle size={16} />,
    excused: <BookOpen size={16} />
  };

  // Process attendance data for daily and monthly views
  const student = data.students[0];
  const attendanceRecords = student.attendance || [];

  const filteredRecords = attendanceRecords.filter((record) => {
    const matchesSearch = student.first_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      filters.status === 'all' ||
      (filters.status === 'late'
        ? record.late === 'Yes'
        : record.status.toLowerCase() === filters.status);
    const recordDate = new Date(record.attendance_date);
    const matchesMonth = recordDate.getMonth() === Number(filters.month);
    const matchesDate = !filters.date || record.attendance_date === filters.date;
    return matchesSearch && matchesStatus && matchesMonth && matchesDate;
  });

  // Calculate monthly stats
  const monthlyStats = {
    present: attendanceRecords.filter(
      (record) =>
        record.status === 'Present' &&
        new Date(record.attendance_date).getMonth() === Number(filters.month)
    ).length,
    absent: attendanceRecords.filter(
      (record) =>
        record.status === 'Absent' &&
        new Date(record.attendance_date).getMonth() === Number(filters.month)
    ).length,
    late: attendanceRecords.filter(
      (record) =>
        record.late === 'Yes' &&
        new Date(record.attendance_date).getMonth() === Number(filters.month)
    ).length,
    excused: 0, // Assuming no 'excused' status in API data; adjust if needed
    trend:
      attendanceRecords.filter(
        (record) =>
          record.status === 'Present' &&
          new Date(record.attendance_date).getMonth() === Number(filters.month)
      ).length >
      attendanceRecords.filter(
        (record) =>
          record.status === 'Present' &&
          new Date(record.attendance_date).getMonth() === Number(filters.month) - 1
      ).length
        ? 'up'
        : 'down'
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-8 px-4">
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <motion.div
          className="mb-12 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="relative mb-8 group"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            whileHover={{ scale: 1.02 }}
          >
            <div className="absolute -left-4 -top-4 h-16 w-16 rounded-full bg-[var(--color-parents)]/10 blur-md group-hover:bg-[var(--color-parents)]/20 transition-all"></div>
            <div className="absolute -right-4 -bottom-4 h-12 w-12 rounded-full bg-[var(--color-parents)]/10 blur-md group-hover:bg-[var(--color-parents)]/20 transition-all"></div>
            <div className="relative z-10 bg-gradient-to-r from-[var(--color-parents)]/5 to-[var(--color-parents)]/10 backdrop-blur-sm border border-[var(--color-teacher)]/20 rounded-2xl px-8 py-6 shadow-lg overflow-hidden">
              <div className="absolute inset-0 opacity-5 [mask-image:linear-gradient(to_bottom,transparent,white)]">
                <svg className="absolute inset-0 h-full w-full" xmlns="http://www.w3.org/2000/svg">
                  <pattern
                    id="pattern"
                    x="0"
                    y="0"
                    width="40"
                    height="40"
                    patternUnits="userSpaceOnUse"
                  >
                    <path
                      d="M0 40L40 0H20L0 20M40 40V20L20 40"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="1.5"
                    />
                  </pattern>
                  <rect width="100%" height="100%" fill="url(#pattern)" />
                </svg>
              </div>
              <div className="relative z-20 flex items-center space-x-4">
                <div className="p-3 rounded-xl bg-[var(--color-parents)] text-white shadow-lg">
                  <Users className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-[var(--color-teacher)]">
                    Student Attendance
                  </h1>
                  <p className="text-sm text-gray-500 mt-1">
                    Track your child's attendance details
                  </p>
                </div>
              </div>
              <motion.div
                className="absolute bottom-0 left-0 h-1 bg-[var(--color-counselor)]"
                initial={{ width: 0 }}
                animate={{ width: '100%' }}
                transition={{ duration: 1, delay: 0.3 }}
              />
            </div>
          </motion.div>
        </motion.div>

        {/* Stats Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
          variants={containerVariants}
          initial="hidden"
          animate="show"
        >
          {[
            {
              title: 'Total Present',
              value: monthlyStats.present,
              icon: <Smile className="text-emerald-600" size={20} />,
              bg: 'bg-emerald-50'
            },
            {
              title: 'Total Absent',
              value: monthlyStats.absent,
              icon: <Frown className="text-rose-600" size={20} />,
              bg: 'bg-rose-50'
            },
            {
              title: 'Late Arrivals',
              value: monthlyStats.late,
              icon: <Clock className="text-amber-600" size={20} />,
              bg: 'bg-amber-50'
            },
            {
              title: "Child's Attendance Progress",
              value: monthlyStats.trend === 'up' ? 'Getting Better' : 'Needs Attention',
              icon: (
                <TrendingUp
                  className={monthlyStats.trend === 'up' ? 'text-green-600' : 'text-red-600'}
                  size={20}
                />
              ),
              bg: monthlyStats.trend === 'up' ? 'bg-green-50' : 'bg-red-50'
            }
          ].map((stat, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ y: -5 }}
              className={`${stat.bg} p-5 rounded-xl shadow-sm border border-gray-100`}
            >
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-gray-500">{stat.title}</p>
                  <p className="text-2xl font-semibold mt-1">{stat.value}</p>
                </div>
                <div className="p-2 rounded-lg bg-white shadow-xs">{stat.icon}</div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Tabs and Filters */}
        <motion.div
          className="bg-white rounded-xl p-1 shadow-sm border border-gray-200 inline-flex mb-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {['daily', 'monthly'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                activeTab === tab
                  ? 'bg-indigo-100 text-indigo-700'
                  : 'text-gray-500 hover:bg-gray-100'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)} View
            </button>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="flex flex-col md:flex-row gap-4 mb-6"
        >
          <div className="flex gap-2">
            <select
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            >
              <option value="all">All Statuses</option>
              <option value="present">Present</option>
              <option value="absent">Absent</option>
              <option value="late">Late</option>
            </select>
            <select
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
              value={filters.month}
              onChange={(e) => setFilters({ ...filters, month: e.target.value })}
            >
              {Array.from({ length: 12 }, (_, i) => (
                <option key={i} value={i}>
                  {new Date(0, i).toLocaleString('default', { month: 'long' })}
                </option>
              ))}
            </select>
            <div className="relative">
              <Calendar
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type="date"
                className="px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                value={filters.date}
                onChange={(e) => setFilters({ ...filters, date: e.target.value })}
              />
            </div>
            <button className="px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg flex items-center gap-2 text-sm">
              <Filter size={16} />
              More Filters
            </button>
          </div>
        </motion.div>

        {/* Daily View */}
        {activeTab === 'daily' && (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="show"
            className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
          >
            {/* Table Header */}
            <div className="grid grid-cols-14 gap-4 p-4 border-b border-gray-200 bg-gray-50 text-sm font-medium text-gray-500">
              <div className="col-span-4">Student</div>
              <div className="col-span-2">Status</div>
              <div className="col-span-2">Date</div>
              <div className="col-span-2">Time</div>
              <div className="col-span-2">Course</div>
              <div className="col-span-1">Late</div>
              <div className="col-span-1"></div>
            </div>

            {/* Student Rows */}
            {filteredRecords.length > 0 ? (
              filteredRecords.map((record) => (
                <motion.div
                  key={record.attendance_id}
                  variants={itemVariants}
                  className={`border-b border-gray-200 ${
                    expandedStudent === record.attendance_id ? 'bg-gray-50' : ''
                  }`}
                >
                  <div
                    className="grid grid-cols-14 gap-4 p-4 items-center cursor-pointer"
                    onClick={() => toggleStudent(record.attendance_id)}
                  >
                    <div className="col-span-4 flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                        <User className="text-indigo-600" size={16} />
                      </div>
                      <span className="font-medium">{student.first_name}</span>
                    </div>
                    <div className="col-span-2">
                      <span
                        className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${
                          statusColors[record.status]
                        }`}
                      >
                        {statusIcons[record.status]}
                        {record.status}
                      </span>
                    </div>
                    <div className="col-span-2 text-sm text-gray-500">
                      {new Date(record.attendance_date).toLocaleDateString()}
                    </div>
                    <div className="col-span-2 text-sm text-gray-500">{record.in_time || '-'}</div>
                    <div className="col-span-2 text-sm text-gray-500">{student.course}</div>
                    <div className="col-span-1">
                      <span
                        className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${
                          statusColors[record.late]
                        }`}
                      >
                        {statusIcons[record.late]}
                        {record.late}
                      </span>
                    </div>
                    <div className="col-span-1 flex justify-end">
                      {expandedStudent === record.attendance_id ? (
                        <ChevronUp size={18} />
                      ) : (
                        <ChevronDown size={18} />
                      )}
                    </div>
                  </div>

                  <AnimatePresence>
                    {expandedStudent === record.attendance_id && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.2 }}
                        className="px-4 pb-4"
                      >
                        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-xs">
                          <h4 className="font-medium mb-2">Attendance Details</h4>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <p className="text-gray-500">Date</p>
                              <p>{new Date(record.attendance_date).toLocaleDateString()}</p>
                            </div>
                            <div>
                              <p className="text-gray-500">Status</p>
                              <p className="capitalize">{record.status}</p>
                            </div>
                            <div>
                              <p className="text-gray-500">Late</p>
                              <p>{record.late}</p>
                            </div>
                            <div>
                              <p className="text-gray-500">Course</p>
                              <p>{student.course}</p>
                            </div>
                            <div>
                              <p className="text-gray-500">Center</p>
                              <p>{student.center_code}</p>
                            </div>
                            <div>
                              <p className="text-gray-500">Batch</p>
                              <p>{student.batch.batch_name}</p>
                            </div>
                            {record.in_time && (
                              <div>
                                <p className="text-gray-500">In Time</p>
                                <p>{record.in_time}</p>
                              </div>
                            )}
                          </div>
                          {record.status === 'Absent' && (
                            <button className="mt-3 text-sm text-indigo-600 hover:text-indigo-800">
                              + Add absence note
                            </button>
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="p-8 text-center"
              >
                <p className="text-gray-500">
                  No attendance records found for the selected criteria
                </p>
              </motion.div>
            )}
          </motion.div>
        )}

        {/* Monthly View */}
        {activeTab === 'monthly' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
          >
            <div className="flex justify-between items-center mb-6">
              <h3 className="font-medium text-lg flex items-center gap-2">
                <Calendar className="text-[var(--color-parents)]" size={18} />
                Monthly Attendance Overview
              </h3>
              <select
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                value={filters.month}
                onChange={(e) => setFilters({ ...filters, month: e.target.value })}
              >
                {Array.from({ length: 12 }, (_, i) => (
                  <option key={i} value={i}>
                    {new Date(0, i).toLocaleString('default', { month: 'long' })}
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h4 className="font-medium mb-4 flex items-center gap-2">
                  <BarChart2 className="text-[var(--color-parents)]" size={18} />
                  Attendance Chart
                </h4>
                <div className="h-64 bg-gray-50 rounded-lg border border-gray-200 flex items-center justify-center">
                  <p className="text-gray-400">Chart visualization would appear here</p>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-4 flex items-center gap-2">
                  <Calendar className="text-[var(--color-parents)]" size={18} />
                  Attendance Summary
                </h4>
                <div className="space-y-4">
                  {[
                    {
                      label: 'Present Days',
                      value: monthlyStats.present,
                      color: 'bg-emerald-500'
                    },
                    {
                      label: 'Absent Days',
                      value: monthlyStats.absent,
                      color: 'bg-rose-500'
                    },
                    {
                      label: 'Late Arrivals',
                      value: monthlyStats.late,
                      color: 'bg-amber-500'
                    },
                    {
                      label: 'Excused Absences',
                      value: monthlyStats.excused,
                      color: 'bg-blue-500'
                    }
                  ].map((item, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>{item.label}</span>
                        <span>{item.value} days</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`${item.color} h-2 rounded-full`}
                          style={{ width: `${(item.value / 30) * 100}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}

export default StudentAttendance;
