import React from 'react';
import PropTypes from 'prop-types';
import { AlertCircle, CheckCircle, Upload, FileText } from 'lucide-react';
import Button from './Button';

const UploadBox = ({
  //   title,
  isDragging,
  isUploaded,
  pdfFile,
  errorMessage,
  handleDragOver,
  handleDragLeave,
  handleDrop,
  handleFileInput,
  resetUploadState,
  clearErrorMessage,
  onClickState,
  inputId,
  loading,
  MAX_FILE_SIZE_MB,
  className,
  submitLabel = 'Submit',
  submitDisabled = false
}) => (
  <div className={`border border-gray-200 rounded-lg bg-white shadow-sm ${className}`}>
    {/* <h2 className="font-semibold text-lg border-b border-gray-200 p-3 px-4 text-gray-700">
      {title}
    </h2> */}
    <div
      className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300 m-4 min-h-[250px] flex flex-col justify-center
        ${
          isDragging
            ? 'border-indigo-500 bg-indigo-50'
            : isUploaded && pdfFile
              ? 'border-green-500 bg-green-50'
              : errorMessage
                ? 'border-red-500 bg-red-50'
                : 'border-gray-300 hover:border-indigo-400  hover:bg-gray-50'
        }`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {isUploaded && pdfFile ? (
        <div className="flex flex-col items-center">
          <CheckCircle className="h-10 w-10 text-green-500 mb-2" />
          <h3 className="text-md font-medium text-gray-900">PDF Ready!</h3>
          <div className="flex items-center justify-center p-2 my-1 bg-gray-100 rounded-md border border-gray-200 w-full max-w-[200px] sm:max-w-xs">
            <FileText size={20} className="text-indigo-600 mr-2 flex-shrink-0" />
            <span className="text-xs text-gray-700 truncate" title={pdfFile.name}>
              {pdfFile.name}
            </span>
          </div>
          <p className="text-gray-500 mb-2 text-xs">
            ({(pdfFile.size / 1024 / 1024).toFixed(2)} MB)
          </p>
          <div className="flex justify-center items-center gap-2">
            <Button
              name="Change document"
              onClick={resetUploadState}
              className="px-4 py-1.5 mt-4 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
              disabled={loading}
              type="button"
            />
            <Button
              name={submitLabel}
              onClick={onClickState}
              className="px-4 py-1.5 mt-4 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
              disabled={submitDisabled}
              type="button"
            />
          </div>
        </div>
      ) : errorMessage ? (
        <div className="flex flex-col items-center">
          <AlertCircle className="h-10 w-10 text-red-500 mb-2" />
          <h3 className="text-md font-medium text-gray-900 mb-1">Upload Error</h3>
          <p className="text-red-600 mb-3 text-xs">{errorMessage}</p>
          <Button
            name="Try Again"
            onClick={() => {
              clearErrorMessage();
              resetUploadState();
            }}
            className="text-sm px-3 py-1.5"
          />
        </div>
      ) : (
        <div className="flex flex-col items-center">
          <Upload className="h-10 w-10 text-gray-400 mb-2" />
          <h3 className="text-md font-medium text-gray-900 mb-1">Upload PDF</h3>
          <p className="text-gray-500 mb-3 text-xs">Drag & drop or click to browse</p>
          <input
            type="file"
            id={inputId}
            className="hidden"
            accept="application/pdf"
            onChange={handleFileInput}
          />
          <label
            htmlFor={inputId}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-400 transition-colors cursor-pointer text-xs font-medium"
          >
            Browse Files
          </label>
          <p className="text-xs text-gray-400 mt-2">Max: {MAX_FILE_SIZE_MB}MB. PDF only.</p>
        </div>
      )}
    </div>
  </div>
);

UploadBox.propTypes = {
  //   title: PropTypes.string.isRequired,
  isDragging: PropTypes.bool.isRequired,
  isUploaded: PropTypes.bool.isRequired,
  pdfFile: PropTypes.object,
  errorMessage: PropTypes.string.isRequired,
  handleDragOver: PropTypes.func.isRequired,
  handleDragLeave: PropTypes.func.isRequired,
  handleDrop: PropTypes.func.isRequired,
  handleFileInput: PropTypes.func.isRequired,
  resetUploadState: PropTypes.func.isRequired,
  clearErrorMessage: PropTypes.func.isRequired,
  inputId: PropTypes.string.isRequired,
  loading: PropTypes.bool.isRequired,
  MAX_FILE_SIZE_MB: PropTypes.number.isRequired,
  className: PropTypes.string,
  onClickState: PropTypes.func,
  submitLabel: PropTypes.string,
  submitDisabled: PropTypes.bool
};

export default UploadBox;
