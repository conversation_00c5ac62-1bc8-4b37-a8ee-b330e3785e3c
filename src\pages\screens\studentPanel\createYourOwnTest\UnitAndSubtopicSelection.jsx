import React, { useState, useEffect, useMemo } from 'react';
// eslint-disable-next-line no-unused-vars
import { motion, AnimatePresence } from 'framer-motion';
import {
  useLazyCreateYourOwnTestExamUnitsQuery,
  useLazyCreateYourOwnTestExamSubtopicsQuery
} from './createYourOwnTest.slice';
import { useTestStore } from './testStore';
import { toast } from 'react-toastify';
import PropTypes from 'prop-types';
import { useCreateYourOwnTestBioStartTestMutation } from './bioTest.slice';
import { useCreateYourOwnTestChemistryStartTestMutation } from './chemistry.Slice';
import { useCreateYourOwnTestMathStartTestMutation } from './maths.Slice';
import { useCreateYourOwnTestPhysicsStartTestMutation } from './physics.Slice';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSpinner,
  faExclamationTriangle,
  faChevronRight,
  faCheckCircle,
  faLayerGroup,
  faSitemap,
  faSlidersH,
  faQuestionCircle,
  faClock,
  faBookmark,
  faClipboardList,
  faBookOpen,
  faLightbulb
} from '@fortawesome/free-solid-svg-icons';
import ParticleAnimation from './ParticleAnimation';

const CosmicBackground = () => (
  <div className="absolute inset-0 z-0 bg-slate-50 overflow-hidden">
    <motion.div
      className="absolute inset-0"
      style={{
        backgroundImage: `radial-gradient(circle at 20% 20%, var(--color-student) 0%, transparent 25%)`,
        opacity: 0.3
      }}
      animate={{
        backgroundPosition: ['0% 0%', '100% 100%'],
        transition: {
          duration: 40,
          repeat: Infinity,
          repeatType: 'mirror',
          ease: 'linear'
        }
      }}
    />
    <motion.div
      className="absolute inset-0"
      style={{
        backgroundImage: `radial-gradient(circle at 80% 70%, var(--color-student) 0%, transparent 25%)`,
        opacity: 0.3
      }}
      animate={{
        backgroundPosition: ['100% 100%', '0% 0%'],
        transition: {
          duration: 50,
          repeat: Infinity,
          repeatType: 'mirror',
          ease: 'linear',
          delay: 5
        }
      }}
    />
  </div>
);

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
};

const listItemVariants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 }
};

const Card = ({ children, className = '', ...props }) => (
  <motion.div
    className={`bg-white rounded-lg shadow-md border border-slate-200 ${className}`}
    variants={cardVariants}
    {...props}
  >
    {children}
  </motion.div>
);
Card.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string
};

const AnimatedPlaceholder = ({ icon, message, details }) => (
  <Card
    className="flex flex-col h-full items-center justify-center text-center p-6 bg-slate-50 border-2 border-dashed border-slate-300"
    initial="hidden"
    animate="visible"
  >
    <motion.div
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ type: 'spring', delay: 0.2 }}
    >
      <FontAwesomeIcon icon={icon} className="text-5xl text-slate-400" />
    </motion.div>
    <h3 className="mt-4 text-xl font-bold text-slate-700">{message}</h3>
    <p className="mt-2 text-base text-slate-500 max-w-xs mx-auto">{details}</p>
  </Card>
);

AnimatedPlaceholder.propTypes = {
  icon: PropTypes.object.isRequired,
  message: PropTypes.string.isRequired,
  details: PropTypes.string.isRequired
};

const UnitAndSubtopicSelection = ({
  selectedExam,
  selectedModule,
  setSelectedUnits,
  selectedUnits,
  setSelectedSubtopics,
  selectedSubtopics,
  setTestStarted
}) => {
  const [numQuestions, setNumQuestions] = useState('20');
  const [testDuration, setTestDuration] = useState('30 minutes');
  const [isGeneratingTest, setIsGeneratingTest] = useState(false);
  const [activeUnit, setActiveUnit] = useState(null);

  const [triggerUnits, { data: unitsData, isLoading: isLoadingUnits, error: unitError }] =
    useLazyCreateYourOwnTestExamUnitsQuery();
  const [
    triggerSubtopics,
    { data: subtopicsData, isLoading: isLoadingSubtopics, error: subtopicError }
  ] = useLazyCreateYourOwnTestExamSubtopicsQuery();
  const [startBioTest, { isLoading: isLoadingBioStart }] =
    useCreateYourOwnTestBioStartTestMutation();
  const [startChemistryTest, { isLoading: isLoadingChemistryStart }] =
    useCreateYourOwnTestChemistryStartTestMutation();
  const [startMathTest, { isLoading: isLoadingMathStart }] =
    useCreateYourOwnTestMathStartTestMutation();
  const [startPhysicsTest, { isLoading: isLoadingPhysicsStart }] =
    useCreateYourOwnTestPhysicsStartTestMutation();

  const userId = sessionStorage.getItem('userId');

  useEffect(() => {
    if (selectedExam && selectedModule) {
      triggerUnits({ examName: selectedExam, subject: selectedModule });
      setSelectedUnits([]);
      setSelectedSubtopics([]);
      setActiveUnit(null);
    }
  }, [selectedExam, selectedModule, triggerUnits, setSelectedUnits, setSelectedSubtopics]);

  useEffect(() => {
    if (activeUnit) {
      triggerSubtopics({ examName: selectedExam, module: selectedModule, unit: activeUnit });
    }
  }, [activeUnit, selectedExam, selectedModule, triggerSubtopics]);

  const allUnits = useMemo(() => unitsData?.units || [], [unitsData]);
  const subtopicsForActiveUnit = useMemo(() => subtopicsData?.subtopics || [], [subtopicsData]);

  const handleUnitClick = (unit) => {
    setActiveUnit(unit);
    if (!selectedUnits.includes(unit)) {
      setSelectedUnits((prev) => [...prev, unit]);
    }
  };

  const handleSubtopicToggle = (subtopic) => {
    setSelectedSubtopics((prev) =>
      prev.includes(subtopic) ? prev.filter((item) => item !== subtopic) : [...prev, subtopic]
    );
  };

  const areAllUnitsSelected = allUnits.length > 0 && selectedUnits.length === allUnits.length;
  const handleSelectAllUnits = () => {
    if (areAllUnitsSelected) {
      setSelectedUnits([]);
    } else {
      setSelectedUnits(allUnits);
    }
  };

  const areAllSubtopicsForActiveUnitSelected =
    subtopicsForActiveUnit.length > 0 &&
    subtopicsForActiveUnit.every((s) => selectedSubtopics.includes(s));

  const handleSelectAllSubtopics = () => {
    if (areAllSubtopicsForActiveUnitSelected) {
      // Deselect all subtopics of the current unit
      setSelectedSubtopics((prev) => prev.filter((s) => !subtopicsForActiveUnit.includes(s)));
    } else {
      // Select all subtopics of the current unit, keeping existing selections from other units
      setSelectedSubtopics((prev) => [...new Set([...prev, ...subtopicsForActiveUnit])]);
    }
  };

  const startTest = async () => {
    if (selectedSubtopics.length === 0) {
      toast.error('Please select at least one subtopic.');
      return;
    }
    if (!numQuestions || parseInt(numQuestions) < 1 || parseInt(numQuestions) > 50) {
      toast.error('Please enter a valid number of questions (1-50).');
      return;
    }

    setIsGeneratingTest(true);
    const normalizedSubject = selectedModule.toLowerCase();
    console.log('selectedModule:', selectedModule);
    console.log('normalizedSubject:', normalizedSubject);

    const payload = {
      num_questions: parseInt(numQuestions),
      exam_name: selectedExam,
      unit_name: selectedUnits.join(', '),
      sub_topics: selectedSubtopics.join(', '),
      user_id: userId,
      duration: testDuration,
      subject: normalizedSubject,
      subject_name: selectedModule
      // Include subject in payload
    };

    console.log('startTest payload:', JSON.stringify(payload, null, 2));

    try {
      let result;
      switch (normalizedSubject) {
        case 'biology':
          result = await startBioTest(payload).unwrap();
          break;
        case 'chemistry':
          result = await startChemistryTest(payload).unwrap();
          break;
        case 'mathematics':
        case 'math':
          result = await startMathTest(payload).unwrap();
          break;
        case 'physics':
          result = await startPhysicsTest(payload).unwrap();
          break;
        default:
          throw new Error('Invalid subject selected: ' + normalizedSubject);
      }

      // console.log('API response (result):', JSON.stringify(result, null, 2));

      if (result && result.message?.toLowerCase().includes('test started successfully')) {
        // Explicitly set subject in testData
        const testDataWithSubject = { ...result, subject: normalizedSubject };
        console.log('Setting testData:', JSON.stringify(testDataWithSubject, null, 2));
        useTestStore.getState().setTestData(testDataWithSubject);
        useTestStore.getState().setTestDuration(testDuration);
        setTestStarted(true);
        toast.success('Test created successfully!');
      } else {
        throw new Error(result?.error || 'Unknown error starting test');
      }
    } catch (error) {
      const errorMessage = error.data?.error || error.message || 'An unknown error occurred.';
      console.error('startTest error:', error);
      toast.error(`Error: ${errorMessage}`);
    } finally {
      setIsGeneratingTest(false);
    }
  };
  const isLoading =
    isLoadingUnits ||
    isLoadingBioStart ||
    isLoadingChemistryStart ||
    isLoadingMathStart ||
    isLoadingPhysicsStart;

  const renderLoader = (text, hasContainer = true) => {
    const loader = (
      <div className="flex items-center justify-center p-8 space-x-3 text-lg text-slate-500">
        <FontAwesomeIcon
          icon={faSpinner}
          className="text-2xl text-[var(--color-student)] animate-spin"
        />
        <span>{text}</span>
      </div>
    );
    return hasContainer ? <Card>{loader}</Card> : loader;
  };

  const renderError = (error, message, hasContainer = true) => {
    const errorBox = (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg text-red-700 flex items-center justify-center space-x-3">
        <FontAwesomeIcon icon={faExclamationTriangle} />
        <span>{error?.data?.error || message}</span>
      </div>
    );
    return hasContainer ? <Card>{errorBox}</Card> : errorBox;
  };

  const renderUnitSkeleton = () => (
    <div className="p-4 space-y-3">
      {[...Array(5)].map((_, i) => (
        <div key={i} className="h-12 bg-slate-200 rounded animate-pulse"></div>
      ))}
    </div>
  );

  return (
    <div
      className="min-h-screen bg-slate-50 text-slate-800 p-4 sm:p-6 md:p-8 font-sans flex flex-col relative"
      style={{ '--color-student': '#2563eb' }}
    >
      <CosmicBackground />
      <AnimatePresence>
        {isGeneratingTest && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-slate-900/80 backdrop-blur-sm z-50 flex flex-col items-center justify-center p-6 text-center text-white overflow-hidden"
          >
            {/* Animated background elements */}
            {[...Array(5)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-48 bg-[var(--color-student)]/50"
                style={{
                  left: `${i * 20 + 10}%`,
                  filter: 'blur(4px)'
                }}
                animate={{
                  y: ['-100%', '200%']
                }}
                transition={{
                  duration: 2 + i,
                  repeat: Infinity,
                  ease: 'linear'
                }}
              />
            ))}

            {/* Main content */}
            <motion.div
              className="relative"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.2, type: 'spring' }}
            >
              {/* Icon representing the forge */}
              {/* {[...Array(8)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 bg-yellow-400 rounded-full"
                  style={{
                    top: '10%',
                    right: '70%'
                  }}
                  animate={{
                    x: (Math.random() - 0.5) * 200,
                    y: (Math.random() - 0.5) * 200,
                    opacity: [1, 0],
                    scale: [1, 0]
                  }}
                  transition={{
                    duration: 0.8,
                    repeat: Infinity,
                    delay: i * 0.15 + 0.6
                  }}
                />
              ))} */}
              {/* <FontAwesomeIcon
                icon={faCheckCircle}
                className="text-8xl text-[var(--color-student)] mb-6 animate-spin"
                style={{ animationDuration: '10s' }}
              /> */}
              <ParticleAnimation />

              {/* sparks */}
              {/* {[...Array(8)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 bg-yellow-400 rounded-full"
                  style={{
                    top: '20%',
                    left: '70%'
                  }}
                  animate={{
                    x: (Math.random() - 0.5) * 200,
                    y: (Math.random() - 0.5) * 200,
                    opacity: [1, 0],
                    scale: [1, 0]
                  }}
                  transition={{
                    duration: 0.8,
                    repeat: Infinity,
                    delay: i * 0.15 + 0.6
                  }}
                />
              ))} */}

              <h2 className="text-3xl font-bold mt-15">Generating Your Questions...</h2>
              <p className="text-slate-300 mt-2 max-w-sm">
                Crafting a unique test from {selectedSubtopics.length} topics with {numQuestions}{' '}
                questions. Please wait a moment.
              </p>

              {/* Progress bar */}
              {/* 
              <div className="w-full max-w-xs mx-auto mt-6 h-2 bg-slate-700 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-[var(--color-student)]"
                  initial={{ x: '-100%' }}
                  animate={{ x: '100%' }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: 'easeInOut',
                    repeatType: 'mirror'
                  }}
                />
              </div> */}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <header className="relative z-10 mb-8">
        <h1 className="text-3xl md:text-4xl font-extrabold text-[var(--color-student)] tracking-tight">
          Test Builder
        </h1>
        <p className="mt-2 text-lg text-slate-600">
          Craft your personalized practice test by following the steps below.
        </p>
      </header>

      <main className="relative z-10 flex-1 grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
        {/* Units Column */}
        <Card className="flex flex-col" initial="hidden" animate="visible">
          <header className="p-4 border-b border-slate-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1, rotate: 360 }}
                  transition={{ duration: 0.7, type: 'spring' }}
                >
                  <FontAwesomeIcon
                    icon={faLayerGroup}
                    className="text-2xl text-[var(--color-student)]"
                  />
                </motion.div>
                <div>
                  <h2 className="text-xl font-bold text-slate-900">Step 1: Select Units</h2>
                  <p className="text-sm text-slate-500">Click a unit to see its subtopics.</p>
                </div>
              </div>
              {!isLoadingUnits && allUnits.length > 0 && (
                <div className="flex items-center pr-2">
                  <input
                    type="checkbox"
                    id="select-all-units"
                    checked={areAllUnitsSelected}
                    onChange={handleSelectAllUnits}
                    className="h-4 w-4 rounded border-slate-300 text-[var(--color-student)] focus:ring-[var(--color-student)] cursor-pointer"
                  />
                  <label
                    htmlFor="select-all-units"
                    className="ml-2 text-sm font-medium text-slate-600 cursor-pointer"
                  >
                    All
                  </label>
                </div>
              )}
            </div>
          </header>
          <div className="flex-1 p-4 space-y-2 overflow-y-auto">
            {isLoadingUnits
              ? renderUnitSkeleton()
              : unitError
                ? renderError(unitError, 'Failed to load units.', false)
                : allUnits.map((unit, index) => (
                    <motion.button
                      key={unit}
                      onClick={() => handleUnitClick(unit)}
                      className={`w-full text-left p-3 rounded-md transition-all duration-200 flex justify-between items-center
                      ${
                        activeUnit === unit
                          ? 'bg-[var(--color-student)] text-white shadow-lg'
                          : `text-slate-700 hover:bg-slate-100 ${
                              selectedUnits.includes(unit) ? 'bg-blue-100' : 'bg-white'
                            }`
                      }`}
                      variants={listItemVariants}
                      initial="hidden"
                      animate="visible"
                      transition={{ delay: index * 0.05 }}
                    >
                      <div className="flex items-center">
                        <FontAwesomeIcon
                          icon={faBookmark}
                          className="mr-3 text-[var(--color-student)]/70"
                        />
                        <span className="font-semibold">{unit}</span>
                      </div>
                      <FontAwesomeIcon
                        icon={faChevronRight}
                        className={`transition-transform ${activeUnit === unit ? 'translate-x-1' : ''}`}
                      />
                    </motion.button>
                  ))}
          </div>
        </Card>

        {/* Subtopics Column */}
        <AnimatePresence mode="wait">
          {activeUnit ? (
            <motion.div
              key="subtopics-loaded"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="h-full"
            >
              <Card className="flex flex-col h-full">
                <header className="p-4 border-b border-slate-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.1 }}
                      >
                        <FontAwesomeIcon
                          icon={faSitemap}
                          className="text-2xl text-[var(--color-student)]"
                        />
                      </motion.div>
                      <div>
                        <h2 className="text-xl font-bold text-slate-900 truncate">
                          Step 2: Choose Subtopics
                        </h2>
                        <p className="text-sm text-slate-500">
                          For unit:{' '}
                          <span className="font-semibold text-[var(--color-student)]">
                            {activeUnit}
                          </span>
                        </p>
                      </div>
                    </div>
                    {!isLoadingSubtopics && subtopicsForActiveUnit.length > 0 && (
                      <div className="flex items-center pr-2">
                        <input
                          type="checkbox"
                          id="select-all-subtopics"
                          checked={areAllSubtopicsForActiveUnitSelected}
                          onChange={handleSelectAllSubtopics}
                          className="h-4 w-4 rounded border-slate-300 text-[var(--color-student)] focus:ring-[var(--color-student)] cursor-pointer"
                        />
                        <label
                          htmlFor="select-all-subtopics"
                          className="ml-2 text-sm font-medium text-slate-600 cursor-pointer"
                        >
                          All
                        </label>
                      </div>
                    )}
                  </div>
                </header>
                <div className="flex-1 p-4 overflow-y-auto">
                  {isLoadingSubtopics ? (
                    renderLoader('Loading subtopics...', false)
                  ) : subtopicError ? (
                    renderError(subtopicError, 'Failed to load subtopics.', false)
                  ) : (
                    <div className="space-y-3">
                      {subtopicsForActiveUnit.map((subtopic, index) => (
                        <motion.label
                          key={subtopic}
                          className="flex items-center p-3 rounded-md hover:bg-slate-100 transition-colors cursor-pointer"
                          variants={listItemVariants}
                          initial="hidden"
                          animate="visible"
                          transition={{ delay: index * 0.05 }}
                          whileHover={{ scale: 1.03 }}
                        >
                          <input
                            type="checkbox"
                            checked={selectedSubtopics.includes(subtopic)}
                            onChange={() => handleSubtopicToggle(subtopic)}
                            className="h-5 w-5 rounded border-slate-300 text-[var(--color-student)] focus:ring-[var(--color-student)]"
                          />
                          <FontAwesomeIcon icon={faClipboardList} className="mx-3 text-slate-400" />
                          <span className="text-slate-700 font-medium">{subtopic}</span>
                        </motion.label>
                      ))}
                    </div>
                  )}
                </div>
              </Card>
            </motion.div>
          ) : (
            <motion.div key="subtopics-placeholder">
              <AnimatedPlaceholder
                icon={faBookOpen}
                message="Dive Deeper into Your Subject"
                details="Select a unit on the left to explore its detailed subtopics and add them to your custom test."
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Configuration Column */}
        <Card
          className="flex flex-col"
          initial="hidden"
          animate="visible"
          transition={{ delay: 0.2 }}
        >
          <header className="p-4 border-b border-slate-200 flex items-center gap-3">
            <motion.div initial={{ scale: 0 }} animate={{ scale: 1 }} transition={{ delay: 0.2 }}>
              <FontAwesomeIcon icon={faSlidersH} className="text-2xl text-[var(--color-student)]" />
            </motion.div>
            <div>
              <h2 className="text-xl font-bold text-slate-900">Step 3: Configure Test</h2>
              <p className="text-sm text-slate-500">Finalize your test parameters.</p>
            </div>
          </header>
          <div className="flex-1 p-4 space-y-6 overflow-y-auto">
            <div className="space-y-4">
              <div>
                <h3 className="flex items-center gap-2 font-semibold text-[var(--color-student)] mb-2">
                  <FontAwesomeIcon icon={faLayerGroup} className="text-[var(--color-student)]" />
                  Selected Units ({selectedUnits.length})
                </h3>
                <div className="text-sm space-y-1 text-slate-700 max-h-20 overflow-y-auto border p-2 rounded-md bg-slate-50">
                  {selectedUnits.length > 0 ? (
                    selectedUnits.map((u) => (
                      <p key={u} className="truncate p-1">
                        {u}
                      </p>
                    ))
                  ) : (
                    <p className="text-slate-400 p-1">None selected</p>
                  )}
                </div>
              </div>
              <div>
                <h3 className="flex items-center gap-2 font-semibold text-[var(--color-student)] mb-2">
                  <FontAwesomeIcon icon={faSitemap} className="text-[var(--color-student)]" />
                  Selected Subtopics ({selectedSubtopics.length})
                </h3>
                <div className="text-sm space-y-1 text-slate-700 max-h-28 overflow-y-auto border p-2 rounded-md bg-slate-50">
                  {selectedSubtopics.length > 0 ? (
                    selectedSubtopics.map((s) => (
                      <p key={s} className="truncate p-1">
                        {s}
                      </p>
                    ))
                  ) : (
                    <p className="text-slate-400 p-1">None selected</p>
                  )}
                </div>
              </div>
            </div>
            <hr className="border-slate-200" />
            <div>
              <label
                htmlFor="numQuestions"
                className="flex items-center gap-2 font-semibold text-[var(--color-student)] mb-2"
              >
                <FontAwesomeIcon icon={faQuestionCircle} className="text-[var(--color-student)]" />
                Number of Questions
              </label>
              <input
                id="numQuestions"
                type="number"
                value={numQuestions}
                onChange={(e) => setNumQuestions(e.target.value)}
                min="1"
                max="50"
                className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg focus:ring-1 focus:ring-[var(--color-student)] focus:border-[var(--color-student)]"
              />
              <p className="text-xs text-slate-500 mt-1">Please enter a number between 1 and 50.</p>
            </div>
            <div>
              <label
                htmlFor="testDuration"
                className="flex items-center gap-2 font-semibold text-[var(--color-student)] mb-2"
              >
                <FontAwesomeIcon icon={faClock} className="text-[var(--color-student)]" />
                Test Duration
              </label>
              <select
                id="testDuration"
                value={testDuration}
                onChange={(e) => setTestDuration(e.target.value)}
                className="w-full px-3 py-2 bg-white border border-slate-300 rounded-lg focus:ring-1 focus:ring-[var(--color-student)] focus:border-[var(--color-student)]"
              >
                {['30 minutes', '45 minutes', '1 hour', '1.5 hours', '2 hours'].map((duration) => (
                  <option key={duration} value={duration}>
                    {duration}
                  </option>
                ))}
              </select>
            </div>
            <div className="mt-auto pt-4">
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg flex items-start gap-3">
                <FontAwesomeIcon icon={faLightbulb} className="text-blue-500 mt-1" />
                <div>
                  <h4 className="font-bold text-blue-800">Pro Tip</h4>
                  <p className="text-xs text-blue-700">
                    Use the 'All' checkbox in the headers to quickly select all units or subtopics.
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="p-4 border-t border-slate-200">
            <motion.button
              onClick={startTest}
              disabled={isLoading || selectedSubtopics.length === 0}
              className="w-full py-3 rounded-lg hover:cursor-pointer bg-[var(--color-student)] font-bold text-white disabled:bg-slate-300 disabled:text-slate-500 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              whileHover={{ scale: 1.02, boxShadow: '0 4px 15px rgba(37, 99, 235, 0.4)' }}
            >
              {isLoading ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} spin />
                  Processing...
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faCheckCircle} />
                  Create Test
                </>
              )}
            </motion.button>
          </div>
        </Card>
      </main>
    </div>
  );
};

UnitAndSubtopicSelection.propTypes = {
  selectedExam: PropTypes.string.isRequired,
  selectedModule: PropTypes.string.isRequired,
  setSelectedUnits: PropTypes.func.isRequired,
  selectedUnits: PropTypes.arrayOf(PropTypes.string).isRequired,
  setSelectedSubtopics: PropTypes.func.isRequired,
  selectedSubtopics: PropTypes.arrayOf(PropTypes.string).isRequired,
  setTestStarted: PropTypes.func.isRequired
};

export default UnitAndSubtopicSelection;
