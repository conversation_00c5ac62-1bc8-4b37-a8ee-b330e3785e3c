/**
 * BULLETPROOF WebSocket Override - Google Meet Level
 * Prevents WebSocket disconnections during tab switching
 * Overrides native WebSocket behavior for maximum stability
 */

class BulletproofWebSocket {
  constructor() {
    this.originalWebSocket = null;
    this.activeConnections = new Map();
    this.isActive = false;
    this.keepAliveIntervals = new Map();
  }

  // Initialize bulletproof WebSocket protection
  initialize() {
    if (this.isActive) return;
    
    console.log('🛡️ Initializing BULLETPROOF WebSocket protection...');
    
    // Store original WebSocket
    this.originalWebSocket = window.WebSocket;
    
    // Override WebSocket constructor
    window.WebSocket = this.createBulletproofWebSocket();
    
    this.isActive = true;
    console.log('✅ BULLETPROOF WebSocket protection active');
  }

  // Restore original WebSocket
  restore() {
    if (!this.isActive || !this.originalWebSocket) return;
    
    console.log('🔄 Restoring original WebSocket...');
    
    // Clear all keep-alive intervals
    this.keepAliveIntervals.forEach(interval => clearInterval(interval));
    this.keepAliveIntervals.clear();
    
    // Restore original WebSocket
    window.WebSocket = this.originalWebSocket;
    
    this.isActive = false;
    console.log('✅ Original WebSocket restored');
  }

  // Create bulletproof WebSocket class
  createBulletproofWebSocket() {
    const self = this;
    
    return class BulletproofWebSocketClass extends self.originalWebSocket {
      constructor(url, protocols) {
        super(url, protocols);
        
        this.bulletproofId = Date.now() + Math.random();
        this.isLiveKitConnection = url.includes('livekit') || url.includes('ws://') || url.includes('wss://');
        this.lastActivity = Date.now();
        this.keepAliveCount = 0;
        
        console.log('🔗 BULLETPROOF WebSocket created:', { url, isLiveKit: this.isLiveKitConnection });
        
        // Store connection
        self.activeConnections.set(this.bulletproofId, this);
        
        // Set up bulletproof protection for LiveKit connections
        if (this.isLiveKitConnection) {
          this.setupBulletproofProtection();
        }
        
        // Override close method to prevent unwanted closures
        const originalClose = this.close;
        this.close = function(code, reason) {
          console.log('🔌 BULLETPROOF WebSocket close requested:', { code, reason, url });
          
          // Only allow intentional closures
          if (code === 1000 || reason === 'intentional') {
            self.activeConnections.delete(this.bulletproofId);
            if (self.keepAliveIntervals.has(this.bulletproofId)) {
              clearInterval(self.keepAliveIntervals.get(this.bulletproofId));
              self.keepAliveIntervals.delete(this.bulletproofId);
            }
            return originalClose.call(this, code, reason);
          } else {
            console.log('🛡️ BULLETPROOF: Preventing unwanted WebSocket closure');
            // Don't actually close, just log the attempt
          }
        };
      }
      
      setupBulletproofProtection() {
        console.log('🛡️ Setting up BULLETPROOF protection for LiveKit WebSocket...');
        
        // Monitor connection state
        this.addEventListener('open', () => {
          console.log('✅ BULLETPROOF WebSocket opened');
          this.startAggressiveKeepAlive();
        });
        
        this.addEventListener('close', (event) => {
          console.log('❌ BULLETPROOF WebSocket closed:', event.code, event.reason);
          
          // Clear keep-alive
          if (self.keepAliveIntervals.has(this.bulletproofId)) {
            clearInterval(self.keepAliveIntervals.get(this.bulletproofId));
            self.keepAliveIntervals.delete(this.bulletproofId);
          }
          
          // Remove from active connections
          self.activeConnections.delete(this.bulletproofId);
        });
        
        this.addEventListener('error', (error) => {
          console.error('❌ BULLETPROOF WebSocket error:', error);
        });
        
        this.addEventListener('message', () => {
          this.lastActivity = Date.now();
        });
      }
      
      startAggressiveKeepAlive() {
        // Clear existing keep-alive
        if (self.keepAliveIntervals.has(this.bulletproofId)) {
          clearInterval(self.keepAliveIntervals.get(this.bulletproofId));
        }
        
        const keepAliveInterval = setInterval(() => {
          if (this.readyState === WebSocket.OPEN) {
            try {
              // Send multiple types of keep-alive messages
              
              // 1. Empty ping
              this.send('');
              
              // 2. JSON ping
              this.send(JSON.stringify({
                type: 'bulletproof_ping',
                timestamp: Date.now(),
                count: this.keepAliveCount++,
                background: document.hidden
              }));
              
              // 3. Binary ping
              const buffer = new ArrayBuffer(8);
              const view = new DataView(buffer);
              view.setUint32(0, Date.now() & 0xFFFFFFFF);
              view.setUint32(4, this.keepAliveCount);
              this.send(buffer);
              
              console.log('💓 BULLETPROOF keep-alive sent (count:', this.keepAliveCount, 'background:', document.hidden, ')');
              
              this.lastActivity = Date.now();
            } catch (error) {
              console.warn('⚠️ BULLETPROOF keep-alive failed:', error);
            }
          } else {
            console.warn('⚠️ BULLETPROOF WebSocket not open for keep-alive:', this.readyState);
          }
        }, document.hidden ? 500 : 5000); // EXTREMELY frequent in background (500ms)
        
        self.keepAliveIntervals.set(this.bulletproofId, keepAliveInterval);
      }
      
      // Override send to track activity
      send(data) {
        this.lastActivity = Date.now();
        return super.send(data);
      }
    };
  }

  // Force keep-alive for all active connections
  forceKeepAlive() {
    this.activeConnections.forEach((ws, id) => {
      if (ws.readyState === WebSocket.OPEN && ws.isLiveKitConnection) {
        try {
          ws.send(JSON.stringify({
            type: 'force_keep_alive',
            timestamp: Date.now(),
            background: document.hidden
          }));
        } catch (error) {
          console.warn('⚠️ Force keep-alive failed for connection:', id, error);
        }
      }
    });
  }

  // Get connection statistics
  getStats() {
    const stats = {
      totalConnections: this.activeConnections.size,
      livekitConnections: 0,
      openConnections: 0,
      backgroundMode: document.hidden
    };
    
    this.activeConnections.forEach(ws => {
      if (ws.isLiveKitConnection) {
        stats.livekitConnections++;
      }
      if (ws.readyState === WebSocket.OPEN) {
        stats.openConnections++;
      }
    });
    
    return stats;
  }
}

// Create singleton instance
const bulletproofWebSocket = new BulletproofWebSocket();

// Auto-initialize when module loads
if (typeof window !== 'undefined') {
  // Initialize after a short delay to ensure other scripts are loaded
  setTimeout(() => {
    bulletproofWebSocket.initialize();
    
    // Set up page visibility monitoring
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        console.log('🛡️ Page hidden - activating BULLETPROOF mode');
        bulletproofWebSocket.forceKeepAlive();
      } else {
        console.log('✅ Page visible - BULLETPROOF mode active');
      }
    });
    
    // Periodic stats logging
    setInterval(() => {
      const stats = bulletproofWebSocket.getStats();
      if (stats.livekitConnections > 0) {
        console.log('📊 BULLETPROOF WebSocket stats:', stats);
      }
    }, 30000);
    
  }, 1000);
}

export default bulletproofWebSocket;
