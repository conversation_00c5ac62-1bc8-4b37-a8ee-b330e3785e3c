// // Import the functions you need from the SDKs you need
// import { initializeApp } from "firebase/app";
// import { getMessaging } from "firebase/messaging";

// // TODO: Add SDKs for Firebase products that you want to use
// // https://firebase.google.com/docs/web/setup#available-libraries

// // Your web app's Firebase configuration
// // For Firebase JS SDK v7.20.0 and later, measurementId is optional
// const firebaseConfig = {
//   apiKey: "AIzaSyDT7404eSoYJCMNtgY2xd_ZqEhzjAA2eUo",
//   authDomain: "sasthra-3a69e.firebaseapp.com",
//   projectId: "sasthra-3a69e",
//   storageBucket: "sasthra-3a69e.firebasestorage.app",
//   messagingSenderId: "119837146788",
//   appId: "1:119837146788:web:6f3c60365aadd3ad677031",
//   measurementId: "G-FYJXQ4S2VT"
// };

// // Initialize Firebase
// const app = initializeApp(firebaseConfig);
// // Initialize Firebase Cloud Messaging and get a reference to the service
// const messaging = getMessaging(app);

// src/notification/firebase.js
import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: 'AIzaSyDT7404eSoYJCMNtgY2xd_ZqEhzjAA2eUo',
  authDomain: 'sasthra-3a69e.firebaseapp.com',
  projectId: 'sasthra-3a69e',
  storageBucket: 'sasthra-3a69e.firebasestorage.app',
  messagingSenderId: '119837146788',
  appId: '1:119837146788:web:6f3c60365aadd3ad677031',
  measurementId: 'G-FYJXQ4S2VT'
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
// Initialize Firebase Cloud Messaging and get a reference to the service
const messaging = getMessaging(app);

// Export messaging, getToken, and onMessage
export { messaging, getToken, onMessage };
