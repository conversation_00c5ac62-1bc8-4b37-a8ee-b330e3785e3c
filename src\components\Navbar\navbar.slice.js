import { createSlice } from '@reduxjs/toolkit';
import { menuApi } from '../../redux/api/api';

const initialState = {
  menuData: null
};

export const menuApiSlice = menuApi.injectEndpoints({
  endpoints: (builder) => ({
    getRoleBasedMenuService: builder.query({
      query: (query) => {
        return `/menu/${query}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Menu']
    })
  })
});

const menuSlice = createSlice({
  name: 'menu',
  initialState,
  reducers: {
    setMenuData(state, action) {
      state.menuData = action.payload;
    },
    clearMenuData(state) {
      state.menuData = null;
    }
  }
});

export const { useLazyGetRoleBasedMenuServiceQuery } = menuApiSlice;
export const { setMenuData, clearMenuData } = menuSlice.actions;
export const selectMenuData = (state) => state.menu.menuData;
export default menuSlice.reducer;
