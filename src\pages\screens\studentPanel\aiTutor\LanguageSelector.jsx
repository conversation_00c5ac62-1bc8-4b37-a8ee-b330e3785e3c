import React, { useState } from 'react';
import axios from 'axios';
import { motion, AnimatePresence } from 'framer-motion';
import { Globe, Rocket, CheckCircle, AlertCircle, Languages } from 'lucide-react';
import TeachingLoadingSequence from './TeachingLoadingSequence';

function LanguageSelector({ processId, subject, onComplete }) {
  const [selectedLanguage, setSelectedLanguage] = useState('english');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [showLaunchSequence, setShowLaunchSequence] = useState(false);
  const [hoveredLanguage, setHoveredLanguage] = useState(null);

  const languages = [
    { id: 'english', name: 'English', color: '#3b82f6', emoji: '🇬🇧', bg: 'bg-blue-500/10' },
    {
      id: 'thanglish',
      name: 'Tamil + English',
      color: '#f59e0b',
      emoji: '🇮🇳',
      bg: 'bg-amber-500/10'
    },
    {
      id: 'teluglish',
      name: 'Telugu + English',
      color: '#10b981',
      emoji: '🇮🇳',
      bg: 'bg-emerald-500/10'
    },
    {
      id: 'kanglish',
      name: 'Kannada + English',
      color: '#8b5cf6',
      emoji: '🇮🇳',
      bg: 'bg-purple-500/10'
    },
    {
      id: 'manglish',
      name: 'Malayalam + English',
      color: '#ef4444',
      emoji: '🇮🇳',
      bg: 'bg-red-500/10'
    },
    {
      id: 'hinglish',
      name: 'Hindi + English',
      color: '#f97316',
      emoji: '🇮🇳',
      bg: 'bg-orange-500/10'
    }
  ];

  const endpoints = {
    Mathematics: 'https://sasthra.in/mathapi/explain_slides',
    Chemistry: 'https://sasthra.in/chemapi/explain_slides',
    Physics: 'https://sasthra.in/physics/explain_slides',
    Biology: 'https://sasthra.in/bioapi/explain_slides'
  };

  const handleStartClass = async () => {
    setShowLaunchSequence(true);
    const apiUrl = endpoints[subject];

    if (!apiUrl) {
      const errMsg = `No endpoint defined for subject: ${subject}`;
      setMessage(errMsg);
      setShowLaunchSequence(false);
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      const payload = {
        process_selector_id: processId,
        language: selectedLanguage
      };

      const response = await axios.post(apiUrl, payload);

      if (response.status === 200) {
        setMessage('Class started successfully!');
        const pdfUrl = response.data.url || 'https://example.com/default.pdf';
        setTimeout(() => {
          onComplete(pdfUrl);
          setShowLaunchSequence(false);
        }, 1500);
      } else {
        setMessage('Failed to start class.');
        setShowLaunchSequence(false);
      }
    } catch (error) {
      console.error('API Error:', error);
      setMessage('Error starting class.');
      setShowLaunchSequence(false);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-64 h-64 rounded-full bg-blue-500/5 blur-3xl animate-float"></div>
        <div className="absolute bottom-0 right-0 w-64 h-64 rounded-full bg-purple-500/5 blur-3xl animate-float-delay"></div>
      </div>

      <AnimatePresence>
        {showLaunchSequence && (
          <TeachingLoadingSequence showLaunchSequence={showLaunchSequence} subject={subject} />
        )}
      </AnimatePresence>

      <motion.div
        className="w-full max-w-3xl bg-gray-900/80 backdrop-blur-lg rounded-2xl border border-gray-800 shadow-2xl overflow-hidden relative z-10"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        {/* Header */}
        <div className="p-8 pb-0">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-3 rounded-xl bg-blue-600/20 border border-blue-500/30">
                <Languages className="text-blue-400 w-6 h-6" />
              </div>
              <h2 className="text-2xl font-bold text-white">Language Selection</h2>
            </div>
            <div className="px-3 py-1.5 rounded-full bg-gray-800 border border-gray-700 text-sm font-medium text-gray-300">
              {subject}
            </div>
          </div>

          <p className="text-gray-400 mb-8">
            Select your preferred language for {subject.toLowerCase()} instruction. The content will
            be delivered in a mix of your chosen language and English.
          </p>
        </div>

        {/* Interactive language selector */}
        <div className="px-8 pb-8">
          <div className="relative">
            {/* Active language highlight */}
            {hoveredLanguage && (
              <motion.div
                className={`absolute inset-0 rounded-xl ${hoveredLanguage.bg} border border-gray-700/50`}
                layoutId="languageHighlight"
                initial={false}
                transition={{ type: 'spring', stiffness: 300, damping: 30 }}
              />
            )}

            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 relative z-10">
              {languages.map((lang) => (
                <motion.button
                  key={lang.id}
                  onHoverStart={() => setHoveredLanguage(lang)}
                  onHoverEnd={() => setHoveredLanguage(null)}
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.98 }}
                  className={`p-4 rounded-xl transition-all duration-200 flex flex-col items-center justify-center border ${
                    selectedLanguage === lang.id
                      ? `border-[${lang.color}] bg-[${lang.color}]/10 shadow-lg`
                      : 'border-gray-700 bg-gray-800/50'
                  }`}
                  onClick={() => setSelectedLanguage(lang.id)}
                  style={{
                    borderColor: selectedLanguage === lang.id ? lang.color : ''
                  }}
                >
                  <span className="text-3xl text-white mb-3">{lang.emoji}</span>
                  <span
                    className={`font-medium ${
                      selectedLanguage === lang.id ? `text-[${lang.color}]` : 'text-gray-300'
                    }`}
                    style={{
                      color: selectedLanguage === lang.id ? lang.color : ''
                    }}
                  >
                    {lang.name}
                  </span>
                  {selectedLanguage === lang.id && (
                    <motion.div
                      className="w-3 h-3  rounded-full mt-2"
                      style={{ backgroundColor: lang.color }}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: 'spring', stiffness: 500 }}
                    />
                  )}
                </motion.button>
              ))}
            </div>
          </div>
        </div>

        {/* Action section */}
        <div className="px-8 pb-8">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <button
              onClick={handleStartClass}
              disabled={loading}
              className={`w-full py-4 rounded-xl  font-semibold transition-all duration-300 relative overflow-hidden group ${
                loading
                  ? 'bg-gray-700 cursor-not-allowed'
                  : 'bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-700'
              }`}
            >
              <div className="relative z-10 flex items-center justify-center space-x-3">
                {loading ? (
                  <>
                    <svg className="animate-spin h-5 w-5 text-white" viewBox="0 0 24 24">
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                        fill="none"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
                      />
                    </svg>
                    <span>Preparing your session...</span>
                  </>
                ) : (
                  <>
                    <Rocket className="w-5 h-5" />
                    <span>Start Learning Session</span>
                  </>
                )}
              </div>
              {!loading && (
                <span className="absolute inset-0 bg-gradient-to-r from-blue-500/30 to-indigo-500/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              )}
            </button>
          </motion.div>

          {/* Status message */}
          {message && (
            <motion.div
              className={`mt-4 p-3 rounded-lg text-center ${
                message.includes('success')
                  ? 'bg-emerald-900/30 text-emerald-400 border border-emerald-400/20'
                  : 'bg-rose-900/30 text-rose-400 border border-rose-400/20'
              }`}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              {message.includes('success') ? (
                <div className="flex items-center justify-center space-x-2">
                  <CheckCircle className="w-5 h-5" />
                  <span>{message}</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <AlertCircle className="w-5 h-5" />
                  <span>{message}</span>
                </div>
              )}
            </motion.div>
          )}
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-blue-600/10 rounded-full blur-xl -mr-16 -mt-16"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-purple-600/10 rounded-full blur-xl -ml-16 -mb-16"></div>
      </motion.div>

      {/* Floating particles */}
      {[...Array(20)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full pointer-events-none"
          style={{
            width: `${Math.random() * 6 + 2}px`,
            height: `${Math.random() * 6 + 2}px`,
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            background: `hsla(${Math.random() * 60 + 200}, 80%, 70%, ${Math.random() * 0.2 + 0.1})`,
            zIndex: 0
          }}
          animate={{
            y: [0, (Math.random() - 0.5) * 60],
            x: [0, (Math.random() - 0.5) * 40],
            opacity: [0.1, 0.6, 0.1]
          }}
          transition={{
            duration: 5 + Math.random() * 10,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: Math.random() * 2
          }}
        />
      ))}
    </div>
  );
}

export default LanguageSelector;
