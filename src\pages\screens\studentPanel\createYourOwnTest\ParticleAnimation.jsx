const ParticleAnimation = () => {
  return (
    <div style={{}}>
      <style jsx>{`
        @keyframes rotate-one {
          0% {
            transform: rotateX(35deg) rotateY(-45deg) rotateZ(0deg);
          }
          100% {
            transform: rotateX(35deg) rotateY(-45deg) rotateZ(360deg);
          }
        }
        @keyframes rotate-two {
          0% {
            transform: rotateX(50deg) rotateY(10deg) rotateZ(0deg);
          }
          100% {
            transform: rotateX(50deg) rotateY(10deg) rotateZ(360deg);
          }
        }
        @keyframes rotate-three {
          0% {
            transform: rotateX(35deg) rotateY(55deg) rotateZ(0deg);
          }
          100% {
            transform: rotateX(35deg) rotateY(55deg) rotateZ(360deg);
          }
        }
      `}</style>

      <div className="absolute top-0   left-1/2 -translate-x-1/2 -translate-y-1/2 w-26 h-26 rounded-full perspective-[800px]">
        <div className="absolute inset-0 rounded-full border-b-5 border-blue-500 animate-[rotate-one_1s_linear_infinite]" />
        <div className="absolute inset-0 rounded-full border-r-5 border-blue-500 animate-[rotate-two_1s_linear_infinite]" />
        <div className="absolute inset-0 rounded-full border-t-5 border-blue-500 animate-[rotate-three_1s_linear_infinite]" />
      </div>
    </div>
  );
};

export default ParticleAnimation;
