import React, { useState } from 'react';
import { Pie } from 'react-chartjs-2';
import { BarChart3, Table, TrendingUp, BookOpen, Award, ChevronDown } from 'lucide-react';
import { Chart as ChartJS, ArcElement, Title, Tooltip, Legend } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { motion, AnimatePresence } from 'framer-motion';
import data from '../boosterModule/data.json';

ChartJS.register(ArcElement, Title, Tooltip, Legend, ChartDataLabels);

const BoosterModule = () => {
  const [selectedSubject, setSelectedSubject] = useState('Physics');
  const [selectedYear, setSelectedYear] = useState('2024');
  const [viewType, setViewType] = useState('chart');

  const subjects = Object.keys(data);
  const years = ['2024', '2023', '2022', '2021'];

  const handleSubjectChange = (e) => setSelectedSubject(e.target.value);
  const handleYearChange = (e) => setSelectedYear(e.target.value);
  const toggleView = () => setViewType(viewType === 'chart' ? 'table' : 'chart');

  const chapters = data[selectedSubject] || [];
  const totalQuestions = chapters.reduce((sum, chapter) => {
    const questions = chapter.NEET[selectedYear];
    return sum + (questions !== null && questions !== undefined ? questions : 0);
  }, 0);

  const generateColors = (count) => {
    const colors = [
      'rgba(79, 172, 254, 0.8)',
      'rgba(255, 107, 107, 0.8)',
      'rgba(72, 207, 173, 0.8)',
      'rgba(255, 182, 72, 0.8)',
      'rgba(162, 155, 254, 0.8)',
      'rgba(255, 159, 243, 0.8)',
      'rgba(34, 197, 94, 0.8)',
      'rgba(239, 68, 68, 0.8)',
      'rgba(168, 85, 247, 0.8)',
      'rgba(245, 158, 11, 0.8)'
    ];
    const additionalColors = [];
    for (let i = colors.length; i < count; i++) {
      const hue = (i * 137.508) % 360;
      additionalColors.push(`hsla(${hue}, 70%, 60%, 0.8)`);
    }
    return [...colors, ...additionalColors].slice(0, count);
  };

  const pieChartData = {
    labels: chapters.map((chapter) => chapter.Chapter_name),
    datasets: [
      {
        label: `Questions in ${selectedYear}`,
        data: chapters.map((chapter) =>
          chapter.NEET[selectedYear] !== null && chapter.NEET[selectedYear] !== undefined
            ? chapter.NEET[selectedYear]
            : 0
        ),
        backgroundColor: generateColors(chapters.length),
        borderColor: generateColors(chapters.length).map((color) => color.replace('0.8', '1')),
        borderWidth: 3
      }
    ]
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      title: { display: false },
      tooltip: {
        callbacks: {
          label: function (context) {
            const value = context.parsed;
            const percentage = totalQuestions > 0 ? ((value / totalQuestions) * 100).toFixed(1) : 0;
            return `${context.label}: ${value} questions (${percentage}%)`;
          }
        },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8
      },
      datalabels: {
        display: true,
        color: 'white',
        font: { weight: 'bold', size: 10, family: 'Inter, sans-serif' },
        formatter: function (value, context) {
          if (value === 0) return '';
          const percentage = totalQuestions > 0 ? ((value / totalQuestions) * 100).toFixed(1) : 0;
          return percentage >= 5 ? percentage + '%' : '';
        },
        anchor: 'center',
        align: 'center'
      }
    }
  };

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <motion.div
        className="bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-100"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <motion.div
              className="flex items-center space-x-2 sm:space-x-3"
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <motion.div
                className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: 'spring', stiffness: 300 }}
              >
                <BookOpen className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
              </motion.div>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
                  NEET Analysis Dashboard
                </h1>
                <p className="text-xs sm:text-sm text-gray-600">
                  Comprehensive chapter-wise question analysis
                </p>
              </div>
            </motion.div>

            {/* View Toggle */}
            <motion.div
              className="flex items-center bg-gray-100/80 backdrop-blur-sm rounded-lg p-1"
              initial={{ x: 20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <motion.button
                onClick={() => setViewType('chart')}
                className={`flex items-center space-x-1 sm:space-x-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-md text-xs sm:text-sm font-medium transition-all ${
                  viewType === 'chart'
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <BarChart3 className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                <span>Chart View</span>
              </motion.button>
              <motion.button
                onClick={() => setViewType('table')}
                className={`flex items-center space-x-1 sm:space-x-2 px-3 sm:px-4 py-1.5 sm:py-2 rounded-md text-xs sm:text-sm font-medium transition-all ${
                  viewType === 'table'
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Table className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                <span>Table View</span>
              </motion.button>
            </motion.div>
          </div>
        </div>
      </motion.div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* Controls */}
        <motion.div
          className="bg-white/50 backdrop-blur-lg rounded-2xl shadow-sm border border-gray-100/50 p-4 sm:p-6 mb-6 sm:mb-8"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6">
            <motion.div
              className="flex items-center space-x-2 sm:space-x-3"
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
            >
              <Award className="w-4 h-4 sm:w-5 sm:h-5 text-purple-500" />
              <span className="text-xs sm:text-sm font-medium text-gray-700">Filters</span>
            </motion.div>

            <div className="flex flex-wrap gap-4">
              <motion.div
                className="relative w-full sm:w-auto"
                whileHover={{ y: -2 }}
                transition={{ type: 'spring', stiffness: 300 }}
              >
                <label className="block text-xs font-medium text-gray-500 mb-1">Subject</label>
                <div className="relative">
                  <select
                    value={selectedSubject}
                    onChange={handleSubjectChange}
                    className="appearance-none bg-gradient-to-r from-purple-50/80 to-blue-50/80 border border-purple-200/50 rounded-xl px-3 sm:px-4 py-2 sm:py-3 pr-10 text-xs sm:text-sm font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all w-full sm:w-48 backdrop-blur-sm"
                  >
                    {subjects.map((subject) => (
                      <option key={subject} value={subject}>
                        {subject}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 sm:w-4 sm:h-4 text-gray-500" />
                </div>
              </motion.div>

              <motion.div
                className="relative w-full sm:w-auto"
                whileHover={{ y: -2 }}
                transition={{ type: 'spring', stiffness: 300 }}
              >
                <label className="block text-xs font-medium text-gray-500 mb-1">Year</label>
                <div className="relative">
                  <select
                    value={selectedYear}
                    onChange={handleYearChange}
                    className="appearance-none bg-gradient-to-r from-purple-50/80 to-blue-50/80 border border-purple-200/50 rounded-xl px-3 sm:px-4 py-2 sm:py-3 pr-10 text-xs sm:text-sm font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all w-full sm:w-32 backdrop-blur-sm"
                  >
                    {years.map((year) => (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 sm:w-4 sm:h-4 text-gray-500" />
                </div>
              </motion.div>
            </div>

            {/* Stats Summary */}
            <motion.div
              className="flex items-center space-x-4 sm:space-x-6 sm:ml-auto"
              initial={{ x: 20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
            >
              <div className="text-center">
                <motion.div
                  className="text-xl sm:text-2xl font-bold text-purple-600"
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 0.3, delay: 0.7 }}
                >
                  {totalQuestions}
                </motion.div>
                <div className="text-xs text-gray-500">Total Questions</div>
              </div>
              <div className="text-center">
                <motion.div
                  className="text-xl sm:text-2xl font-bold text-blue-600"
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 0.3, delay: 0.8 }}
                >
                  {chapters.filter((chapter) => chapter.NEET[selectedYear] > 0).length}
                </motion.div>
                <div className="text-xs text-gray-500">Active Chapters</div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Content */}
        <AnimatePresence mode="wait">
          {viewType === 'chart' ? (
            <motion.div
              key="chart"
              className="bg-white/50 backdrop-blur-lg rounded-2xl shadow-sm border border-gray-100/50 overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              <div className="border-b border-gray-100/50 px-4 sm:px-6 py-3 sm:py-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                  >
                    <h2 className="text-base sm:text-lg font-semibold text-gray-900">
                      Question Distribution Analysis
                    </h2>
                    <p className="text-xs sm:text-sm text-gray-600">
                      {selectedSubject} • NEET {selectedYear}
                    </p>
                  </motion.div>
                  <motion.div
                    className="flex items-center space-x-2"
                    initial={{ x: 20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                  >
                    <TrendingUp className="w-4 h-4 sm:w-5 sm:h-5 text-green-500" />
                    <span className="text-xs sm:text-sm text-gray-600">Visual Analytics</span>
                  </motion.div>
                </div>
              </div>

              <div className="p-4 sm:p-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
                  {/* Pie Chart Section */}
                  <motion.div
                    className="lg:col-span-2"
                    initial={{ scale: 0.95, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                  >
                    <div className="bg-gradient-to-br from-gray-50/80 to-gray-100/80 backdrop-blur-sm rounded-xl p-4 sm:p-6">
                      <div className="h-[300px] sm:h-[400px] lg:h-[500px]">
                        <Pie data={pieChartData} options={pieChartOptions} />
                      </div>
                    </div>
                  </motion.div>

                  {/* Chapter Details Section */}
                  <div className="lg:col-span-1">
                    <motion.h3
                      className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 text-gray-900"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.5, duration: 0.5 }}
                    >
                      Chapter Breakdown
                    </motion.h3>
                    <div className="bg-gray-50/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 h-[300px] sm:h-[400px] lg:h-[500px] overflow-y-auto">
                      <div className="space-y-3">
                        {chapters
                          .filter((chapter) => chapter.NEET[selectedYear] > 0)
                          .sort((a, b) => (b.NEET[selectedYear] || 0) - (a.NEET[selectedYear] || 0))
                          .map((chapter, index) => {
                            const questions = chapter.NEET[selectedYear] || 0;
                            const percentage =
                              totalQuestions > 0
                                ? ((questions / totalQuestions) * 100).toFixed(1)
                                : 0;
                            const colors = generateColors(chapters.length);
                            const originalIndex = chapters.findIndex(
                              (c) => c.Chapter_name === chapter.Chapter_name
                            );

                            return (
                              <motion.div
                                key={chapter.Chapter_name}
                                className="bg-white/80 backdrop-blur-sm rounded-lg p-3 sm:p-4 shadow-sm border border-gray-100/50 hover:shadow-md transition-shadow"
                                initial={{ x: -20, opacity: 0 }}
                                animate={{ x: 0, opacity: 1 }}
                                transition={{ delay: 0.1 * index, duration: 0.3 }}
                                whileHover={{ y: -3 }}
                              >
                                <div className="flex items-center">
                                  <motion.div
                                    className="w-3 h-3 sm:w-4 sm:h-4 rounded-full mr-2 sm:mr-3 flex-shrink-0 shadow-sm"
                                    style={{ backgroundColor: colors[originalIndex] }}
                                    whileHover={{ scale: 1.2 }}
                                  ></motion.div>
                                  <div className="flex-grow min-w-0">
                                    <p
                                      className="text-xs sm:text-sm font-medium text-gray-900 truncate"
                                      title={chapter.Chapter_name}
                                    >
                                      {chapter.Chapter_name}
                                    </p>
                                    <div className="flex justify-between items-center mt-1 sm:mt-2">
                                      <span className="text-xs text-gray-500 bg-gray-100/80 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-md">
                                        {questions} questions
                                      </span>
                                      <span className="text-xs sm:text-sm font-bold text-purple-600 bg-purple-50/80 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-md">
                                        {percentage}%
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </motion.div>
                            );
                          })}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="table"
              className="bg-white/50 backdrop-blur-lg rounded-2xl shadow-sm border border-gray-100/50 overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              <div className="border-b border-gray-100/50 px-4 sm:px-6 py-3 sm:py-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <motion.div
                    initial={{ x: -20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                  >
                    <h2 className="text-base sm:text-lg font-semibold text-gray-900">
                      Chapter-wise Question Table
                    </h2>
                    <p className="text-xs sm:text-sm text-gray-600">
                      {selectedSubject} • NEET {selectedYear}
                    </p>
                  </motion.div>
                  <motion.div
                    className="flex items-center space-x-2"
                    initial={{ x: 20, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                  >
                    <Table className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500" />
                    <span className="text-xs sm:text-sm text-gray-600">Detailed View</span>
                  </motion.div>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead className="bg-gradient-to-r from-purple-50/80 to-blue-50/80 backdrop-blur-sm">
                    <tr>
                      <th className="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                        Chapter Name
                      </th>
                      <th className="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                        Questions (NEET {selectedYear})
                      </th>
                      <th className="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                        Percentage
                      </th>
                      <th className="px-4 sm:px-6 py-3 sm:py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                        Priority
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100/50">
                    {chapters.map((chapter, index) => {
                      const questions = chapter.NEET[selectedYear];
                      const percentage =
                        totalQuestions > 0 && questions > 0
                          ? ((questions / totalQuestions) * 100).toFixed(1)
                          : 0;
                      const getPriority = (pct) => {
                        if (pct >= 15)
                          return { label: 'High', color: 'bg-red-100/80 text-red-800' };
                        if (pct >= 8)
                          return { label: 'Medium', color: 'bg-yellow-100/80 text-yellow-800' };
                        if (pct > 0)
                          return { label: 'Low', color: 'bg-green-100/80 text-green-800' };
                        return { label: 'None', color: 'bg-gray-100/80 text-gray-800' };
                      };
                      const priority = getPriority(parseFloat(percentage));

                      return (
                        <motion.tr
                          key={index}
                          className={`hover:bg-gray-50/80 transition-colors ${
                            index % 2 === 0 ? 'bg-white/80' : 'bg-gray-50/50'
                          } backdrop-blur-sm`}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.1 * index, duration: 0.3 }}
                        >
                          <td className="px-4 sm:px-6 py-3 sm:py-4 text-xs sm:text-sm font-medium text-gray-900">
                            {chapter.Chapter_name}
                          </td>
                          <td className="px-4 sm:px-6 py-3 sm:py-4 text-xs sm:text-sm text-gray-700">
                            {questions !== null && questions !== undefined ? (
                              <span className="inline-flex items-center px-2 sm:px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100/80 text-blue-800">
                                {questions}
                              </span>
                            ) : (
                              <span className="text-gray-400">N/A</span>
                            )}
                          </td>
                          <td className="px-4 sm:px-6 py-3 sm:py-4 text-xs sm:text-sm text-gray-700">
                            {percentage > 0 ? (
                              <span className="font-semibold text-purple-600">{percentage}%</span>
                            ) : (
                              <span className="text-gray-400">0%</span>
                            )}
                          </td>
                          <td className="px-4 sm:px-6 py-3 sm:py-4 text-xs sm:text-sm">
                            <span
                              className={`inline-flex items-center px-2 sm:px-2.5 py-0.5 rounded-full text-xs font-medium ${priority.color}`}
                            >
                              {priority.label}
                            </span>
                          </td>
                        </motion.tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export default BoosterModule;
