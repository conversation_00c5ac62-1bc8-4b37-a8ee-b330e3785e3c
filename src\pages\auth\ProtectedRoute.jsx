import React from 'react';
import { Navigate, Outlet } from 'react-router';

const ProtectedRoute = ({ allowedRoles }) => {
  const role = sessionStorage.getItem('role');

  if (!role) {
    return <Navigate to="/auth" replace />;
  }

  // Check whether role is allowed
  if (!allowedRoles.includes(role)) {
    return <Navigate to="/auth" replace />;
  }

  return <Outlet />;
};

export default ProtectedRoute;
