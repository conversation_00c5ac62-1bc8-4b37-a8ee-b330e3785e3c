'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useDirectorListMentorServiceQuery } from '../addMentor/addMentor.Slice';
import Toastify from '../../../../components/PopUp/Toastify';
import { FiUsers, FiMail, FiBook, FiAlertCircle, FiSearch, FiGrid, FiList } from 'react-icons/fi';
import { FaGraduationCap } from 'react-icons/fa6';

const ListMentor = () => {
  const {
    data: mentors,
    isLoading: mentorsLoading,
    isError: mentorsError,
    error: mentorsErrorData
  } = useDirectorListMentorServiceQuery();
  const [res, setRes] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [courseFilter, setCourseFilter] = useState('');
  const [viewMode, setViewMode] = useState('grid'); // grid or table

  // Safety check: Ensure data is an array
  const mentorsArray = Array.isArray(mentors) ? mentors : [];

  // Filter mentors based on search and course filter
  const filteredMentors = mentorsArray.filter((mentor) => {
    const matchesSearch =
      `${mentor.first_name} ${mentor.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mentor.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mentor.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCourse = !courseFilter || mentor.course_name === courseFilter;

    console.log('Mentor Check:', {
      name: `${mentor.first_name} ${mentor.last_name}`,
      course: mentor.course_name || 'No course',
      matchesSearch,
      matchesCourse,
      courseFilter
    });

    return matchesSearch && matchesCourse;
  });

  console.log('Filtered Mentors:', filteredMentors);

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: 'easeOut' }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.3 }
    },
    hover: {
      scale: 1.02,
      boxShadow: '0px 10px 30px rgba(0, 0, 0, 0.1)',
      transition: { duration: 0.2 }
    }
  };

  const buttonVariants = {
    hover: {
      scale: 1.05,
      boxShadow: '0px 8px 20px rgba(0, 0, 0, 0.15)',
      transition: { duration: 0.2 }
    },
    tap: { scale: 0.95 }
  };

  if (mentorsLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8 flex items-center justify-center">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            className="inline-block w-12 h-12 border-4 border-gray-300 border-t-[var(--color-director)] rounded-full mb-4"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
          />
          <p className="text-gray-600 text-lg">Loading mentors...</p>
        </motion.div>
      </div>
    );
  }

  if (mentorsError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8 flex items-center justify-center">
        <motion.div
          className="text-center bg-white rounded-3xl p-8 shadow-2xl"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <FiAlertCircle className="mx-auto text-6xl text-red-500 mb-4" />
          <h3 className="text-xl font-bold text-gray-900 mb-2">Error Loading Data</h3>
          <p className="text-red-500">{mentorsErrorData?.message || 'Failed to load mentors'}</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8">
      <Toastify res={res} resClear={() => setRes(null)} />

      <motion.div
        className="max-w-7xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header Section */}
        <motion.div className="text-center mb-8" variants={itemVariants}>
          <motion.div
            className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4"
            style={{
              background: `linear-gradient(135deg, var(--color-director), var(--color-director))`,
              boxShadow: '0px 10px 30px rgba(0, 0, 0, 0.1)'
            }}
            whileHover={{ scale: 1.1, rotate: 5 }}
          >
            <FaGraduationCap className="text-4xl" style={{ color: 'white' }} />
          </motion.div>
          <motion.h1 className="text-4xl font-bold mb-2" style={{ color: 'var(--color-director)' }}>
            List Mentors
          </motion.h1>
          <motion.p className="text-gray-600 text-lg">View and manage mentors</motion.p>
          <motion.div className="mt-4 inline-flex items-center px-4 py-2 bg-white rounded-full shadow-lg">
            <FiUsers className="mr-2 text-gray-500" />
            <span className="text-gray-700 font-medium">
              {filteredMentors.length} mentors available
            </span>
          </motion.div>
        </motion.div>

        {/* Controls Section */}
        <motion.div
          className="bg-white rounded-3xl shadow-2xl overflow-hidden mb-8"
          variants={itemVariants}
          style={{
            background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
            border: '1px solid rgba(0, 0, 0, 0.05)'
          }}
        >
          <div
            className="px-8 py-6"
            style={{
              background: `linear-gradient(135deg, var(--color-director), var(--color-director))`
            }}
          >
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              {/* Title & Description */}
              <div>
                <h3 className="text-2xl font-bold text-white">Mentor Management</h3>
                <p className="mt-1 text-white text-opacity-90">Search and filter mentors</p>
              </div>

              {/* Controls: Search, Filter & View Mode Toggle */}
              <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                {/* Search */}
                <div className="relative w-full sm:w-auto">
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white text-opacity-70" />
                  <input
                    type="text"
                    placeholder="Search mentors..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full sm:w-64 pl-10 pr-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg placeholder-white placeholder-opacity-70 focus:outline-none focus:bg-opacity-30"
                    aria-label="Search mentors"
                  />
                </div>

                {/* Course Filter Dropdown */}
                <select
                  value={courseFilter}
                  onChange={(e) => setCourseFilter(e.target.value)}
                  className="px-4 py-2 bg-white bg-opacity-20 border border-white border-opacity-30 rounded-lg focus:outline-none focus:bg-opacity-30 text-white"
                  style={{ color: 'var(--color-director)' }}
                  aria-label="Filter by course"
                >
                  <option value="" className="text-gray-900">
                    All Courses
                  </option>
                  <option value="JEE" className="text-gray-900">
                    JEE
                  </option>
                  <option value="NEET" className="text-gray-900">
                    NEET
                  </option>
                </select>

                {/* View Toggle Switch */}
                <div className="flex bg-white bg-opacity-20 rounded-lg p-1">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded ${viewMode === 'grid' ? 'bg-white bg-opacity-30' : ''}`}
                    aria-label="Grid view"
                  >
                    <FiGrid style={{ color: 'var(--color-director)' }} />
                  </button>
                  <button
                    onClick={() => setViewMode('table')}
                    className={`p-2 rounded ${viewMode === 'table' ? 'bg-white bg-opacity-30' : ''}`}
                    aria-label="Table view"
                  >
                    <FiList style={{ color: 'var(--color-director)' }} />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Mentors Content */}
        <motion.div
          className="bg-white rounded-3xl shadow-2xl overflow-hidden"
          variants={itemVariants}
          style={{
            background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
            border: '1px solid rgba(0, 0, 0, 0.05)'
          }}
        >
          <div className="p-8">
            {filteredMentors.length === 0 ? (
              <div className="text-center py-12">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <FaGraduationCap className="mx-auto text-6xl text-gray-300 mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-2">No Mentors Found</h3>
                  <p className="text-gray-600">
                    {searchTerm || courseFilter
                      ? 'No mentors match your search criteria'
                      : 'No mentors available'}
                  </p>
                </motion.div>
              </div>
            ) : viewMode === 'grid' ? (
              /* Grid View */
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                {filteredMentors.map((mentor, index) => (
                  <motion.div
                    key={mentor.id}
                    className="bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
                    variants={cardVariants}
                    whileHover="hover"
                    initial="hidden"
                    animate="visible"
                    transition={{ delay: index * 0.1 }}
                  >
                    <div className="p-6">
                      <div className="flex items-center mb-4">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold mr-4"
                          style={{ backgroundColor: 'var(--color-director)' }}
                        >
                          {mentor.first_name.charAt(0)}
                          {mentor.last_name.charAt(0)}
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-900 text-lg">
                            {mentor.first_name} {mentor.last_name}
                          </h4>
                          <p className="text-gray-600 text-sm">@{mentor.username}</p>
                        </div>
                      </div>
                      <div className="space-y-3 mb-4">
                        <div className="flex items-center text-sm text-gray-600">
                          <FiMail className="mr-2" />
                          {mentor.email}
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <FiBook className="mr-2" />
                          <span
                            className="px-2 py-1 rounded-full text-xs font-medium text-white"
                            style={{ backgroundColor: 'var(--color-director)' }}
                          >
                            {mentor.course_name}
                          </span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <FiBook className="mr-2" />
                          <span
                            className="px-2 py-1 rounded-full text-xs font-medium text-white"
                            style={{ backgroundColor: 'var(--color-director)' }}
                          >
                            {mentor.subject_name}
                          </span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              /* Table View */
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                        Mentor
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                        Username
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                        Course
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                        Subject
                      </th>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-700">
                        Email
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100">
                    <AnimatePresence>
                      {filteredMentors.map((mentor, index) => (
                        <motion.tr
                          key={mentor.id}
                          className="hover:bg-gray-50 transition-all duration-300"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: 20 }}
                          transition={{ delay: index * 0.05 }}
                          whileHover={{ backgroundColor: '#f9fafb' }}
                        >
                          <td className="px-6 py-4">
                            <div className="flex items-center">
                              <div
                                className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold mr-3"
                                style={{ backgroundColor: 'var(--color-director)' }}
                              >
                                {mentor.first_name.charAt(0)}
                                {mentor.last_name.charAt(0)}
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">
                                  {mentor.first_name} {mentor.last_name}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 text-gray-900 font-medium">
                            @{mentor.username}
                          </td>
                          <td className="px-6 py-4">
                            <span
                              className="px-3 py-1 rounded-full text-xs font-medium text-white"
                              style={{ backgroundColor: 'var(--color-director)' }}
                            >
                              {mentor.course_name}
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <span
                              className="px-3 py-1 rounded-full text-xs font-medium text-white"
                              style={{ backgroundColor: 'var(--color-director)' }}
                            >
                              {mentor.subject_name}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-gray-600">{mentor.email}</td>
                        </motion.tr>
                      ))}
                    </AnimatePresence>
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default ListMentor;
