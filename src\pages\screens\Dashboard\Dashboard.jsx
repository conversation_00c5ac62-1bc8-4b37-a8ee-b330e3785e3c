import React from 'react';
import DirectorPanelDashboard from '../directorPanel/directorPanelDashboard/DirectorPanelDashboard';

import ParentDashboard from '../parentPanel/parentDashboard/ParentDashboard';
import StudentsDashboard from '../studentPanel/studentsDashboard/StudentsDashboard';
import CenterTraineeOverview from '../centreTraineePanel/overView/CennterTraineeOverview';
import TeacherOverview from '../teacherPanel/teacherOverview/TeacherOverview';
import Overview from '../centreCounselorPanel/overView/Overview';
import MentorDashboard from '../mentorPanel/mentorDashboard/MentorDashboard';

const Dashboard = () => {
  const role = sessionStorage.getItem('role');

  const renderDashboard = () => {
    switch (role) {
      case 'director':
        return <DirectorPanelDashboard />;
      case 'center_counselor':
        return <Overview />;
      case 'faculty':
        return <CenterTraineeOverview />;
      case 'parent':
        return <ParentDashboard />;
      case 'student':
        return <StudentsDashboard />;
      case 'kota_teacher':
        return <TeacherOverview />;
      case 'mendor':
        return <MentorDashboard />;
      default:
        return <div>No dashboard available for your role.</div>;
    }
  };

  return (
    <div className="[&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
      {renderDashboard()}
    </div>
  );
};

export default Dashboard;
