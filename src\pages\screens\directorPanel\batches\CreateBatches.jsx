'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  useAddBatchesServiceMutation,
  useUpdateBatchServiceMutation,
  useDeleteBatchServiceMutation,
  useListBatchServiceQuery
} from './createBatches.Slice';
import Toastify from '../../../../components/PopUp/Toastify';
import {
  FiUsers,
  FiEdit,
  FiTrash2,
  FiPlus,
  FiX,
  FiFileText,
  FiBook,
  FiAlertCircle
} from 'react-icons/fi';

const CreateBatches = () => {
  const [
    addBatch,
    { isLoading: isAdding, isSuccess: isAddSuccess, isError: isAddError, error: addError }
  ] = useAddBatchesServiceMutation();
  const [
    updateBatch,
    {
      isLoading: isUpdating,
      isSuccess: isUpdateSuccess,
      isError: isUpdateError,
      error: updateError
    }
  ] = useUpdateBatchServiceMutation();
  const [
    deleteBatch,
    {
      isLoading: isDeleting,
      isSuccess: isDeleteSuccess,
      isError: isDeleteError,
      error: deleteError
    }
  ] = useDeleteBatchServiceMutation();
  const {
    data: batches,
    isLoading: isListing,
    isError: isListError,
    error: listError
  } = useListBatchServiceQuery();

  const [addFormData, setAddFormData] = useState({
    batch_name: '',
    description: ''
  });

  const [updateFormData, setUpdateFormData] = useState({
    batch_id: '',
    batch_name: '',
    description: ''
  });

  const [selectedBatch, setSelectedBatch] = useState(null);
  const [res, setRes] = useState(null);
  const [deletingBatchId, setDeletingBatchId] = useState(null);

  // Validation functions
  const validateBatchName = (name) => {
    if (!name) return 'Batch name is required';
    if (name.length < 3) return 'Batch name must be at least 3 characters long';
    return '';
  };

  const validateDescription = (description) => {
    if (!description) return 'Description is required';
    if (description.length < 10) return 'Description must be at least 10 characters long';
    return '';
  };

  const [addErrors, setAddErrors] = useState({
    batch_name: '',
    description: ''
  });

  const [updateErrors, setUpdateErrors] = useState({
    batch_name: '',
    description: ''
  });

  const handleAddChange = (e) => {
    const { name, value } = e.target;
    setAddFormData((prev) => ({
      ...prev,
      [name]: value
    }));

    // Real-time validation
    let error = '';
    if (name === 'batch_name') {
      error = validateBatchName(value);
    } else if (name === 'description') {
      error = validateDescription(value);
    }
    setAddErrors((prev) => ({ ...prev, [name]: error }));
  };

  const handleUpdateChange = (e) => {
    const { name, value } = e.target;
    setUpdateFormData((prev) => ({
      ...prev,
      [name]: value
    }));

    // Real-time validation
    let error = '';
    if (name === 'batch_name') {
      error = validateBatchName(value);
    } else if (name === 'description') {
      error = validateDescription(value);
    }
    setUpdateErrors((prev) => ({ ...prev, [name]: error }));
  };

  const validateAddForm = () => {
    const newErrors = {
      batch_name: validateBatchName(addFormData.batch_name),
      description: validateDescription(addFormData.description)
    };
    setAddErrors(newErrors);
    return Object.values(newErrors).every((error) => error === '');
  };

  const validateUpdateForm = () => {
    const newErrors = {
      batch_name: validateBatchName(updateFormData.batch_name),
      description: validateDescription(updateFormData.description)
    };
    setUpdateErrors(newErrors);
    return Object.values(newErrors).every((error) => error === '');
  };

  const handleAddSubmit = async (e) => {
    e.preventDefault();
    if (!validateAddForm()) {
      setRes({ status: 'error', message: 'Please fix the errors in the form.' });
      return;
    }
    const body = {
      batch_name: addFormData.batch_name,
      description: addFormData.description
    };
    try {
      const response = await addBatch(body).unwrap();
      setAddFormData({
        batch_name: '',
        description: ''
      });
      setAddErrors({
        batch_name: '',
        description: ''
      });
      setRes({ status: 'success', message: 'Batch added successfully!' });
    } catch (err) {
      setRes({
        status: 'error',
        message: err?.data?.message || 'Failed to add batch. Please try again.'
      });
    }
  };

  const handleUpdateSubmit = async (e) => {
    e.preventDefault();
    if (!validateUpdateForm()) {
      setRes({ status: 'error', message: 'Please fix the errors in the form.' });
      return;
    }
    const body = {
      batch_id: updateFormData.batch_id,
      batch_name: updateFormData.batch_name,
      description: updateFormData.description
    };
    if (!body.batch_id) {
      setRes({ status: 'error', message: 'Batch ID is missing for update' });
      return;
    }
    try {
      const response = await updateBatch(body).unwrap();
      setUpdateFormData({
        batch_id: '',
        batch_name: '',
        description: ''
      });
      setUpdateErrors({
        batch_name: '',
        description: ''
      });
      setSelectedBatch(null);
      setRes({ status: 'success', message: 'Batch updated successfully!' });
    } catch (err) {
      setRes({
        status: 'error',
        message: err?.data?.message || 'Failed to update batch. Please try again.'
      });
    }
  };

  const handleDelete = async (batch_id) => {
    if (!batch_id) {
      setRes({ status: 'error', message: 'Batch ID is missing for delete' });
      return;
    }
    setDeletingBatchId(batch_id);
    const body = { batch_id };
    try {
      const response = await deleteBatch(body).unwrap();
      setRes({ status: 'success', message: 'Batch deleted successfully!' });
    } catch (err) {
      setRes({
        status: 'error',
        message: err?.data?.message || 'Failed to delete batch. Please try again.'
      });
    } finally {
      setDeletingBatchId(null);
    }
  };

  const selectBatchForUpdate = (batch) => {
    setSelectedBatch(batch);
    setUpdateFormData({
      batch_id: batch.batch_id || batch.id,
      batch_name: batch.batch_name || '',
      description: batch.description || ''
    });
    setUpdateErrors({
      batch_name: '',
      description: ''
    });
  };

  const cancelUpdate = () => {
    setSelectedBatch(null);
    setUpdateFormData({
      batch_id: '',
      batch_name: '',
      description: ''
    });
    setUpdateErrors({
      batch_name: '',
      description: ''
    });
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: 'easeOut' }
    }
  };

  const inputVariants = {
    hover: {
      scale: 1.02,
      boxShadow: '0px 8px 25px rgba(0, 0, 0, 0.1)',
      transition: { duration: 0.2 }
    },
    focus: {
      scale: 1.01,
      boxShadow: '0px 0px 0px 3px rgba(var(--color-director-rgb), 0.1)',
      transition: { duration: 0.2 }
    }
  };

  const buttonVariants = {
    hover: {
      scale: 1.02,
      boxShadow: '0px 10px 30px rgba(0, 0, 0, 0.2)',
      transition: { duration: 0.2 }
    },
    tap: { scale: 0.98 }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.3 }
    },
    hover: {
      scale: 1.02,
      boxShadow: '0px 10px 30px rgba(0, 0, 0, 0.1)',
      transition: { duration: 0.2 }
    }
  };

  // Safety check for batches data
  const batchesArray = Array.isArray(batches) ? batches : [];

  if (isListError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8 flex items-center justify-center">
        <motion.div
          className="text-center bg-white rounded-3xl p-8 shadow-2xl"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <FiAlertCircle className="mx-auto text-6xl text-red-500 mb-4" />
          <h3 className="text-xl font-bold text-gray-900 mb-2">Error Loading Batches</h3>
          <p className="text-red-500">{listError?.data?.message || 'Failed to load batches'}</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 p-4 md:p-8">
      <Toastify res={res} resClear={() => setRes(null)} />

      <motion.div
        className="max-w-6xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header Section */}
        <motion.div className="text-center mb-8" variants={itemVariants}>
          <motion.div
            className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4"
            style={{
              background: `linear-gradient(135deg, var(--color-director), var(--color-director))`,
              boxShadow: '0px 10px 30px rgba(0, 0, 0, 0.1)'
            }}
            whileHover={{ scale: 1.1, rotate: 5 }}
            whileTap={{ scale: 0.9 }}
          >
            <FiUsers className="text-2xl" style={{ color: 'white' }} />
          </motion.div>
          <motion.h1 className="text-4xl font-bold mb-2" style={{ color: 'var(--color-director)' }}>
            Batch Manager
          </motion.h1>
          <motion.p className="text-gray-600 text-lg">
            Create, update, and manage your batches
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Add Batch Form */}
          <motion.div
            className="bg-white rounded-3xl shadow-2xl overflow-hidden"
            variants={itemVariants}
            style={{
              background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
              border: '1px solid rgba(0, 0, 0, 0.05)'
            }}
          >
            <div
              className="px-8 py-6 text-white"
              style={{
                background: `linear-gradient(135deg, var(--color-director), var(--color-director)dd)`
              }}
            >
              <div className="flex items-center">
                <FiPlus className="mr-3 text-[var(--color-director)]  text-2xl" />
                <div>
                  <h3 className="text-2xl text-[var(--color-director)] font-bold">Add New Batch</h3>
                  <p className="text-black text-opacity-90 mt-1">
                    Create a new batch for your courses
                  </p>
                </div>
              </div>
            </div>

            <div className="p-8">
              <form onSubmit={handleAddSubmit} className="space-y-6">
                {/* Batch Name */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <FiUsers className="mr-2" style={{ color: 'var(--color-director)' }} />
                    Batch Name
                  </label>
                  <div className="relative">
                    <motion.input
                      type="text"
                      name="batch_name"
                      placeholder="Enter batch name (e.g., 2025-2026)"
                      value={addFormData.batch_name}
                      onChange={handleAddChange}
                      className={`w-full border-2 ${
                        addErrors.batch_name ? 'border-red-400' : 'border-gray-200'
                      } rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                      style={{
                        focusBorderColor: 'var(--color-director)',
                        fontSize: '16px'
                      }}
                      required
                      variants={inputVariants}
                      whileHover="hover"
                      whileFocus="focus"
                    />
                    <FiUsers className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  {addErrors.batch_name && (
                    <motion.p
                      className="text-red-500 text-sm flex items-center"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                    >
                      <span className="mr-1">⚠</span> {addErrors.batch_name}
                    </motion.p>
                  )}
                </motion.div>

                {/* Description */}
                <motion.div variants={itemVariants} className="space-y-2">
                  <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                    <FiFileText className="mr-2" style={{ color: 'var(--color-director)' }} />
                    Description
                  </label>
                  <div className="relative">
                    <motion.textarea
                      name="description"
                      placeholder="Enter batch description"
                      value={addFormData.description}
                      onChange={handleAddChange}
                      className={`w-full border-2 ${
                        addErrors.description ? 'border-red-400' : 'border-gray-200'
                      } rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white resize-none`}
                      style={{
                        focusBorderColor: 'var(--color-director)',
                        fontSize: '16px'
                      }}
                      rows="4"
                      required
                      variants={inputVariants}
                      whileHover="hover"
                      whileFocus="focus"
                    />
                    <FiFileText className="absolute left-4 top-6 text-gray-400" />
                  </div>
                  {addErrors.description && (
                    <motion.p
                      className="text-red-500 text-sm flex items-center"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                    >
                      <span className="mr-1">⚠</span> {addErrors.description}
                    </motion.p>
                  )}
                </motion.div>

                {/* Submit Button */}
                <motion.div variants={itemVariants} className="pt-4">
                  <motion.button
                    type="submit"
                    disabled={isAdding}
                    className="w-full  px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3"
                    style={{
                      background: `linear-gradient(135deg, var(--color-director), var(--color-director)dd)`,
                      boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.1)',
                      color: 'var(--color-director)'
                    }}
                    variants={buttonVariants}
                    whileHover="hover"
                    whileTap="tap"
                  >
                    {isAdding ? (
                      <>
                        <motion.div
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                          animate={{ rotate: 360 }}
                          transition={{
                            duration: 1,
                            repeat: Number.POSITIVE_INFINITY,
                            ease: 'linear'
                          }}
                        />
                        <span>Adding...</span>
                      </>
                    ) : (
                      <>
                        <FiPlus size={20} />
                        <span>Add Batch</span>
                      </>
                    )}
                  </motion.button>
                </motion.div>
              </form>
            </div>
          </motion.div>

          {/* Update Batch Form */}
          <AnimatePresence>
            {selectedBatch ? (
              <motion.div
                className="bg-white rounded-3xl shadow-2xl overflow-hidden"
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
                style={{
                  background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
                  border: '1px solid rgba(0, 0, 0, 0.05)'
                }}
              >
                <div
                  className="px-8 py-6 text-white"
                  style={{
                    background: `linear-gradient(135deg, var(--color-director), var(--color-director)dd)`
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <FiEdit className="mr-3 text-2xl" />
                      <div>
                        <h3 className="text-2xl font-bold">Update Batch</h3>
                        <p className="text-white text-opacity-90 mt-1">Modify batch information</p>
                      </div>
                    </div>
                    <motion.button
                      onClick={cancelUpdate}
                      className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <FiX className="text-2xl" />
                    </motion.button>
                  </div>
                </div>

                <div className="p-8">
                  <form onSubmit={handleUpdateSubmit} className="space-y-6">
                    {/* Batch Name */}
                    <motion.div variants={itemVariants} className="space-y-2">
                      <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                        <FiUsers className="mr-2" style={{ color: 'var(--color-director)' }} />
                        Batch Name
                      </label>
                      <div className="relative">
                        <motion.input
                          type="text"
                          name="batch_name"
                          placeholder="Enter batch name"
                          value={updateFormData.batch_name}
                          onChange={handleUpdateChange}
                          className={`w-full border-2 ${
                            updateErrors.batch_name ? 'border-red-400' : 'border-gray-200'
                          } rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white`}
                          style={{
                            focusBorderColor: 'var(--color-director)',
                            fontSize: '16px'
                          }}
                          required
                          variants={inputVariants}
                          whileHover="hover"
                          whileFocus="focus"
                        />
                        <FiUsers className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      </div>
                      {updateErrors.batch_name && (
                        <motion.p
                          className="text-red-500 text-sm flex items-center"
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                        >
                          <span className="mr-1">⚠</span> {updateErrors.batch_name}
                        </motion.p>
                      )}
                    </motion.div>

                    {/* Description */}
                    <motion.div variants={itemVariants} className="space-y-2">
                      <label className="flex items-center text-sm font-semibold text-gray-700 mb-2">
                        <FiFileText className="mr-2" style={{ color: 'var(--color-director)' }} />
                        Description
                      </label>
                      <div className="relative">
                        <motion.textarea
                          name="description"
                          placeholder="Enter batch description"
                          value={updateFormData.description}
                          onChange={handleUpdateChange}
                          className={`w-full border-2 ${
                            updateErrors.description ? 'border-red-400' : 'border-gray-200'
                          } rounded-xl px-4 py-4 pl-12 focus:outline-none transition-all duration-300 bg-gray-50 focus:bg-white resize-none`}
                          style={{
                            focusBorderColor: 'var(--color-director)',
                            fontSize: '16px'
                          }}
                          rows="4"
                          required
                          variants={inputVariants}
                          whileHover="hover"
                          whileFocus="focus"
                        />
                        <FiFileText className="absolute left-4 top-6 text-gray-400" />
                      </div>
                      {updateErrors.description && (
                        <motion.p
                          className="text-red-500 text-sm flex items-center"
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                        >
                          <span className="mr-1">⚠</span> {updateErrors.description}
                        </motion.p>
                      )}
                    </motion.div>

                    {/* Action Buttons */}
                    <motion.div variants={itemVariants} className="pt-4 grid grid-cols-2 gap-4">
                      <motion.button
                        type="submit"
                        disabled={isUpdating || !updateFormData.batch_id}
                        className="text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                        style={{
                          background: `linear-gradient(135deg, var(--color-director), var(--color-director)dd)`,
                          boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.1)'
                        }}
                        variants={buttonVariants}
                        whileHover="hover"
                        whileTap="tap"
                      >
                        {isUpdating ? (
                          <>
                            <motion.div
                              className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                              animate={{ rotate: 360 }}
                              transition={{
                                duration: 1,
                                repeat: Number.POSITIVE_INFINITY,
                                ease: 'linear'
                              }}
                            />
                            <span>Updating...</span>
                          </>
                        ) : (
                          <>
                            <FiEdit size={16} />
                            <span>Update</span>
                          </>
                        )}
                      </motion.button>
                      <motion.button
                        type="button"
                        onClick={cancelUpdate}
                        className="bg-gray-500 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:bg-gray-600 flex items-center justify-center space-x-2"
                        variants={buttonVariants}
                        whileHover="hover"
                        whileTap="tap"
                      >
                        <FiX size={16} />
                        <span>Cancel</span>
                      </motion.button>
                    </motion.div>
                  </form>
                </div>
              </motion.div>
            ) : (
              /* Placeholder when no batch is selected */
              <motion.div
                className="bg-white rounded-3xl shadow-2xl overflow-hidden flex items-center justify-center"
                variants={itemVariants}
                style={{
                  background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
                  border: '1px solid rgba(0, 0, 0, 0.05)',
                  minHeight: '400px'
                }}
              >
                <div className="text-center">
                  <FiEdit className="mx-auto text-6xl text-gray-300 mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Select a Batch to Edit</h3>
                  <p className="text-gray-600">
                    Choose a batch from the list below to update its information
                  </p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Batch List */}
        <motion.div
          className="mt-8 bg-white rounded-3xl shadow-2xl overflow-hidden"
          variants={itemVariants}
          style={{
            background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
            border: '1px solid rgba(0, 0, 0, 0.05)'
          }}
        >
          <div
            className="px-8 py-6 text-white"
            style={{
              background: `linear-gradient(135deg, var(--color-director), var(--color-director)dd)`
            }}
          >
            <div className="flex items-center">
              <FiUsers className="mr-3 text-2xl" />
              <div>
                <h3 className="text-2xl font-bold">Batches List</h3>
                <p className="text-white text-opacity-90 mt-1">
                  {batchesArray.length} batch{batchesArray.length !== 1 ? 'es' : ''} available
                </p>
              </div>
            </div>
          </div>

          <div className="p-8">
            {isListing ? (
              <div className="text-center py-12">
                <motion.div
                  className="inline-block w-8 h-8 border-4 border-gray-300 border-t-[var(--color-director)] rounded-full mb-4"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
                />
                <p className="text-gray-600 text-lg">Loading batches...</p>
              </div>
            ) : batchesArray.length === 0 ? (
              <div className="text-center py-12">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <FiUsers className="mx-auto text-6xl text-gray-300 mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-2">No Batches Available</h3>
                  <p className="text-gray-600">Create your first batch using the form above</p>
                </motion.div>
              </div>
            ) : (
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                variants={containerVariants}
              >
                <AnimatePresence>
                  {batchesArray.map((batch, index) => (
                    <motion.div
                      key={batch.batch_id}
                      className="bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-lg border border-gray-100 overflow-hidden"
                      variants={cardVariants}
                      whileHover="hover"
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="p-6">
                        <div className="flex items-center mb-4">
                          <div
                            className="w-12 h-12 rounded-full flex items-center justify-center text-white font-bold mr-4"
                            style={{ backgroundColor: 'var(--color-director)' }}
                          >
                            <FiUsers />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-bold text-gray-900 text-lg truncate">
                              {batch.batch_name}
                            </h4>
                            <p className="text-gray-600 text-sm">ID: {batch.batch_id || 'N/A'}</p>
                          </div>
                        </div>
                        <div className="space-y-2 mb-4">
                          <div className="flex items-start text-sm text-gray-600">
                            <FiFileText className="mr-2 mt-1 flex-shrink-0" />
                            <span className="line-clamp-3">
                              {batch.description || 'No description'}
                            </span>
                          </div>
                          {batch.course_id && (
                            <div className="flex items-center text-sm text-gray-600">
                              <FiBook className="mr-2" />
                              Course: {batch.course_name || batch.course_id}
                            </div>
                          )}
                        </div>
                        <div className="flex space-x-2">
                          <motion.button
                            onClick={() => selectBatchForUpdate(batch)}
                            className="flex-1 px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2 text-white"
                            style={{ backgroundColor: 'var(--color-director)' }}
                            variants={buttonVariants}
                            whileHover="hover"
                            whileTap="tap"
                          >
                            <FiEdit size={16} style={{ color: 'white' }} />
                            <span>Edit</span>
                          </motion.button>
                          <motion.button
                            onClick={() => handleDelete(batch.batch_id)}
                            disabled={deletingBatchId === batch.batch_id || !batch.batch_id}
                            className="flex-1  text-director px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                            variants={buttonVariants}
                            whileHover="hover"
                            whileTap="tap"
                          >
                            {deletingBatchId === batch.batch_id ? (
                              <>
                                <motion.div
                                  className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                                  animate={{ rotate: 360 }}
                                  transition={{
                                    duration: 1,
                                    repeat: Number.POSITIVE_INFINITY,
                                    ease: 'linear'
                                  }}
                                />
                                <span>Deleting...</span>
                              </>
                            ) : (
                              <>
                                <FiTrash2 size={16} style={{ color: 'white' }} />
                                <span>Delete</span>
                              </>
                            )}
                          </motion.button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </motion.div>
            )}
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default CreateBatches;
