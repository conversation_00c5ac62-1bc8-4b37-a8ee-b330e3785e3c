/**
 * WebRTC to RTMP Streaming Service
 * This service provides a more practical approach to RTMP streaming
 * by using WebSocket to relay media data to the backend for RTMP publishing
 */

class WebRTCToRTMPService {
  constructor() {
    this.isStreaming = false;
    this.websocket = null;
    this.mediaRecorder = null;
    this.sessionId = null;
    this.rtmpConfig = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 2000;
  }

  async startRTMPStream(mediaStream, rtmpConfig, websocketUrl) {
    try {
      console.log('🚀 Starting WebRTC to RTMP stream...');
      
      this.rtmpConfig = rtmpConfig;
      this.sessionId = rtmpConfig.session_id;
      
      // Create WebSocket connection to backend
      await this.connectWebSocket(websocketUrl);
      
      // Set up MediaRecorder for capturing stream data
      await this.setupMediaRecorder(mediaStream);
      
      // Send initial configuration
      this.sendMessage({
        type: 'rtmp_start',
        sessionId: this.sessionId,
        rtmpUrl: rtmpConfig.rtmp_url,
        streamKey: rtmpConfig.stream_key
      });
      
      // Start recording
      this.mediaRecorder.start(1000); // 1-second chunks
      this.isStreaming = true;
      
      console.log('✅ WebRTC to RTMP stream started successfully');
      return true;
      
    } catch (error) {
      console.error('❌ Failed to start WebRTC to RTMP stream:', error);
      this.cleanup();
      return false;
    }
  }

  async connectWebSocket(websocketUrl) {
    return new Promise((resolve, reject) => {
      try {
        this.websocket = new WebSocket(websocketUrl);
        
        this.websocket.onopen = () => {
          console.log('🔗 WebSocket connected for RTMP relay');
          this.reconnectAttempts = 0;
          resolve();
        };
        
        this.websocket.onmessage = (event) => {
          this.handleWebSocketMessage(event);
        };
        
        this.websocket.onerror = (error) => {
          console.error('❌ WebSocket error:', error);
          reject(error);
        };
        
        this.websocket.onclose = (event) => {
          console.log('🔌 WebSocket disconnected:', event.code, event.reason);
          if (this.isStreaming && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnect(websocketUrl);
          }
        };
        
      } catch (error) {
        reject(error);
      }
    });
  }

  async setupMediaRecorder(mediaStream) {
    return new Promise((resolve, reject) => {
      try {
        // Try different codec options for better compatibility
        const codecOptions = [
          'video/webm;codecs=vp8,opus',
          'video/webm;codecs=vp9,opus',
          'video/webm',
          'video/mp4'
        ];
        
        let selectedMimeType = null;
        for (const mimeType of codecOptions) {
          if (MediaRecorder.isTypeSupported(mimeType)) {
            selectedMimeType = mimeType;
            break;
          }
        }
        
        if (!selectedMimeType) {
          throw new Error('No supported media recorder codec found');
        }
        
        console.log('📹 Using codec:', selectedMimeType);
        
        this.mediaRecorder = new MediaRecorder(mediaStream, {
          mimeType: selectedMimeType,
          videoBitsPerSecond: 2000000, // 2 Mbps
          audioBitsPerSecond: 128000   // 128 kbps
        });
        
        this.mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0 && this.isStreaming) {
            this.sendMediaData(event.data);
          }
        };
        
        this.mediaRecorder.onerror = (event) => {
          console.error('❌ MediaRecorder error:', event.error);
          reject(event.error);
        };
        
        this.mediaRecorder.onstop = () => {
          console.log('📹 MediaRecorder stopped');
        };
        
        resolve();
        
      } catch (error) {
        reject(error);
      }
    });
  }

  sendMediaData(blob) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      // Send media data as binary
      this.websocket.send(blob);
    }
  }

  sendMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message));
    }
  }

  handleWebSocketMessage(event) {
    try {
      const message = JSON.parse(event.data);
      
      switch (message.type) {
        case 'rtmp_started':
          console.log('✅ RTMP stream started on server');
          break;
          
        case 'rtmp_error':
          console.error('❌ RTMP server error:', message.error);
          this.stopRTMPStream();
          break;
          
        case 'rtmp_stopped':
          console.log('🛑 RTMP stream stopped on server');
          break;
          
        case 'ping':
          this.sendMessage({ type: 'pong' });
          break;
          
        default:
          console.log('📨 Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('❌ Error parsing WebSocket message:', error);
    }
  }

  async attemptReconnect(websocketUrl) {
    this.reconnectAttempts++;
    console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
    
    setTimeout(async () => {
      try {
        await this.connectWebSocket(websocketUrl);
        
        // Restart RTMP stream after reconnection
        if (this.rtmpConfig) {
          this.sendMessage({
            type: 'rtmp_start',
            sessionId: this.sessionId,
            rtmpUrl: this.rtmpConfig.rtmp_url,
            streamKey: this.rtmpConfig.stream_key
          });
        }
        
      } catch (error) {
        console.error('❌ Reconnection failed:', error);
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          console.error('❌ Max reconnection attempts reached. Stopping stream.');
          this.stopRTMPStream();
        }
      }
    }, this.reconnectDelay * this.reconnectAttempts);
  }

  stopRTMPStream() {
    console.log('🛑 Stopping WebRTC to RTMP stream...');
    
    this.isStreaming = false;
    
    // Stop MediaRecorder
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }
    
    // Send stop message to server
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.sendMessage({
        type: 'rtmp_stop',
        sessionId: this.sessionId
      });
    }
    
    this.cleanup();
    console.log('✅ WebRTC to RTMP stream stopped');
  }

  cleanup() {
    // Close WebSocket
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    
    // Reset state
    this.mediaRecorder = null;
    this.sessionId = null;
    this.rtmpConfig = null;
    this.reconnectAttempts = 0;
  }

  getStreamingStatus() {
    return {
      isStreaming: this.isStreaming,
      sessionId: this.sessionId,
      websocketConnected: this.websocket && this.websocket.readyState === WebSocket.OPEN,
      reconnectAttempts: this.reconnectAttempts,
      rtmpUrl: this.rtmpConfig ? this.rtmpConfig.rtmp_url : null,
      streamKey: this.rtmpConfig ? '***' + this.rtmpConfig.stream_key.slice(-4) : null
    };
  }

  // Fallback method using HTTP chunks (for when WebSocket is not available)
  async startHTTPRTMPStream(mediaStream, rtmpConfig, apiBaseUrl) {
    try {
      console.log('🚀 Starting HTTP-based RTMP stream...');
      
      this.rtmpConfig = rtmpConfig;
      this.sessionId = rtmpConfig.session_id;
      
      // Set up MediaRecorder
      await this.setupMediaRecorder(mediaStream);
      
      // Override data handler for HTTP upload
      this.mediaRecorder.ondataavailable = async (event) => {
        if (event.data.size > 0 && this.isStreaming) {
          await this.uploadMediaChunk(event.data, apiBaseUrl);
        }
      };
      
      // Start recording
      this.mediaRecorder.start(2000); // 2-second chunks for HTTP
      this.isStreaming = true;
      
      console.log('✅ HTTP-based RTMP stream started');
      return true;
      
    } catch (error) {
      console.error('❌ Failed to start HTTP RTMP stream:', error);
      return false;
    }
  }

  async uploadMediaChunk(blob, apiBaseUrl) {
    try {
      const formData = new FormData();
      formData.append('chunk', blob);
      formData.append('sessionId', this.sessionId);
      formData.append('timestamp', Date.now());
      
      const response = await fetch(`${apiBaseUrl}/api/rtmp-relay/upload-chunk`, {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
    } catch (error) {
      console.error('❌ Failed to upload media chunk:', error);
    }
  }
}

export default new WebRTCToRTMPService();
