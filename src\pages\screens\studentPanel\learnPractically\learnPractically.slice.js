import { createSlice } from '@reduxjs/toolkit';
import { VirtualLabsApi } from '../../../../redux/api/api';

const initialState = {
  learnPracticallyData: null,
  simulationContent: null
};

export const learnPracticallyApiSlice = VirtualLabsApi.injectEndpoints({
  endpoints: (builder) => ({
    getLearnPracticallyService: builder.query({
      query: (query) => {
        return `/api/simulations/${query}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['VirtualLabs']
    }),
    getLearnPracticallyServiceById: builder.query({
      query: (query) => {
        console.log('Query for simulation by ID:', query);

        return `/api/simulation/${query.subjectName}/${query.simulationId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['VirtualLabs']
    })
  })
});

const learnPracticallySlice = createSlice({
  name: 'learnPractically',
  initialState,
  reducers: {
    setLearnPracticallyData(state, action) {
      state.learnPracticallyData = action.payload;
    },
    clearLearnPracticallyData(state) {
      state.learnPracticallyData = null;
    },
    setSimulationContent(state, action) {
      state.simulationContent = action.payload;
    },
    clearSimulationContent(state) {
      state.simulationContent = null;
    }
  }
});

export const {
  useLazyGetLearnPracticallyServiceQuery,
  useLazyGetLearnPracticallyServiceByIdQuery
} = learnPracticallyApiSlice;
export const {
  setLearnPracticallyData,
  clearLearnPracticallyData,
  setSimulationContent,
  clearSimulationContent
} = learnPracticallySlice.actions;
export const selectLearnPracticallyData = (state) => state.learnPractically.learnPracticallyData;
export default learnPracticallySlice.reducer;
