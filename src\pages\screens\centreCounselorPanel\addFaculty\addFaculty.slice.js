import { centerCounselorPanelApi } from '../../../../redux/api/api';

// const initialState = {};

export const addFacultySlice = centerCounselorPanelApi.injectEndpoints({
  endpoints: (builder) => ({
    addFaculty: builder.mutation({
      query: (body) => ({
        url: '/submit-faculty-request',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CenterCounselor']
    })
  })
});

export const { useAddFacultyMutation } = addFacultySlice;
