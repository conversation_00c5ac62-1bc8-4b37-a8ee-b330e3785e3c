import { useState, useEffect } from 'react';
import {
  useLazyGetEventsByTeacherQuery,
  useLazyGetEventDocumentUrlQuery
} from './scheduleEvents.slice';
import Button from '../../../../components/Field/Button';

function ScheduleEvents() {
  const [teacherId, setTeacherId] = useState('');
  const [getEventsByTeacher, { data: eventsData, isLoading, error }] =
    useLazyGetEventsByTeacherQuery();
  const [getEventDocumentUrl] = useLazyGetEventDocumentUrlQuery();
  const [loadingDoc, setLoadingDoc] = useState(null);

  useEffect(() => {
    // Get user ID from sessionStorage dynamically
    const userId = sessionStorage.getItem('userId');
    if (userId) {
      setTeacherId(userId);
      getEventsByTeacher(userId);
    }
  }, [getEventsByTeacher]);

  useEffect(() => {
    if (teacherId) {
      getEventsByTeacher(teacherId);
    }
  }, [teacherId, getEventsByTeacher]);

  // Handle opening document in new tab using presigned URL from backend
  const handleDocumentOpen = async (eventId, documentName, docId, documentType) => {
    if (!eventId || !documentName || !documentType) {
      alert('Required document information is missing');
      return;
    }

    try {
      setLoadingDoc(docId);

      // Get presigned document URL from backend
      const response = await getEventDocumentUrl({
        eventId: eventId,
        documentType: documentType
      }).unwrap();

      // Check if we got a valid download URL (matching your backend response)
      if (response && response.download_url) {
        // Open document in new tab using presigned URL
        window.open(response.download_url, '_blank', 'noopener,noreferrer');
      } else {
        alert('Unable to get document URL. Please try again.');
      }
    } catch (error) {
      console.error('Error opening document:', error);

      // Handle different error types based on your backend responses
      if (error.status === 401) {
        alert('Authentication failed. Please login again.');
      } else if (error.status === 403) {
        alert('Access denied. You do not have permission to access this document.');
      } else if (error.status === 404) {
        alert(
          error.data?.message || 'Document not found. The file may have been moved or deleted.'
        );
      } else if (error.status === 400) {
        alert(error.data?.message || 'Invalid request. Please check the document information.');
      } else {
        alert(
          `Failed to open document: ${error.data?.message || error.message || 'Unknown error'}`
        );
      }
    } finally {
      setLoadingDoc(null);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString) => {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  // Function to check if event has already occurred
  const isEventPast = (eventDate, eventTime) => {
    try {
      const now = new Date();
      const [hours, minutes] = eventTime.split(':');
      const eventDateTime = new Date(eventDate);
      eventDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

      // Add a small buffer (5 minutes) to account for timezone differences
      const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds
      return eventDateTime.getTime() + bufferTime < now.getTime();
    } catch (error) {
      console.error('Error checking event time:', error);
      // If there's an error parsing the date/time, assume event is not past
      return false;
    }
  };

  // Function to get event status
  const getEventStatus = (eventDate, eventTime) => {
    try {
      const now = new Date();
      const [hours, minutes] = eventTime.split(':');
      const eventDateTime = new Date(eventDate);
      eventDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

      const timeDiff = eventDateTime.getTime() - now.getTime();
      const hoursDiff = timeDiff / (1000 * 60 * 60);

      if (timeDiff < -5 * 60 * 1000) {
        // More than 5 minutes past
        return { status: 'completed', label: 'Completed', color: 'bg-red-100 text-red-800' };
      } else if (timeDiff < 0) {
        // Within 5 minutes past
        return {
          status: 'just-ended',
          label: 'Just Ended',
          color: 'bg-orange-100 text-orange-800'
        };
      } else if (hoursDiff <= 1) {
        // Within 1 hour
        return {
          status: 'starting-soon',
          label: 'Starting Soon',
          color: 'bg-yellow-100 text-yellow-800'
        };
      } else if (hoursDiff <= 24) {
        // Within 24 hours
        return { status: 'today', label: 'Today', color: 'bg-blue-100 text-blue-800' };
      } else {
        return { status: 'upcoming', label: 'Upcoming', color: 'bg-green-100 text-green-800' };
      }
    } catch (error) {
      console.error('Error getting event status:', error);
      return { status: 'unknown', label: 'Unknown', color: 'bg-gray-100 text-gray-800' };
    }
  };

  // Handle start streaming
  const handleStartStreaming = (event) => {
    // Add your streaming logic here
    console.log('Starting stream for event:', event);
    // Navigate to TeacherLiveStreaming component using window.location
    window.location.href = '/sasthra/teacher/live-streaming';
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-lg">Loading events...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-red-500 text-lg">
          Error loading events: {error.message || 'Something went wrong'}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Schedule Events</h1>
          <p className="text-gray-600">Manage and view your scheduled events</p>
        </div>

        {/* Events Display */}
        {eventsData && eventsData.events && eventsData.events.length > 0 ? (
          <div className="space-y-6">
            <div className="bg-white p-4 rounded-lg shadow">
              <h2 className="text-xl font-semibold text-gray-800 mb-2">Events Summary</h2>
              <p className="text-gray-600">
                Total Events: <span className="font-medium">{eventsData.total_count}</span>
              </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {eventsData.events.map((event) => (
                <div
                  key={event.id}
                  className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
                >
                  <div className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <h3 className="text-lg font-semibold text-gray-900 truncate">
                        {event.topic}
                      </h3>
                      <div className="flex flex-col items-end space-y-1">
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                          Lecture {event.lecture_number}
                        </span>
                        {(() => {
                          const eventStatus = getEventStatus(event.event_date, event.event_time);
                          return (
                            <span className={`px-2 py-1 text-xs rounded-full ${eventStatus.color}`}>
                              {eventStatus.label}
                            </span>
                          );
                        })()}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center text-sm text-gray-600">
                        <span className="font-medium w-20">Subject:</span>
                        <span className="capitalize">{event.subject}</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-600">
                        <span className="font-medium w-20">Course:</span>
                        <span>{event.course_name}</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-600">
                        <span className="font-medium w-20">Batch:</span>
                        <span>{event.batch_name}</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-600">
                        <span className="font-medium w-20">Chapter:</span>
                        <span className="capitalize">{event.chapter_name}</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-600">
                        <span className="font-medium w-20">Teacher:</span>
                        <span>{event.kota_teacher_name}</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-600">
                        <span className="font-medium w-20">Date:</span>
                        <span>{formatDate(event.event_date)}</span>
                      </div>

                      <div className="flex items-center text-sm text-gray-600">
                        <span className="font-medium w-20">Time:</span>
                        <span>{formatTime(event.event_time)}</span>
                      </div>
                    </div>

                    {/* Document Downloads */}
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex flex-col space-y-2">
                        {event.event_content_document_name && (
                          <button
                            onClick={() =>
                              handleDocumentOpen(
                                event.id,
                                event.event_content_document_name,
                                `content-${event.id}`,
                                'event_content'
                              )
                            }
                            disabled={loadingDoc === `content-${event.id}`}
                            className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center justify-start p-2 rounded hover:bg-blue-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {loadingDoc === `content-${event.id}` ? (
                              <>
                                <span className="animate-spin mr-2">⏳</span>
                                Opening...
                              </>
                            ) : (
                              <>📄 {event.event_content_document_name}</>
                            )}
                          </button>
                        )}
                        {event.notes_document_name && (
                          <button
                            onClick={() =>
                              handleDocumentOpen(
                                event.id,
                                event.notes_document_name,
                                `notes-${event.id}`,
                                'notes'
                              )
                            }
                            disabled={loadingDoc === `notes-${event.id}`}
                            className="text-green-600 hover:text-green-800 text-sm font-medium flex items-center justify-start p-2 rounded hover:bg-green-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {loadingDoc === `notes-${event.id}` ? (
                              <>
                                <span className="animate-spin mr-2">⏳</span>
                                Opening...
                              </>
                            ) : (
                              <>📝 {event.notes_document_name}</>
                            )}
                          </button>
                        )}
                      </div>
                    </div>

                    {/* Start Streaming Button - Only show if event hasn't occurred yet */}
                    {!isEventPast(event.event_date, event.event_time) && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <Button
                          name="Start Streaming"
                          onClick={() => handleStartStreaming(event)}
                          className="w-full bg-[var(--color-teacher)] hover:bg-[#5bb82a] text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
                          icon={<span className="mr-2">🎥</span>}
                          tooltip="Start live streaming for this event"
                        />
                      </div>
                    )}

                    <div className="mt-4 text-xs text-gray-500">
                      Created: {new Date(event.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <div className="text-gray-500 text-lg mb-2">No events found</div>
            <p className="text-gray-400">No scheduled events available for this teacher.</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default ScheduleEvents;
