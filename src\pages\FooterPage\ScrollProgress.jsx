import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

const ScrollProgress = () => {
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const updateScrollProgress = () => {
      const scrollPosition = window.scrollY;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      const maxScroll = documentHeight - windowHeight;
      const progress = (scrollPosition / maxScroll) * 100;

      setScrollProgress(progress);
    };

    window.addEventListener('scroll', updateScrollProgress);
    window.addEventListener('resize', updateScrollProgress);

    return () => {
      window.removeEventListener('scroll', updateScrollProgress);
      window.removeEventListener('resize', updateScrollProgress);
    };
  }, []);

  return (
    <motion.div
      className="fixed top-0 left-0 h-1 z-50 bg-gradient-to-r from-primary to-accent"
      style={{ width: `${scrollProgress}%` }}
      initial={{ width: 0 }}
      transition={{ type: 'spring', damping: 30, stiffness: 100 }}
    />
  );
};

export default ScrollProgress;
