import React, { useEffect } from 'react';
import { useLazyGetCenterStudentsFacultyQuery } from './centerStudentsFaculty.slice';

const CenterStudentsFaculty = () => {
  const [trigger, { data, error, isLoading }] = useLazyGetCenterStudentsFacultyQuery();

  useEffect(() => {
    trigger();
  }, [trigger]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div>
      <h2>Center Students and Faculty</h2>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
};

export default CenterStudentsFaculty;
