import { teacherDashboardApi } from '../../../../redux/api/api';
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  scheduleEventsData: null
};

export const scheduleEventsSlice = teacherDashboardApi.injectEndpoints({
  endpoints: (builder) => ({
    getEventsByTeacher: builder.query({
      query: (teacherId) => `/events/by-teacher/${teacherId}`,
      transformResponse: (response) => {
        console.log('Events by Teacher Data:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['TeacherDashboard']
    }),

    getEventDocumentUrl: builder.query({
      query: ({ eventId, documentType }) => `/events/${eventId}/documents/${documentType}/download`,
      transformResponse: (response) => {
        console.log('Document URL Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['TeacherDashboard']
    })
  })
});

const ScheduleEventsSlice = createSlice({
  name: 'scheduleEvents',
  initialState,
  reducers: {
    setScheduleEventsData(state, action) {
      state.scheduleEventsData = action.payload;
    }
  }
});

// Export hooks
export const { useLazyGetEventsByTeacherQuery, useLazyGetEventDocumentUrlQuery } =
  scheduleEventsSlice;

export const { setScheduleEventsData } = ScheduleEventsSlice.actions;
export default ScheduleEventsSlice.reducer;
