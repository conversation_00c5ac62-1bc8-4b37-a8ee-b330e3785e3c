/**
 * ROBUST STREAMING MANAGER - ZOOM/GOOGLE MEET LEVEL
 * Handles background streaming with multiple protection layers
 * Ensures streaming continues regardless of tab switching
 */

class RobustStreamingManager {
  constructor() {
    // Check if we're in a browser environment
    this.isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined';

    this.isActive = false;
    this.mediaStream = null;
    this.livekitRoom = null;
    this.localVideoTrack = null;
    this.localAudioTrack = null;
    this.backgroundWorker = null;
    this.wakeLock = null;
    this.keepAliveInterval = null;
    this.connectionMonitorInterval = null;
    this.isTabVisible = true;
    this.lastActivity = Date.now();
    this.forceKeepAlive = true;

    // Initialize handlers as null
    this.handleVisibilityChange = null;
    this.handleBeforeUnload = null;
    this.handlePageHide = null;
    this.handlePageFocus = null;
    this.handleWindowBlur = null;
    this.handleWorkerMessage = null;
  }

  // Initialize robust streaming
  async initialize(config) {
    try {
      console.log('🚀 Initializing ROBUST streaming manager...', config);

      // Check if we're in a browser environment
      if (!this.isBrowser) {
        console.warn('⚠️ Not in browser environment, skipping robust streaming manager');
        return false;
      }

      this.config = config;
      this.isActive = true;

      // 1. Set up page visibility monitoring
      this.setupPageVisibilityMonitoring();

      // 2. Set up wake lock with aggressive retry
      await this.setupWakeLock();

      // 3. Set up background worker communication
      await this.setupBackgroundWorker();

      // 4. Set up aggressive keep-alive
      this.setupKeepAlive();

      // 5. Set up connection monitoring
      this.setupConnectionMonitoring();

      // 6. Set up media stream protection
      await this.setupMediaStreamProtection();

      console.log('✅ ROBUST streaming manager initialized');
      return true;

    } catch (error) {
      console.error('❌ Failed to initialize robust streaming manager:', error);
      throw error;
    }
  }

  // Set up page visibility monitoring with multiple events
  setupPageVisibilityMonitoring() {
    if (!this.isBrowser) return;
    // Primary visibility API
    this.handleVisibilityChange = () => {
      this.isTabVisible = !document.hidden;
      this.lastActivity = Date.now();

      console.log('👁️ Visibility changed:', this.isTabVisible ? 'visible' : 'hidden');

      if (!this.isTabVisible && this.isActive) {
        console.log('🔄 Tab hidden, activating protection mechanisms...');
        this.activateBackgroundProtection();
      } else if (this.isTabVisible && this.isActive) {
        console.log('✅ Tab visible, verifying stream integrity...');
        this.verifyStreamIntegrity();
      }

      // Notify service worker
      if (this.backgroundWorker) {
        this.backgroundWorker.postMessage({
          type: 'TAB_VISIBILITY_CHANGED',
          data: { visible: this.isTabVisible }
        });
      }
    };

    // Multiple event listeners for comprehensive coverage
    document.addEventListener('visibilitychange', this.handleVisibilityChange);

    // Page lifecycle events
    this.handlePageHide = () => {
      console.log('📴 Page hide event - activating emergency protection');
      this.activateEmergencyProtection();
    };

    this.handlePageFocus = () => {
      console.log('🔍 Page focus event - verifying stream');
      this.verifyStreamIntegrity();
    };

    this.handleWindowBlur = () => {
      console.log('🌫️ Window blur - maintaining stream');
      this.activateBackgroundProtection();
    };

    window.addEventListener('pagehide', this.handlePageHide);
    window.addEventListener('focus', this.handlePageFocus);
    window.addEventListener('blur', this.handleWindowBlur);

    // Before unload protection
    this.handleBeforeUnload = (e) => {
      if (this.isActive) {
        e.preventDefault();
        e.returnValue = 'Streaming is active. Are you sure you want to leave?';
        return e.returnValue;
      }
    };

    window.addEventListener('beforeunload', this.handleBeforeUnload);
  }

  // Set up wake lock with aggressive retry
  async setupWakeLock() {
    if (!this.isBrowser || !('wakeLock' in navigator)) {
      console.warn('⚠️ Wake Lock API not supported or not in browser');
      return;
    }
    
    const acquireWakeLock = async () => {
      try {
        if (!this.wakeLock) {
          this.wakeLock = await navigator.wakeLock.request('screen');
          console.log('✅ Wake Lock acquired');
          
          this.wakeLock.addEventListener('release', () => {
            console.log('🔓 Wake Lock released, re-acquiring...');
            this.wakeLock = null;
            
            // Immediate re-acquisition
            if (this.isActive) {
              setTimeout(acquireWakeLock, 100);
            }
          });
        }
      } catch (error) {
        console.error('❌ Failed to acquire Wake Lock:', error);
        
        // Retry after delay
        if (this.isActive) {
          setTimeout(acquireWakeLock, 5000);
        }
      }
    };
    
    await acquireWakeLock();
    
    // Periodic wake lock check
    setInterval(() => {
      if (this.isActive && !this.wakeLock) {
        acquireWakeLock();
      }
    }, 30000);
  }

  // Set up background worker communication
  async setupBackgroundWorker() {
    if (!this.isBrowser || !('serviceWorker' in navigator)) {
      console.warn('⚠️ Service Worker not supported or not in browser');
      return;
    }

    try {
      // Use the existing service worker
      const registration = await navigator.serviceWorker.ready;
      this.backgroundWorker = registration.active;

      if (this.backgroundWorker) {
        // Create the handler if it doesn't exist
        if (!this.handleWorkerMessage) {
          this.handleWorkerMessage = (event) => {
            const { type } = event.data;

            switch (type) {
              case 'PING_REQUEST':
                // Respond to service worker ping
                if (this.backgroundWorker) {
                  this.backgroundWorker.postMessage({
                    type: 'PING_RESPONSE',
                    data: { timestamp: Date.now() }
                  });
                }
                break;

              case 'CHECK_TAB_VISIBILITY':
                // Report current visibility
                if (this.backgroundWorker) {
                  this.backgroundWorker.postMessage({
                    type: 'TAB_VISIBILITY_CHANGED',
                    data: { visible: this.isTabVisible }
                  });
                }
                break;

              case 'EMERGENCY_MODE_ACTIVE':
                console.log('🚨 Service worker in emergency mode, activating local recovery');
                if (this.activateEmergencyProtection) {
                  this.activateEmergencyProtection();
                }
                break;
            }
          };
        }

        navigator.serviceWorker.addEventListener('message', this.handleWorkerMessage);
        console.log('✅ Background worker communication established');
      }
    } catch (error) {
      console.error('❌ Failed to set up background worker:', error);
    }
  }



  // Set up aggressive keep-alive
  setupKeepAlive() {
    this.keepAliveInterval = setInterval(() => {
      if (!this.isActive) return;
      
      this.lastActivity = Date.now();
      
      // Force activity on media tracks
      if (this.localVideoTrack && this.localVideoTrack.mediaStreamTrack) {
        const track = this.localVideoTrack.mediaStreamTrack;
        if (track.readyState === 'live') {
          // Track is alive, good
          console.log('💓 Video track alive');
        } else {
          console.log('⚠️ Video track not live, attempting restart');
          this.restartVideoTrack();
        }
      }
      
      if (this.localAudioTrack && this.localAudioTrack.mediaStreamTrack) {
        const track = this.localAudioTrack.mediaStreamTrack;
        if (track.readyState === 'live') {
          console.log('💓 Audio track alive');
        } else {
          console.log('⚠️ Audio track not live, attempting restart');
          this.restartAudioTrack();
        }
      }
      
      // Check LiveKit connection
      if (this.livekitRoom) {
        const state = this.livekitRoom.state;
        if (state === 'disconnected') {
          console.log('🔄 LiveKit disconnected, reconnecting...');
          this.reconnectLiveKit();
        }
      }
      
    }, 10000); // Every 10 seconds
  }

  // Set up connection monitoring
  setupConnectionMonitoring() {
    this.connectionMonitorInterval = setInterval(() => {
      if (!this.isActive) return;
      
      // Monitor connection health
      if (this.livekitRoom) {
        const participants = this.livekitRoom.participants.size;
        const connectionQuality = this.livekitRoom.engine?.connectionQuality || 'unknown';
        
        console.log('📊 Connection status:', {
          state: this.livekitRoom.state,
          participants,
          quality: connectionQuality,
          tabVisible: this.isTabVisible
        });
      }
      
    }, 30000); // Every 30 seconds
  }

  // Set up media stream protection
  async setupMediaStreamProtection() {
    // This will be called when media streams are available
    console.log('🛡️ Media stream protection ready');
  }

  // Activate background protection when tab becomes hidden
  activateBackgroundProtection() {
    console.log('🛡️ Activating background protection...');
    
    // Ensure wake lock
    if (!this.wakeLock && 'wakeLock' in navigator) {
      this.setupWakeLock();
    }
    
    // Notify service worker
    if (this.backgroundWorker) {
      this.backgroundWorker.postMessage({
        type: 'TAB_VISIBILITY_CHANGED',
        data: { visible: false }
      });
    }
    
    // Force keep tracks alive
    this.forceKeepTracksAlive();
  }

  // Activate emergency protection
  activateEmergencyProtection() {
    console.log('🚨 Activating EMERGENCY protection...');
    
    // All protection mechanisms at once
    this.activateBackgroundProtection();
    
    // Notify service worker of emergency
    if (this.backgroundWorker) {
      this.backgroundWorker.postMessage({
        type: 'EMERGENCY_RECOVERY'
      });
    }
    
    // Force reconnection if needed
    if (this.livekitRoom && this.livekitRoom.state === 'disconnected') {
      this.reconnectLiveKit();
    }
  }

  // Verify stream integrity when tab becomes visible
  verifyStreamIntegrity() {
    console.log('🔍 Verifying stream integrity...');
    
    // Check all components
    if (this.livekitRoom && this.livekitRoom.state === 'disconnected') {
      this.reconnectLiveKit();
    }
    
    if (this.localVideoTrack && this.localVideoTrack.mediaStreamTrack.readyState !== 'live') {
      this.restartVideoTrack();
    }
    
    if (this.localAudioTrack && this.localAudioTrack.mediaStreamTrack.readyState !== 'live') {
      this.restartAudioTrack();
    }
  }

  // Force keep tracks alive
  forceKeepTracksAlive() {
    if (this.localVideoTrack) {
      try {
        this.localVideoTrack.restart();
      } catch (error) {
        console.warn('⚠️ Failed to restart video track:', error);
      }
    }
    
    if (this.localAudioTrack) {
      try {
        this.localAudioTrack.restart();
      } catch (error) {
        console.warn('⚠️ Failed to restart audio track:', error);
      }
    }
  }

  // Set media tracks for protection
  setMediaTracks(videoTrack, audioTrack) {
    this.localVideoTrack = videoTrack;
    this.localAudioTrack = audioTrack;
    console.log('📹 Media tracks set for protection');
  }

  // Set LiveKit room for protection
  setLiveKitRoom(room) {
    this.livekitRoom = room;
    console.log('🔗 LiveKit room set for protection');
  }

  // Restart video track
  async restartVideoTrack() {
    try {
      if (this.localVideoTrack) {
        await this.localVideoTrack.restart();
        console.log('✅ Video track restarted');
      }
    } catch (error) {
      console.error('❌ Failed to restart video track:', error);
    }
  }

  // Restart audio track
  async restartAudioTrack() {
    try {
      if (this.localAudioTrack) {
        await this.localAudioTrack.restart();
        console.log('✅ Audio track restarted');
      }
    } catch (error) {
      console.error('❌ Failed to restart audio track:', error);
    }
  }

  // Reconnect LiveKit
  async reconnectLiveKit() {
    try {
      if (this.livekitRoom && this.config) {
        await this.livekitRoom.connect(this.config.livekitUrl, this.config.livekitToken);
        console.log('✅ LiveKit reconnected');
      }
    } catch (error) {
      console.error('❌ Failed to reconnect LiveKit:', error);
    }
  }

  // Stop robust streaming
  async stop() {
    console.log('🛑 Stopping robust streaming manager...');
    
    this.isActive = false;
    
    // Clear intervals
    if (this.keepAliveInterval) {
      clearInterval(this.keepAliveInterval);
    }
    
    if (this.connectionMonitorInterval) {
      clearInterval(this.connectionMonitorInterval);
    }
    
    // Remove event listeners
    if (this.handleVisibilityChange) {
      document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    }

    if (this.handleBeforeUnload) {
      window.removeEventListener('beforeunload', this.handleBeforeUnload);
    }

    if (this.handlePageHide) {
      window.removeEventListener('pagehide', this.handlePageHide);
    }

    if (this.handlePageFocus) {
      window.removeEventListener('focus', this.handlePageFocus);
    }

    if (this.handleWindowBlur) {
      window.removeEventListener('blur', this.handleWindowBlur);
    }
    
    // Release wake lock
    if (this.wakeLock) {
      try {
        await this.wakeLock.release();
        this.wakeLock = null;
      } catch (error) {
        console.error('❌ Failed to release wake lock:', error);
      }
    }
    
    // Stop service worker background streaming
    if (this.backgroundWorker) {
      this.backgroundWorker.postMessage({
        type: 'STOP_BACKGROUND_STREAMING'
      });
      
      navigator.serviceWorker.removeEventListener('message', this.handleWorkerMessage);
    }
    
    console.log('✅ Robust streaming manager stopped');
  }
}

// Create singleton instance with comprehensive error handling
let robustStreamingManager;

// Check if we're in a browser environment first
const isBrowserEnvironment = typeof window !== 'undefined' && typeof document !== 'undefined';

if (isBrowserEnvironment) {
  try {
    robustStreamingManager = new RobustStreamingManager();
    console.log('✅ RobustStreamingManager created successfully');
  } catch (error) {
    console.error('❌ Failed to create RobustStreamingManager:', error);
    robustStreamingManager = null;
  }
}

// If creation failed or not in browser, use fallback
if (!robustStreamingManager) {
  console.log('🔄 Using fallback streaming manager implementation');

  robustStreamingManager = {
    isActive: false,
    isBrowser: isBrowserEnvironment,

    async initialize(config) {
      console.log('🔄 Fallback: Initialize called', config ? 'with config' : 'without config');
      this.isActive = true;
      return true;
    },

    setMediaTracks(videoTrack, audioTrack) {
      console.log('📹 Fallback: Media tracks set', videoTrack ? 'video' : 'no video', audioTrack ? 'audio' : 'no audio');
    },

    setLiveKitRoom(room) {
      console.log('🔗 Fallback: LiveKit room set', room ? 'with room' : 'without room');
    },

    async stop() {
      console.log('🛑 Fallback: Stopping');
      this.isActive = false;
    }
  };
}

export default robustStreamingManager;
