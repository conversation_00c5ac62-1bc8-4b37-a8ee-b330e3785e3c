import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  UploadCloud,
  FileText,
  Loader2,
  CheckCircle,
  XCircle,
  DownloadCloud,
  BookOpen,
  Bookmark,
  BookmarkCheck,
  ChevronDown,
  ChevronUp,
  Stars,
  Sparkles,
  PartyPopper
} from 'lucide-react';
import {
  useLazyGetPdfQuestionGeneratorCreateServiceQuery,
  useGetPdfQuestionGeneratorUploadServiceMutation
} from './pdfQuestionGenerator.slice';

const PdfQuestionGenerator = () => {
  const [file, setFile] = useState(null);
  const [fileName, setFileName] = useState('');
  const userId = sessionStorage.getItem('userId');
  const [response, setResponse] = useState(null);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const fileInputRef = useRef(null);
  const [pdfCreateGenerator] = useLazyGetPdfQuestionGeneratorCreateServiceQuery();
  const [pdfUpload] = useGetPdfQuestionGeneratorUploadServiceMutation();

  // Supported file types
  const supportedFileTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile && supportedFileTypes.includes(selectedFile.type)) {
      setFile(selectedFile);
      setFileName(selectedFile.name);
      setError('');
    } else {
      setError('Please upload a PDF, JPG, JPEG, or PNG file only');
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile && supportedFileTypes.includes(droppedFile.type)) {
      setFile(droppedFile);
      setFileName(droppedFile.name);
      setError('');
    } else {
      setError('Please upload a PDF, JPG, JPEG, or PNG file only');
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file || !userId) {
      setError('Please upload a file and ensure user ID is available');
      return;
    }

    const formData = new FormData();
    formData.append('file', file);
    formData.append('user_id', userId);

    setLoading(true);
    try {
      const res = await pdfUpload(formData).unwrap();
      setResponse(res);
      setError('');
    } catch (err) {
      setError(err?.data?.error || 'Failed to process file. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadPDF = async (questionId) => {
    try {
      const res = await pdfCreateGenerator({ question_id: questionId }, true);
      const blob = await res.data;
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `questions_${questionId}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();

      // Trigger confetti effect
      triggerConfetti();
    } catch (err) {
      setError(err);
    }
  };

  const triggerConfetti = () => {
    const confettiContainer = document.createElement('div');
    confettiContainer.className = 'fixed inset-0 pointer-events-none z-[9999]';
    document.body.appendChild(confettiContainer);

    const confettiTypes = [
      { class: 'w-1.5 h-1.5 rounded-full', count: 100 },
      { class: 'w-1 h-3', count: 80 },
      { class: 'w-2 h-2 rounded-sm', count: 70 },
      { class: 'w-1 h-1 rounded-full', count: 120 }
    ];

    confettiTypes.forEach((type) => {
      for (let i = 0; i < type.count; i++) {
        const confetti = document.createElement('div');
        confetti.className = `absolute ${type.class}`;
        const startX = Math.random() * 100;
        const endX = startX + (Math.random() * 60 - 30);
        const hue = Math.random() * 360;
        const saturation = 80 + Math.random() * 20;
        const lightness = 50 + Math.random() * 20;
        confetti.style.backgroundColor = `hsl(${hue}, ${saturation}%, ${lightness}%)`;
        confetti.style.left = `${startX}%`;
        confetti.style.top = '-5px';
        confetti.style.opacity = '0.9';
        confetti.style.transform = `rotate(${Math.random() * 360}deg)`;
        const duration = Math.random() * 2 + 1;
        confetti.style.animation = `confetti-fall-${i} ${duration}s ease-in forwards`;
        const style = document.createElement('style');
        style.innerHTML = `
          @keyframes confetti-fall-${i} {
            0% { transform: translateY(0) rotate(0deg); opacity: 1; }
            100% { transform: translateY(100vh) rotate(${Math.random() * 1080}deg) translateX(${endX - startX}vw); opacity: 0; }
          }
        `;
        document.head.appendChild(style);
        confettiContainer.appendChild(confetti);
      }
    });

    for (let i = 0; i < 8; i++) {
      const celeb = document.createElement('div');
      celeb.className = 'absolute text-2xl';
      celeb.style.left = `${5 + i * 12}%`;
      celeb.style.top = '15%';
      celeb.style.opacity = '0';
      celeb.style.animation = `celebrate-${i} 1.5s ease-out forwards`;
      const style = document.createElement('style');
      style.innerHTML = `
        @keyframes celebrate-${i} {
          0% { transform: scale(0) translateY(0); opacity: 0; }
          50% { transform: scale(1.2) translateY(-15px); opacity: 1; }
          100% { transform: scale(0.8) translateY(-30px); opacity: 0; }
        }
      `;
      document.head.appendChild(style);
      celeb.innerHTML = ['🎉', '✨', '🎊', '🥳', '👏', '🌟', '💫', '🎈'][i];
      confettiContainer.appendChild(celeb);
    }

    setTimeout(() => {
      confettiContainer.remove();
      const styles = document.querySelectorAll('style');
      styles.forEach((style) => {
        if (style.innerHTML.includes('confetti-fall-') || style.innerHTML.includes('celebrate-')) {
          style.remove();
        }
      });
    }, 3000);
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 flex items-center justify-center p-4 relative overflow-hidden">
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(10)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-blue-200/20"
            style={{
              width: `${Math.random() * 200 + 100}px`,
              height: `${Math.random() * 200 + 100}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              filter: 'blur(40px)'
            }}
          />
        ))}
      </div>

      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, type: 'spring' }}
        className="w-full max-w-3xl relative z-10"
      >
        <motion.div
          className="bg-white rounded-3xl overflow-hidden shadow-2xl border border-white/20 backdrop-blur-sm"
          whileHover={{ y: -5, boxShadow: '0 20px 40px -10px rgba(59, 130, 246, 0.3)' }}>
          <motion.div
            className="bg-gradient-to-r from-blue-600 to-indigo-600 p-6 relative overflow-hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <div className="absolute inset-0 bg-noise opacity-10"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent"></div>
            <div className="flex items-center justify-between relative z-10">
              <div className="flex items-center space-x-3">
                <motion.div
                  whileHover={{ rotate: 15, scale: 1.1 }}
                  className="p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20"
                >
                  <FileText className="h-8 w-8 text-white" />
                </motion.div>
                <div>
                  <h1 className="text-2xl font-bold text-white drop-shadow-md">
                    Question Generator
                  </h1>
                  <p className="text-sm text-blue-100/90">
                    Transform your PDFs or images into practice questions
                  </p>
                </div>
              </div>
              <motion.div
                whileHover={{ rotate: 15, scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="bg-white/20 p-2 rounded-lg backdrop-blur-sm border border-white/20 shadow-sm"
              >
                <PartyPopper className="h-6 w-6 text-white" />
              </motion.div>
            </div>
          </motion.div>

          <div className="p-6 relative">
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <motion.div
                className={`relative border-2 border-dashed rounded-2xl p-8 text-center cursor-pointer transition-all ${isDragging ? 'border-blue-400 bg-blue-50/50' : 'border-gray-300 hover:border-blue-500 bg-white/50'}`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current.click()}
                whileHover={{ scale: 1.01 }}
              >
                <div className="absolute inset-0 bg-grid-blue-100/[0.03] rounded-xl"></div>
                <input
                  ref={fileInputRef}
                  id="file-upload"
                  type="file"
                  onChange={handleFileChange}
                  accept="application/pdf,image/jpeg,image/jpg,image/png"
                  className="hidden"
                />
                <motion.div className="flex flex-col items-center justify-center space-y-3 relative z-10">
                  <motion.div
                    animate={{
                      y: [0, -5, 0],
                      rotate: isDragging ? 10 : 0
                    }}
                    transition={{
                      y: { repeat: Infinity, duration: 2, ease: 'easeInOut' },
                      rotate: { duration: 0.2 }
                    }}
                  >
                    <UploadCloud
                      className={`h-12 w-12 ${isDragging ? 'text-blue-500' : 'text-gray-400'}`}
                    />
                  </motion.div>
                  <p className="text-lg font-medium text-gray-700">
                    {isDragging
                      ? 'Drop your file here'
                      : fileName ||
                        'Drag & drop your PDF or image (JPG, JPEG, PNG) or click to browse'}
                  </p>
                  <p className="text-sm text-gray-500">
                    Supports PDF, JPG, JPEG, PNG files up to 10MB
                  </p>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="mt-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full text-sm text-white hover:from-blue-600 hover:to-blue-700 transition-all shadow-md"
                  >
                    Select File
                  </motion.div>
                </motion.div>
              </motion.div>

              {fileName && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-4 flex items-center justify-between bg-blue-50/50 rounded-xl p-3 border border-blue-100 backdrop-blur-sm"
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <FileText className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-gray-800 font-medium truncate max-w-xs">{fileName}</p>
                      <p className="text-xs text-gray-500">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setIsBookmarked(!isBookmarked)}
                      className="p-2 rounded-full hover:bg-blue-100 transition-colors"
                    >
                      {isBookmarked ? (
                        <BookmarkCheck className="h-5 w-5 text-blue-600" />
                      ) : (
                        <Bookmark className="h-5 w-5 text-gray-500 hover:text-blue-600" />
                      )}
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => {
                        setFile(null);
                        setFileName('');
                      }}
                      className="p-2 rounded-full hover:bg-blue-100 text-gray-500 hover:text-red-500 transition-colors"
                    >
                      <XCircle className="h-5 w-5" />
                    </motion.button>
                  </div>
                </motion.div>
              )}
            </motion.div>

            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-4 bg-red-50 border border-red-200 rounded-xl p-3 flex items-start space-x-3"
                >
                  <div className="p-1.5 bg-red-100 rounded-full">
                    <XCircle className="h-5 w-5 text-red-500" />
                  </div>
                  <p className="text-red-600 flex-1">{error}</p>
                  <button onClick={() => setError('')} className="text-red-500 hover:text-red-700">
                    <XCircle className="h-5 w-5" />
                  </button>
                </motion.div>
              )}
            </AnimatePresence>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="mt-6"
            >
              <motion.button
                onClick={handleSubmit}
                disabled={!file || loading}
                className={`w-full py-3.5 rounded-xl hover:cursor-pointer font-medium flex items-center justify-center space-x-2 relative overflow-hidden ${!file || loading ? 'bg-gray-200 text-gray-500 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-blue-500/30'}`}
                whileHover={!file || loading ? {} : { scale: 1.02 }}
                whileTap={!file || loading ? {} : { scale: 0.98 }}
              >
                {loading && (
                  <motion.div
                    className="absolute inset-0 bg-blue-500/30"
                    animate={{ x: [-200, 200] }}
                    transition={{ repeat: Infinity, duration: 1.5, ease: 'linear' }}
                  />
                )}
                {loading ? (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="h-5 w-5" />
                    <span>Generate Questions</span>
                  </>
                )}
              </motion.button>
            </motion.div>

            <AnimatePresence>
              {response && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-6 bg-blue-50 rounded-2xl overflow-hidden border border-blue-100"
                >
                  <div className="p-5">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-green-100 rounded-lg">
                          <CheckCircle className="h-6 w-6 text-green-600" />
                        </div>
                        <div>
                          <h2 className="text-xl font-semibold text-gray-800">
                            Generated Questions
                          </h2>
                          <p className="text-sm text-gray-600">Ready for download</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                       
                        <button
                          onClick={toggleExpand}
                          className="p-2 rounded-full hover:bg-blue-100 text-gray-500 hover:text-gray-700 transition-colors"
                        >
                          {isExpanded ? (
                            <ChevronUp className="h-5 w-5" />
                          ) : (
                            <ChevronDown className="h-5 w-5" />
                          )}
                        </button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <h3 className="text-sm font-medium text-gray-600 mb-1 flex items-center">
                          <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                          Subject
                        </h3>
                        <p className="text-gray-800 font-medium text-lg">{response.subject}</p>
                      </div>
                      <div className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <h3 className="text-sm font-medium text-gray-600 mb-1 flex items-center">
                          <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                          Topic
                        </h3>
                        <p className="text-gray-800 font-medium text-lg">{response.topic}</p>
                      </div>
                    </div>

                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="space-y-4"
                        >
                          <div className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                            <h3 className="text-sm font-medium text-gray-600 mb-2 flex items-center">
                              <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                              Extracted Content
                            </h3>
                            <div className="bg-gray-50 rounded-lg p-3 max-h-40 overflow-y-auto text-gray-700">
                              <p>{response.extracted_question}</p>
                            </div>
                          </div>

                          <div className="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                            <h3 className="text-sm font-medium text-gray-600 mb-2 flex items-center">
                              <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                              Generated Questions
                            </h3>
                            <ul className="space-y-6">
                              {response.questions.map((q, index) => (
                                <motion.li
                                  key={q.id}
                                  initial={{ opacity: 0, x: -10 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ delay: index * 0.05 }}
                                  className="bg-blue-50 rounded-lg p-4 border border-blue-100">
                                  <div className="flex items-start">
                                    <span className="flex-shrink-0 mt-0.5 mr-3">
                                      <Stars className="h-4 w-4 text-blue-500" />
                                    </span>
                                    <div className="flex-1">
                                      <div className="flex items-center">
                                        <span className="text-blue-600 font-medium mr-2">
                                          {q.id}.
                                        </span>
                                        <span className="text-gray-700 font-medium">
                                          {q.question_text}
                                        </span>
                                      </div>
                                      <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-2">
                                        {Object.entries(q.options).map(([key, value]) => (
                                          <div
                                            key={key}
                                            className={`p-2 rounded-md ${
                                              q.correct_answer === key
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-gray-100 text-gray-800'
                                            }`}>
                                            <span className="font-medium">{key}:</span> {value}
                                          </div>
                                        ))}
                                      </div>
                                      <div className="mt-2">
                                        <p className="text-sm text-gray-600">
                                          <span className="font-medium">Solution:</span>{' '}
                                          {q.solution}
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </motion.li>
                              ))}
                            </ul>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    <motion.div
                      className="mt-6"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      <motion.button
                        onClick={() => handleDownloadPDF(response.question_id)}
                        className="w-full py-3.5 bg-gradient-to-r  from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 hover:cursor-pointer text-white rounded-xl font-medium flex items-center justify-center space-x-2 relative overflow-hidden shadow-lg hover:shadow-green-500/30"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <DownloadCloud className="h-5 w-5" />
                        <span>Download Question PDF</span>
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/30 to-white/0"
                          animate={{ x: [-200, 200] }}
                          transition={{ repeat: Infinity, duration: 2, ease: 'linear' }}
                        />
                      </motion.button>
                    </motion.div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default PdfQuestionGenerator;
