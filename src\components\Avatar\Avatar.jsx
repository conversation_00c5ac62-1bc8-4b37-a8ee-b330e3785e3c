import { useRef, useEffect, useState, useMemo, useCallback } from 'react';
import { useGLTF } from '@react-three/drei';
import { useFrame, useLoader } from '@react-three/fiber';
import { useGraph } from '@react-three/fiber';
import { SkeletonUtils } from 'three-stdlib';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader';
import * as THREE from 'three';

// Browser detection utility
const getBrowserInfo = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  const isChrome =
    userAgent.includes('chrome') && !userAgent.includes('edg') && !userAgent.includes('brave');
  const isEdge = userAgent.includes('edg');
  const isBrave =
    userAgent.includes('brave') ||
    (userAgent.includes('chrome') && !window.chrome?.runtime?.onConnect);
  const isFirefox = userAgent.includes('firefox');

  return {
    isChrome,
    isEdge,
    isBrave,
    isFirefox,
    isProblematicBrowser: isChrome || isEdge,
    browserName: isChrome
      ? 'Chrome'
      : isEdge
        ? 'Edge'
        : isBrave
          ? 'Brave'
          : isFirefox
            ? 'Firefox'
            : 'Unknown'
  };
};

// Web Worker for heavy computations
const createModelWorker = () => {
  const workerCode = `
    // Web Worker for model processing
    let processedPhonemes = [];
    let visemeMap = {};

    self.onmessage = function(e) {
      const { type, data } = e.data;

      switch(type) {
        case 'INIT_VISEME_MAP':
          visemeMap = data;
          break;

        case 'PROCESS_PHONEMES':
          if (data) {
            // Parse phonemes in format: "H-ph e-ph l-ph l-ph o-ph ,-ph  -ph t-ph h-ph i-ph s-ph"
            processedPhonemes = data.split(" ").map((p) => {
              const phoneme = p.replace("-ph", "").trim();
              return {
                phoneme: phoneme,
                visemeData: visemeMap[phoneme] || {}
              };
            });
          } else {
            processedPhonemes = [];
          }
          self.postMessage({ type: 'PHONEMES_PROCESSED', data: processedPhonemes });
          break;

        case 'GET_ACTIVE_VISEME':
          const { currentTime, phonemesPerSecond } = data;
          const phonemeIndex = Math.floor(currentTime * phonemesPerSecond);
          const currentActiveViseme = phonemeIndex < processedPhonemes.length
            ? processedPhonemes[phonemeIndex].visemeData
            : {};
          self.postMessage({ type: 'ACTIVE_VISEME', data: currentActiveViseme });
          break;

        case 'BATCH_MORPH_CALCULATION':
          const { morphTargetDictionary, activeViseme, currentInfluences, deltaTime, fadeSpeed } = data;
          const updates = [];

          // Reset all morph targets first
          for (let i = 0; i < currentInfluences.length; i++) {
            const current = currentInfluences[i];
            if (Math.abs(current) > 0.01) {
              const newValue = current * (1 - deltaTime * fadeSpeed);
              updates.push({ index: i, value: newValue });
            }
          }

          // Apply active viseme morph targets
          if (activeViseme && Object.keys(activeViseme).length > 0) {
            Object.entries(activeViseme).forEach(([morphName, targetValue]) => {
              const morphIndex = morphTargetDictionary[morphName];
              if (morphIndex !== undefined) {
                const current = currentInfluences[morphIndex];
                const target = targetValue;

                if (Math.abs(current - target) > 0.01) {
                  const newValue = current + (target - current) * deltaTime * fadeSpeed;
                  // Update or add to updates array
                  const existingUpdate = updates.find(u => u.index === morphIndex);
                  if (existingUpdate) {
                    existingUpdate.value = newValue;
                  } else {
                    updates.push({ index: morphIndex, value: newValue });
                  }
                }
              }
            });
          }

          self.postMessage({ type: 'MORPH_UPDATES', data: updates });
          break;
      }
    };
  `;

  const blob = new Blob([workerCode], { type: 'application/javascript' });
  return new Worker(URL.createObjectURL(blob));
};

const visemeMap = {
  AA: { Jaw_Open: 0.5, Mouth_Close: -0.4 },
  AE: { Jaw_Open: 0.4, Mouth_Stretch_L: 0.3, Mouth_Stretch_R: 0.3 },
  AH: { Jaw_Open: 0.35, Mouth_Close: -0.25 },
  AO: { Jaw_Open: 0.4, Mouth_Pucker_Up_L: 0.3, Mouth_Pucker_Up_R: 0.3 },
  B: { Mouth_Close: 0.5, Mouth_Press_L: 0.4, Mouth_Press_R: 0.4 },
  P: { Mouth_Close: 0.5, Mouth_Press_L: 0.4, Mouth_Press_R: 0.4 },
  M: { Mouth_Close: 0.5, Mouth_Press_L: 0.4, Mouth_Press_R: 0.4 },
  CH: { Jaw_Open: 0.3, Mouth_Press_L: 0.3, Mouth_Press_R: 0.3 },
  JH: { Jaw_Open: 0.3, Mouth_Press_L: 0.3, Mouth_Press_R: 0.3 },
  SH: { Mouth_Shrug_Upper: 0.3, Mouth_Close: 0.4 },
  D: { Jaw_Open: 0.3, Tongue_Tip_Up: 0.3 },
  T: { Jaw_Open: 0.3, Tongue_Tip_Up: 0.3 },
  EH: { Mouth_Wide: 0.4, Mouth_Stretch_L: 0.3, Mouth_Stretch_R: 0.3 },
  ER: { Jaw_Open: 0.3, V_Tongue_Raise: 0.3, Mouth_Close: -0.3 },
  EY: { Mouth_Stretch_L: 0.3, Mouth_Stretch_R: 0.3 },
  F: { V_Dental_Lip: 0.5, Mouth_Close: -0.3 },
  V: { V_Dental_Lip: 0.5, Mouth_Close: -0.3 },
  G: { Jaw_Open: 0.3, Tongue_Backward: 0.3 },
  K: { Jaw_Open: 0.3, Tongue_Backward: 0.3 },
  NG: { Jaw_Open: 0.3, Tongue_Backward: 0.3 },
  HH: { Mouth_Close: -0.25 },
  IY: { Mouth_Smile_L: 0.4, Mouth_Smile_R: 0.4, Mouth_Wide: 0.4 },
  L: { Tongue_Tip_Up: 0.5 },
  N: { Mouth_Close: -0.3, Tongue_Tip_Up: 0.4 },
  OW: { Mouth_Pucker_Up_L: 0.4, Mouth_Pucker_Up_R: 0.4 },
  OY: { Mouth_Pucker_Up_L: 0.4, Mouth_Stretch_R: 0.3 },
  R: { Tongue_Curl_U: 0.5, Mouth_Close: -0.3 },
  S: { V_Tight: 0.5, Mouth_Shrug_Upper: 0.3 },
  Z: { V_Tight: 0.5, Mouth_Shrug_Upper: 0.3 },
  TH: { V_Tongue_Out: 0.5 },
  DH: { V_Tongue_Out: 0.5 },
  UW: { Mouth_Pucker_Up_L: 0.5, Mouth_Pucker_Up_R: 0.5 },
  W: { Mouth_Pucker_Up_L: 0.4, Mouth_Pucker_Up_R: 0.4 },
  Y: { Mouth_Smile_L: 0.3, Mouth_Smile_R: 0.3 },
  ',': { Mouth_Close: 0.3 },
  '.': { Mouth_Close: 0.3 },
  '-': { Mouth_Close: 0.3 },
  ' ': {}
};

// Expression map for facial expressions
const expressionMap = {
  neutral: {},

  smile: {
    Mouth_Smile_L: 0.3,
    Mouth_Smile_R: 0.3,
    Cheek_Raise_L: 0.2,
    Cheek_Raise_R: 0.2,
    Eye_Blink_L: 0.05,
    Eye_Blink_R: 0.05
  },

  sad: {
    Mouth_Frown_L: 0.3,
    Mouth_Frown_R: 0.3,
    Cheek_Suck_L: 0.2,
    Cheek_Suck_R: 0.2,
    Brow_Raise_Inner_L: 0.15,
    Brow_Raise_Inner_R: 0.15
  },

  angry: {
    Brow_Compress_L: 0.3,
    Brow_Compress_R: 0.3,
    Brow_Drop_L: 0.2,
    Brow_Drop_R: 0.2,
    Eye_Squint_L: 0.2,
    Eye_Squint_R: 0.2,
    Mouth_Tighten_L: 0.25,
    Mouth_Tighten_R: 0.25
  },

  surprised: {
    Brow_Raise_Outer_L: 0.25,
    Brow_Raise_Outer_R: 0.25,
    Eye_Wide_L: 0.25,
    Eye_Wide_R: 0.25,
    Jaw_Open: 0.2,
    Mouth_Open: 0.2
  },

  blink: {
    Eye_Blink_L: 1.0,
    Eye_Blink_R: 1.0
  },

  confused: {
    Brow_Raise_Inner_L: 0.2,
    Brow_Drop_R: 0.2,
    Mouth_Shrug_Lower: 0.15
  },

  excited: {
    Mouth_Smile_L: 0.4,
    Mouth_Smile_R: 0.4,
    Cheek_Raise_L: 0.3,
    Cheek_Raise_R: 0.3,
    Eye_Wide_L: 0.15,
    Eye_Wide_R: 0.15
  },

  disgust: {
    Nose_Sneer_L: 0.2,
    Nose_Sneer_R: 0.2,
    Mouth_Press_L: 0.2,
    Mouth_Press_R: 0.2
  },

  thinking: {
    Brow_Raise_Inner_L: 0.1,
    Brow_Raise_Inner_R: 0.1,
    Mouth_Tighten_L: 0.15,
    Mouth_Tighten_R: 0.15
  },

  laugh: {
    Mouth_Smile_L: 0.5,
    Mouth_Smile_R: 0.5,
    Cheek_Raise_L: 0.3,
    Cheek_Raise_R: 0.3,
    Jaw_Open: 0.3
  },

  fear: {
    Eye_Wide_L: 0.4,
    Eye_Wide_R: 0.4,
    Brow_Raise_Outer_L: 0.3,
    Brow_Raise_Outer_R: 0.3,
    Mouth_Open: 0.3,
    Jaw_Open: 0.2
  },

  bored: {
    Brow_Drop_L: 0.1,
    Brow_Drop_R: 0.1,
    Eye_Blink_L: 0.2,
    Eye_Blink_R: 0.2,
    Mouth_Shrug_Lower: 0.15
  },

  focus: {
    Brow_Compress_L: 0.2,
    Brow_Compress_R: 0.2,
    Eye_Squint_L: 0.2,
    Eye_Squint_R: 0.2
  },

  wink_left: {
    Eye_Blink_L: 1.0,
    Eye_Blink_R: 0.0
  },

  wink_right: {
    Eye_Blink_L: 0.0,
    Eye_Blink_R: 1.0
  }
};

export function Avatar({
  phonemes,
  audioBlob,
  isAudioPlaying,
  animation = 'Idle',
  currentExpression
}) {
  const group = useRef();

  // Browser-specific optimizations
  const browserInfo = useMemo(() => getBrowserInfo(), []);
  const [performanceMode, setPerformanceMode] = useState(
    browserInfo.isProblematicBrowser ? 'low' : 'medium'
  );

  // Web Worker for heavy computations
  const worker = useRef(null);
  const [workerReady, setWorkerReady] = useState(false);
  const [activeViseme, setActiveViseme] = useState(null);

  // Progressive loading state
  const [modelLoadingStage, setModelLoadingStage] = useState('skeleton');
  const [loadedMeshes, setLoadedMeshes] = useState(new Set());

  // Memory cleanup intervals
  const memoryCleanupInterval = useRef(null);

  // Memory cleanup intervals for Chrome/Edge
  useEffect(() => {
    if (browserInfo.isProblematicBrowser) {
      memoryCleanupInterval.current = setInterval(() => {
        // Force garbage collection hints for problematic browsers
        if (window.gc) {
          window.gc();
        }
        // Clear unused textures and geometries
        THREE.Cache.clear();
      }, 30000); // Every 30 seconds for Chrome/Edge

      console.log(`Initialized memory cleanup for ${browserInfo.browserName}`);
    }

    return () => {
      if (memoryCleanupInterval.current) {
        clearInterval(memoryCleanupInterval.current);
      }
    };
  }, [browserInfo]);

  // Initialize Web Worker with browser-specific settings
  useEffect(() => {
    worker.current = createModelWorker();

    worker.current.onmessage = (e) => {
      const { type, data } = e.data;

      switch (type) {
        case 'PHONEMES_PROCESSED':
          console.log('Phonemes processed in worker');
          break;
        case 'ACTIVE_VISEME':
          setActiveViseme(data);
          break;
        case 'MORPH_UPDATES':
          // Apply morph updates from worker
          if (facialMesh?.morphTargetInfluences) {
            data.forEach(({ index, value }) => {
              facialMesh.morphTargetInfluences[index] = value;
            });
          }
          break;
      }
    };

    // Initialize viseme map in worker
    worker.current.postMessage({
      type: 'INIT_VISEME_MAP',
      data: visemeMap
    });

    setWorkerReady(true);

    return () => {
      if (worker.current) {
        worker.current.terminate();
      }
    };
  }, []);

  // Progressive model loading with browser-specific optimizations
  const { scene } = useGLTF('/avatar_defined_keys.glb');
  const fbx = useLoader(FBXLoader, '/idle_sasthra_female.fbx');

  // Memoize expensive operations with browser-specific settings
  const clone = useMemo(() => {
    console.log(`Cloning scene for ${browserInfo.browserName}...`);
    const clonedScene = SkeletonUtils.clone(scene);

    // Progressive loading: Start with skeleton only for problematic browsers
    if (browserInfo.isProblematicBrowser) {
      setModelLoadingStage('skeleton');
      setTimeout(() => setModelLoadingStage('essential'), 1000);
      setTimeout(() => setModelLoadingStage('complete'), 2000);
    } else {
      setModelLoadingStage('complete');
    }

    return clonedScene;
  }, [scene, browserInfo]);

  const { nodes, materials } = useGraph(clone);

  // Memoize facial mesh reference
  const facialMesh = useMemo(() => {
    return nodes?.outfit_outfit_0045_8; // Std_Skin_Head mesh
  }, [nodes]);

  // Function to apply facial expressions
  const applyExpression = useCallback(
    (expression, intensity = 1.0) => {
      if (!facialMesh?.morphTargetInfluences || !facialMesh?.morphTargetDictionary) return;

      const expressionData = expressionMap[expression];
      if (!expressionData) return;

      // Apply expression morph targets
      Object.entries(expressionData).forEach(([morphName, value]) => {
        const morphIndex = facialMesh.morphTargetDictionary[morphName];
        if (morphIndex !== undefined) {
          facialMesh.morphTargetInfluences[morphIndex] = value * intensity;
        }
      });
    },
    [facialMesh]
  );

  // Reset all expression morph targets to neutral
  const resetExpressions = useCallback(() => {
    if (!facialMesh?.morphTargetInfluences || !facialMesh?.morphTargetDictionary) return;

    // Get all expression-related morph targets
    const expressionMorphs = new Set();
    Object.values(expressionMap).forEach((expression) => {
      Object.keys(expression).forEach((morphName) => {
        expressionMorphs.add(morphName);
      });
    });

    // Reset expression morphs to 0
    expressionMorphs.forEach((morphName) => {
      const morphIndex = facialMesh.morphTargetDictionary[morphName];
      if (morphIndex !== undefined) {
        facialMesh.morphTargetInfluences[morphIndex] = 0;
      }
    });
  }, [facialMesh]);

  const [animationMixer, setAnimationMixer] = useState(null);
  const [animations, setAnimations] = useState({});
  const audio = useRef(new Audio());
  const currentPhonemeIndexRef = useRef(0);
  const [currentExpressionState, setCurrentExpressionState] = useState('neutral');

  useEffect(() => {
    console.log('Loaded nodes:', nodes);
    console.log('Loaded materials:', materials);
  }, [nodes, materials]);

  // Process phonemes in Web Worker when they change
  useEffect(() => {
    if (workerReady && worker.current && phonemes) {
      worker.current.postMessage({
        type: 'PROCESS_PHONEMES',
        data: phonemes
      });
    }
  }, [phonemes, workerReady]);

  // Handle expression changes
  useEffect(() => {
    if (currentExpression !== currentExpressionState) {
      setCurrentExpressionState(currentExpression);
      resetExpressions();
      if (currentExpression !== 'neutral') {
        applyExpression(currentExpression);
      }
    }
  }, [currentExpression, currentExpressionState, resetExpressions, applyExpression]);

  // Memory management - dispose resources on unmount
  useEffect(() => {
    return () => {
      // Dispose animation mixer
      if (animationMixer) {
        animationMixer.stopAllAction();
        animationMixer.uncacheRoot(animationMixer.getRoot());
      }

      // Dispose geometries and materials
      if (nodes) {
        Object.values(nodes).forEach((node) => {
          if (node.geometry) {
            node.geometry.dispose();
          }
          if (node.material) {
            if (Array.isArray(node.material)) {
              node.material.forEach((mat) => mat.dispose());
            } else {
              node.material.dispose();
            }
          }
        });
      }

      // Dispose materials
      if (materials) {
        Object.values(materials).forEach((material) => {
          material.dispose();
          if (material.map) material.map.dispose();
          if (material.normalMap) material.normalMap.dispose();
          if (material.roughnessMap) material.roughnessMap.dispose();
          if (material.metalnessMap) material.metalnessMap.dispose();
          if (material.emissiveMap) material.emissiveMap.dispose();
        });
      }

      // Dispose FBX resources
      if (fbx) {
        fbx.traverse((child) => {
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((mat) => mat.dispose());
            } else {
              child.material.dispose();
            }
          }
        });
      }
    };
  }, [animationMixer, nodes, materials, fbx]);

  // Browser-specific animation setup with reduced frame rates for Chrome/Edge
  useEffect(() => {
    if (!group.current || !fbx || !nodes.mixamorigHips) return;

    const mixer = new THREE.AnimationMixer(nodes.mixamorigHips);

    // Browser-specific animation optimizations
    if (browserInfo.isProblematicBrowser) {
      mixer.timeScale = 0.8; // Slower animation for Chrome/Edge
      console.log(`Reduced animation speed for ${browserInfo.browserName}`);
    } else {
      mixer.timeScale = 1.0;
    }
    mixer._accuIndex = 0; // Reset accumulator for better performance

    setAnimationMixer(mixer);

    // Use the FBX animation directly with browser-specific optimizations
    if (fbx.animations && fbx.animations.length > 0) {
      const clip = fbx.animations[0];
      clip.name = 'Idle';

      // Advanced animation clip optimizations
      clip.optimize();
      clip.resetDuration(); // Recalculate duration for optimization

      // More aggressive track reduction for problematic browsers
      const optimizedTracks = clip.tracks.filter((track) => {
        const trackName = track.name.toLowerCase();
        const isEssential =
          !trackName.includes('ik') &&
          !trackName.includes('pole') &&
          !trackName.includes('ctrl') &&
          !trackName.includes('helper');

        // For Chrome/Edge, remove even more tracks
        if (browserInfo.isProblematicBrowser) {
          return (
            isEssential &&
            !trackName.includes('finger') &&
            !trackName.includes('toe') &&
            !trackName.includes('twist')
          );
        }

        return isEssential;
      });

      if (optimizedTracks.length < clip.tracks.length) {
        clip.tracks = optimizedTracks;
        console.log(
          `Optimized animation for ${browserInfo.browserName}: reduced from ${clip.tracks.length + (clip.tracks.length - optimizedTracks.length)} to ${optimizedTracks.length} tracks`
        );
      }

      const action = mixer.clipAction(clip, nodes.mixamorigHips);

      // Browser-specific action configuration
      action.setLoop(THREE.LoopRepeat);
      action.clampWhenFinished = false;
      action.enabled = true;
      action.weight = 1.0;
      action.timeScale = browserInfo.isProblematicBrowser ? 0.9 : 1.0;

      // Enable interpolation optimization
      action.setEffectiveWeight(1.0);
      action.setEffectiveTimeScale(browserInfo.isProblematicBrowser ? 0.9 : 1.0);

      setAnimations({ Idle: action });
      action.play();
      console.log(
        `Successfully loaded and optimized idle animation for ${browserInfo.browserName}`
      );
    }

    return () => {
      mixer.stopAllAction();
      mixer.uncacheRoot(mixer.getRoot());
    };
  }, [fbx, nodes.mixamorigHips, browserInfo]);

  useEffect(() => {
    if (!animationMixer || !animations[animation]) return;
    const fadeTime = 1;
    Object.values(animations).forEach((action) => action.fadeOut(fadeTime));
    animations[animation].reset().fadeIn(fadeTime).play();
  }, [animation, animations, animationMixer]);

  useEffect(() => {
    if (audioBlob) {
      audio.current.src = audioBlob; // Use the blob URL directly
      if (isAudioPlaying) audio.current.play();
    }
  }, [audioBlob, isAudioPlaying]);

  // Browser-specific frame processing with reduced rates for Chrome/Edge
  const lastUpdateTime = useRef(0);
  const frameSkipCounter = useRef(0);
  const lastVisemeRequest = useRef(0);

  // Browser-specific performance thresholds
  const FRAME_SKIP_THRESHOLD = useRef(browserInfo.isProblematicBrowser ? 2 : 1);
  const VISEME_REQUEST_INTERVAL = useRef(browserInfo.isProblematicBrowser ? 50 : 33); // Slower for Chrome/Edge
  const MORPH_UPDATE_INTERVAL = useRef(browserInfo.isProblematicBrowser ? 33 : 16); // 30fps vs 60fps

  // Browser-optimized useFrame with reduced rates for Chrome/Edge
  useFrame(
    useCallback(
      (_, delta) => {
        // Browser-specific delta capping
        const cappedDelta = browserInfo.isProblematicBrowser
          ? Math.min(delta, 1 / 15) // Cap at 15fps for Chrome/Edge
          : Math.min(delta, 1 / 20); // Cap at 20fps for others
        const now = performance.now();

        // Always update animation mixer with browser-specific delta
        if (animationMixer) {
          const animationSpeed = browserInfo.isProblematicBrowser ? 0.85 : 0.95;
          animationMixer.update(cappedDelta * animationSpeed);
        }

        // Browser-specific performance monitoring
        const frameTime = now - lastUpdateTime.current;
        const targetFrameTime = browserInfo.isProblematicBrowser ? 50 : 33; // 20fps vs 30fps targets

        if (frameTime > targetFrameTime) {
          FRAME_SKIP_THRESHOLD.current = Math.min(
            browserInfo.isProblematicBrowser ? 4 : 3,
            FRAME_SKIP_THRESHOLD.current + 1
          );
          VISEME_REQUEST_INTERVAL.current = Math.min(
            browserInfo.isProblematicBrowser ? 80 : 50,
            VISEME_REQUEST_INTERVAL.current + 5
          );
        } else if (frameTime < targetFrameTime / 2) {
          FRAME_SKIP_THRESHOLD.current = Math.max(
            browserInfo.isProblematicBrowser ? 2 : 1,
            FRAME_SKIP_THRESHOLD.current - 1
          );
          VISEME_REQUEST_INTERVAL.current = Math.max(
            browserInfo.isProblematicBrowser ? 50 : 16,
            VISEME_REQUEST_INTERVAL.current - 2
          );
        }

        // Throttle overall updates based on performance
        if (now - lastUpdateTime.current < MORPH_UPDATE_INTERVAL.current) return;
        lastUpdateTime.current = now;

        // Only process lip sync when conditions are met
        if (!phonemes || !isAudioPlaying || !audio.current || !workerReady || !worker.current)
          return;
        if (!facialMesh?.morphTargetInfluences || !facialMesh?.morphTargetDictionary) return;

        // Adaptive frame skipping for lip sync
        frameSkipCounter.current++;
        if (frameSkipCounter.current < FRAME_SKIP_THRESHOLD.current) return;
        frameSkipCounter.current = 0;

        // Request active viseme from Web Worker (throttled)
        if (now - lastVisemeRequest.current >= VISEME_REQUEST_INTERVAL.current) {
          lastVisemeRequest.current = now;
          worker.current.postMessage({
            type: 'GET_ACTIVE_VISEME',
            data: {
              currentTime: audio.current.currentTime,
              phonemesPerSecond: browserInfo.isProblematicBrowser ? 12 : 16 // Reduced for Chrome/Edge
            }
          });
        }

        // Process morph updates from Web Worker if available
        if (activeViseme && Object.keys(activeViseme).length > 0) {
          const morphTargetDictionary = facialMesh.morphTargetDictionary;
          const currentInfluences = Array.from(facialMesh.morphTargetInfluences);

          // Send batch calculation to Web Worker
          worker.current.postMessage({
            type: 'BATCH_MORPH_CALCULATION',
            data: {
              morphTargetDictionary,
              activeViseme,
              currentInfluences,
              deltaTime: cappedDelta,
              fadeSpeed: browserInfo.isProblematicBrowser ? 10 : 15 // Slower fade for Chrome/Edge
            }
          });
        }
      },
      [animationMixer, phonemes, isAudioPlaying, workerReady, facialMesh, activeViseme, browserInfo]
    )
  );

  // Process phonemes in Web Worker when they change
  useEffect(() => {
    if (workerReady && worker.current && phonemes) {
      worker.current.postMessage({
        type: 'PROCESS_PHONEMES',
        data: phonemes
      });
    }
  }, [phonemes, workerReady]);

  // Handle expression changes
  useEffect(() => {
    if (currentExpression !== currentExpressionState) {
      setCurrentExpressionState(currentExpression);
      resetExpressions();
      if (currentExpression !== 'neutral') {
        applyExpression(currentExpression);
      }
    }
  }, [currentExpression, currentExpressionState, resetExpressions, applyExpression]);

  // Memory management - dispose resources on unmount
  useEffect(() => {
    return () => {
      // Dispose animation mixer
      if (animationMixer) {
        animationMixer.stopAllAction();
        animationMixer.uncacheRoot(animationMixer.getRoot());
        // Note: AnimationMixer doesn't have dispose method in Three.js
      }

      // Dispose geometries and materials
      if (nodes) {
        Object.values(nodes).forEach((node) => {
          if (node.geometry) {
            node.geometry.dispose();
          }
          if (node.material) {
            if (Array.isArray(node.material)) {
              node.material.forEach((mat) => mat.dispose());
            } else {
              node.material.dispose();
            }
          }
        });
      }

      // Dispose materials
      if (materials) {
        Object.values(materials).forEach((material) => {
          material.dispose();
          // Dispose textures
          if (material.map) material.map.dispose();
          if (material.normalMap) material.normalMap.dispose();
          if (material.roughnessMap) material.roughnessMap.dispose();
          if (material.metalnessMap) material.metalnessMap.dispose();
          if (material.emissiveMap) material.emissiveMap.dispose();
        });
      }

      // Dispose FBX resources
      if (fbx) {
        fbx.traverse((child) => {
          if (child.geometry) child.geometry.dispose();
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((mat) => mat.dispose());
            } else {
              child.material.dispose();
            }
          }
        });
      }
    };
  }, [animationMixer, nodes, materials, fbx]);

  // Browser-specific animation setup with reduced frame rates for Chrome/Edge
  useEffect(() => {
    if (!group.current || !fbx || !nodes.mixamorigHips) return;

    const mixer = new THREE.AnimationMixer(nodes.mixamorigHips);

    // Browser-specific animation optimizations
    if (browserInfo.isProblematicBrowser) {
      mixer.timeScale = 0.8; // Slower animation for Chrome/Edge
      console.log(`Reduced animation speed for ${browserInfo.browserName}`);
    } else {
      mixer.timeScale = 1.0;
    }
    mixer._accuIndex = 0; // Reset accumulator for better performance

    setAnimationMixer(mixer);

    // Use the FBX animation directly with browser-specific optimizations
    if (fbx.animations && fbx.animations.length > 0) {
      const clip = fbx.animations[0];
      clip.name = 'Idle';

      // Advanced animation clip optimizations
      clip.optimize();
      clip.resetDuration(); // Recalculate duration for optimization

      // More aggressive track reduction for problematic browsers
      const optimizedTracks = clip.tracks.filter((track) => {
        const trackName = track.name.toLowerCase();
        const isEssential =
          !trackName.includes('ik') &&
          !trackName.includes('pole') &&
          !trackName.includes('ctrl') &&
          !trackName.includes('helper');

        // For Chrome/Edge, remove even more tracks
        if (browserInfo.isProblematicBrowser) {
          return (
            isEssential &&
            !trackName.includes('finger') &&
            !trackName.includes('toe') &&
            !trackName.includes('twist')
          );
        }

        return isEssential;
      });

      if (optimizedTracks.length < clip.tracks.length) {
        clip.tracks = optimizedTracks;
        console.log(
          `Optimized animation for ${browserInfo.browserName}: reduced from ${clip.tracks.length + (clip.tracks.length - optimizedTracks.length)} to ${optimizedTracks.length} tracks`
        );
      }

      const action = mixer.clipAction(clip, nodes.mixamorigHips);

      // Browser-specific action configuration
      action.setLoop(THREE.LoopRepeat);
      action.clampWhenFinished = false;
      action.enabled = true;
      action.weight = 1.0;
      action.timeScale = browserInfo.isProblematicBrowser ? 0.9 : 1.0;

      // Enable interpolation optimization
      action.setEffectiveWeight(1.0);
      action.setEffectiveTimeScale(browserInfo.isProblematicBrowser ? 0.9 : 1.0);

      setAnimations({ Idle: action });
      action.play();
      console.log(
        `Successfully loaded and optimized idle animation for ${browserInfo.browserName}`
      );
    }

    return () => {
      mixer.stopAllAction();
      mixer.uncacheRoot(mixer.getRoot());
    };
  }, [fbx, nodes.mixamorigHips, browserInfo]);

  useEffect(() => {
    if (!animationMixer || !animations[animation]) return;
    const fadeTime = 1;
    Object.values(animations).forEach((action) => action.fadeOut(fadeTime));
    animations[animation].reset().fadeIn(fadeTime).play();
  }, [animation, animations, animationMixer]);

  useEffect(() => {
    if (audioBlob) {
      audio.current.src = audioBlob; // Use the blob URL directly
      currentPhonemeIndexRef.current = 0;
      if (isAudioPlaying) audio.current.play();
    }
  }, [audioBlob, isAudioPlaying]);

  // Progressive mesh rendering based on loading stage and browser
  const shouldRenderMesh = useCallback(
    (category = 'essential') => {
      if (modelLoadingStage === 'skeleton') return false;
      if (modelLoadingStage === 'essential') return category === 'essential';
      if (modelLoadingStage === 'complete') {
        // For problematic browsers, skip non-essential meshes
        if (browserInfo.isProblematicBrowser && category === 'optional') return false;
        return true;
      }
      return true;
    },
    [modelLoadingStage, browserInfo]
  );

  if (!nodes) return null;

  return (
    <group ref={group} dispose={null} position={[0, -6.5, 0.5]} scale={7.5}>
      <primitive object={nodes.mixamorigHips} />
      <primitive object={nodes.Ctrl_Master} />
      <primitive object={nodes.Ctrl_ArmPole_IK_Left} />
      <primitive object={nodes.Ctrl_Hand_IK_Left} />
      <primitive object={nodes.Ctrl_ArmPole_IK_Right} />
      <primitive object={nodes.Ctrl_Hand_IK_Right} />
      <primitive object={nodes.Ctrl_Foot_IK_Left} />
      <primitive object={nodes.Ctrl_LegPole_IK_Left} />
      <primitive object={nodes.Ctrl_Foot_IK_Right} />
      <primitive object={nodes.Ctrl_LegPole_IK_Right} />
      {nodes.outfit_outfit_0085 && nodes.outfit_outfit_0085.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085"
          geometry={nodes.outfit_outfit_0085.geometry}
          material={materials.Std_Tearline_R}
          skeleton={nodes.outfit_outfit_0085.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_1 && nodes.outfit_outfit_0085_1.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_1"
          geometry={nodes.outfit_outfit_0085_1.geometry}
          material={materials.Std_Tearline_L}
          skeleton={nodes.outfit_outfit_0085_1.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_1.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_1.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_2 && nodes.outfit_outfit_0085_2.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_2"
          geometry={nodes.outfit_outfit_0085_2.geometry}
          material={materials.Female_Brow_Transparency}
          skeleton={nodes.outfit_outfit_0085_2.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_2.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_2.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_3 && nodes.outfit_outfit_0085_3.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_3"
          geometry={nodes.outfit_outfit_0085_3.geometry}
          material={materials.Female_Brow_Base_Transparency}
          skeleton={nodes.outfit_outfit_0085_3.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_3.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_3.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_4 && nodes.outfit_outfit_0085_4.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_4"
          geometry={nodes.outfit_outfit_0085_4.geometry}
          material={materials.Silver}
          skeleton={nodes.outfit_outfit_0085_4.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_4.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_4.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_5 && nodes.outfit_outfit_0085_5.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_5"
          geometry={nodes.outfit_outfit_0085_5.geometry}
          material={materials.Std_Skin_Body}
          skeleton={nodes.outfit_outfit_0085_5.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_5.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_5.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_6 && nodes.outfit_outfit_0085_6.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_6"
          geometry={nodes.outfit_outfit_0085_6.geometry}
          material={materials.Std_Skin_Arm}
          skeleton={nodes.outfit_outfit_0085_6.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_6.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_6.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_7 && nodes.outfit_outfit_0085_7.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_7"
          geometry={nodes.outfit_outfit_0085_7.geometry}
          material={materials.Std_Tongue}
          skeleton={nodes.outfit_outfit_0085_7.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_7.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_7.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_8 && nodes.outfit_outfit_0085_8.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_8"
          geometry={nodes.outfit_outfit_0085_8.geometry}
          material={materials.AvatarBody}
          skeleton={nodes.outfit_outfit_0085_8.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_8.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_8.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_9 && nodes.outfit_outfit_0085_9.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_9"
          geometry={nodes.outfit_outfit_0085_9.geometry}
          material={materials['Hair.003']}
          skeleton={nodes.outfit_outfit_0085_9.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_9.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_9.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_10 && nodes.outfit_outfit_0085_10.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_10"
          geometry={nodes.outfit_outfit_0085_10.geometry}
          material={materials['Hair.002']}
          skeleton={nodes.outfit_outfit_0085_10.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_10.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_10.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_11 && nodes.outfit_outfit_0085_11.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_11"
          geometry={nodes.outfit_outfit_0085_11.geometry}
          material={materials.outfit}
          skeleton={nodes.outfit_outfit_0085_11.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_11.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_11.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_12 && nodes.outfit_outfit_0085_12.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_12"
          geometry={nodes.outfit_outfit_0085_12.geometry}
          material={materials.Std_Skin_Head}
          skeleton={nodes.outfit_outfit_0085_12.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_12.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_12.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_13 && nodes.outfit_outfit_0085_13.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_13"
          geometry={nodes.outfit_outfit_0085_13.geometry}
          material={materials.Std_Upper_Teeth}
          skeleton={nodes.outfit_outfit_0085_13.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_13.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_13.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_14 && nodes.outfit_outfit_0085_14.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_14"
          geometry={nodes.outfit_outfit_0085_14.geometry}
          material={materials.Std_Lower_Teeth}
          skeleton={nodes.outfit_outfit_0085_14.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_14.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_14.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_15 && nodes.outfit_outfit_0085_15.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_15"
          geometry={nodes.outfit_outfit_0085_15.geometry}
          material={materials.Std_Eyelash}
          skeleton={nodes.outfit_outfit_0085_15.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_15.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_15.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_17 && nodes.outfit_outfit_0085_17.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_17"
          geometry={nodes.outfit_outfit_0085_17.geometry}
          material={materials.Std_Eye_Occlusion_R}
          skeleton={nodes.outfit_outfit_0085_17.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_17.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_17.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_18 && nodes.outfit_outfit_0085_18.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_18"
          geometry={nodes.outfit_outfit_0085_18.geometry}
          material={materials.Std_Eye_Occlusion_L}
          skeleton={nodes.outfit_outfit_0085_18.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_18.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_18.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_19 && nodes.outfit_outfit_0085_19.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_19"
          geometry={nodes.outfit_outfit_0085_19.geometry}
          material={materials.Std_Eye_R}
          skeleton={nodes.outfit_outfit_0085_19.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_19.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_19.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_20 && nodes.outfit_outfit_0085_20.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_20"
          geometry={nodes.outfit_outfit_0085_20.geometry}
          material={materials.Std_Cornea_R}
          skeleton={nodes.outfit_outfit_0085_20.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_20.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_20.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_21 && nodes.outfit_outfit_0085_21.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_21"
          geometry={nodes.outfit_outfit_0085_21.geometry}
          material={materials.Std_Eye_L}
          skeleton={nodes.outfit_outfit_0085_21.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_21.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_21.morphTargetInfluences}
        />
      )}
      {nodes.outfit_outfit_0085_22 && nodes.outfit_outfit_0085_22.skeleton && (
        <skinnedMesh
          name="outfit_outfit_0085_22"
          geometry={nodes.outfit_outfit_0085_22.geometry}
          material={materials.Std_Cornea_L}
          skeleton={nodes.outfit_outfit_0085_22.skeleton}
          morphTargetDictionary={nodes.outfit_outfit_0085_22.morphTargetDictionary}
          morphTargetInfluences={nodes.outfit_outfit_0085_22.morphTargetInfluences}
        />
      )}
    </group>
  );
}

// Preload models for better performance
useGLTF.preload('/avatar_defined_keys.glb');
