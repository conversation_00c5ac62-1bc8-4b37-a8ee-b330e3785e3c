import { PaperBasedBioTestApi } from '../../../../redux/api/api';

export const PaperBasedBioTestApiSlice = PaperBasedBioTestApi.injectEndpoints({
  endpoints: (builder) => ({
    paperBasedBioTestStartTest: builder.mutation({
      query: (body) => ({
        url: '/generate_biology_paper',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Paper Based Biology Test Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['PaperBasedBioTest']
    })
  })
});

export const { usePaperBasedBioTestStartTestMutation } = PaperBasedBioTestApiSlice;
