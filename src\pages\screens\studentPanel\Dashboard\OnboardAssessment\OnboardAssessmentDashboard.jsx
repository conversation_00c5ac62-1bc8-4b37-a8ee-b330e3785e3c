import React, { useEffect } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { setOnboardAssessment, useLazyGetOnboardAssessmentServiceQuery } from '../dashboard.slice';
import { useDispatch, useSelector } from 'react-redux';

const OnboardAssessmentDashboard = () => {
  const [OnboardAssessment] = useLazyGetOnboardAssessmentServiceQuery();

  const dispatch = useDispatch();

  const onboardAssessmentData = useSelector((state) => state.studentDashboard.onboardAssessment);

  useEffect(() => {
    handleGetOnboardAssessment();
  }, []);

  const handleGetOnboardAssessment = async () => {
    try {
      const res = await OnboardAssessment({ userId: sessionStorage.userId }).unwrap();
      dispatch(setOnboardAssessment(res));
    } catch (error) {
      console.error('Error fetching onboarding assessment:', error);
    }
  };

  const chartOptions = {
    chart: {
      type: 'column'
    },
    title: {
      text: 'Onboarding Assessment Overview'
    },
    xAxis: {
      categories: ['Quiz'],
      title: {
        text: null
      }
    },
    yAxis: {
      min: 0,
      title: {
        text: 'Number of Questions'
      }
    },
    tooltip: {
      valueSuffix: ' questions'
    },
    plotOptions: {
      column: {
        grouping: true,
        shadow: false,
        borderWidth: 0
      }
    },
    credits: {
      enabled: false
    },
    series: [
      {
        name: 'Total Questions',
        data: [onboardAssessmentData?.total_questions || 0],
        color: '#007bff'
      },
      {
        name: 'Correct Answers',
        data: [onboardAssessmentData?.correct_answers || 0],
        color: '#28a745'
      },
      {
        name: 'Incorrect Answers',
        data: [onboardAssessmentData?.incorrect_answers || 0],
        color: '#dc3545'
      }
    ]
  };

  const options = {
    chart: {
      type: 'bar',
      animation: true
    },
    title: {
      text: 'Topic-wise Assessment Performance'
    },
    xAxis: {
      categories: ['Mental Ability', 'Logical Reasoning', 'Math Reasoning'],
      title: {
        text: null
      }
    },
    yAxis: {
      min: 0,
      title: {
        text: 'Number of Questions'
      }
    },
    plotOptions: {
      bar: {
        grouping: true,
        shadow: false,
        borderWidth: 0
      }
    },
    tooltip: {
      valueSuffix: ' questions'
    },
    lang: {
      noData: 'No data found'
    },
    noData: {
      style: {
        fontWeight: 'bold',
        fontSize: '16px',
        color: '#666'
      }
    },
    series: [
      {
        name: 'Total Questions',
        data: [
          onboardAssessmentData?.topic_breakdown?.mental_ability?.total_questions || 0,
          onboardAssessmentData?.topic_breakdown?.logical_reasoning?.total_questions || 0,
          onboardAssessmentData?.topic_breakdown?.math_reasoning?.total_questions || 0
        ],
        color: '#00aaff'
      },
      {
        name: 'Correct Answers',
        data: [
          onboardAssessmentData?.topic_breakdown?.mental_ability?.total_correct || 0,
          onboardAssessmentData?.topic_breakdown?.logical_reasoning?.total_correct || 0,
          onboardAssessmentData?.topic_breakdown?.math_reasoning?.total_correct || 0
        ],
        color: '#00ff66'
      },
      {
        name: 'Incorrect Answers',
        data: [
          (onboardAssessmentData?.topic_breakdown?.mental_ability?.total_questions || 0) -
            (onboardAssessmentData?.topic_breakdown?.mental_ability?.total_correct || 0),
          (onboardAssessmentData?.topic_breakdown?.logical_reasoning?.total_questions || 0) -
            (onboardAssessmentData?.topic_breakdown?.logical_reasoning?.total_correct || 0),
          (onboardAssessmentData?.topic_breakdown?.math_reasoning?.total_questions || 0) -
            (onboardAssessmentData?.topic_breakdown?.math_reasoning?.total_correct || 0)
        ],
        color: '#ff4d4d'
      }
    ],
    credits: {
      enabled: false
    }
  };

  const isEmpty =
    !onboardAssessmentData?.total_questions &&
    !onboardAssessmentData?.correct_answers &&
    !onboardAssessmentData?.incorrect_answers;

  return (
    <div className="grid grid-cols-2 gap-4">
      {isEmpty ? (
        <div className="col-span-2 text-center text-gray-500 font-semibold">
          No assessment data found - Login again to get Onboard assessment
        </div>
      ) : (
        <>
          <HighchartsReact highcharts={Highcharts} options={chartOptions} />
          <HighchartsReact highcharts={Highcharts} options={options} />
        </>
      )}
    </div>
  );
};

export default OnboardAssessmentDashboard;
