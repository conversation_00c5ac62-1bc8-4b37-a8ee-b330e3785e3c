import { Tabs } from '../UI/Tabs';

export function TabsDemo() {
  const tabContents = [
    {
      title: 'Physics',
      value: 'physics',
      heading: 'Master Physics Concepts with Precision',
      description:
        'Video lessons, solved examples, conceptual quizzes, problem-solving marathons, and real-time doubt sessions.'
    },
    {
      title: 'Chemistry',
      value: 'chemistry',
      heading: 'Crack Chemistry with Simplified Techniques',
      description:
        'Organic, Inorganic & Physical Chemistry modules, smart memorization tools, and conceptual problem banks.'
    },
    {
      title: 'Biology',
      value: 'biology',
      heading: 'Biology Simplified & Systematized',
      description:
        'High-yield NCERT-based notes, diagram-based learning, flashcards, and exam-centric practice tests.'
    }
  ];

  const tabs = tabContents.map(({ title, value, heading, description }) => ({
    title,
    value,
    content: (
      <div className="w-full h-screen flex flex-col justify-center items-center bg-amber-300 text-white p-4">
        <h2 className="text-4xl font-bold mb-4">{heading}</h2>
        <p className="text-xl text-center max-w-3xl">{description}</p>
      </div>
    )
  }));

  return (
    <div className="relative flex flex-col bg-amber-300">
      <Tabs tabs={tabs} />
    </div>
  );
}
