import { createSlice } from '@reduxjs/toolkit';
import { parentStudentAttendanceApi } from '../../../../redux/api/api';

const initialState = {
  parentStudentAttendance: null
};

export const parentAttendanceApiSlice = parentStudentAttendanceApi.injectEndpoints({
  endpoints: (builder) => ({
    getParentStudentAttendance: builder.query({
      query: (studentId) => ({
        url: `/student-attendance-parentpanel?studentId=${studentId}`,
        method: 'GET'
      }),
      transformResponse: (response) => {
        console.log('Parent Student Attendance Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ParentStudentAttendance']
    })
  })
});

const attendanceSlice = createSlice({
  name: 'attendance',
  initialState,
  reducers: {
    setParentStudentAttendance: (state, action) => {
      state.parentStudentAttendance = action.payload;
    }
  }
});

export default attendanceSlice.reducer;
export const { setParentStudentAttendance } = attendanceSlice.actions;
export const { useGetParentStudentAttendanceQuery } = parentAttendanceApiSlice;
