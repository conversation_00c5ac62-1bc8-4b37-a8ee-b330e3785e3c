import { useState } from 'react';
// eslint-disable-next-line no-unused-vars
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircle2,
  Atom,
  FlaskConical,
  Calculator,
  TrendingUp,
  Award,
  Target,
  BarChart3,
  Info,
  ChevronRight,
  Star
} from 'lucide-react';

const TestPerformanceDashboard = () => {
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [hoveredCard, setHoveredCard] = useState(null);
  const [showTooltip, setShowTooltip] = useState(null);

  // Animation variants
  const containerVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.15,
        delayChildren: 0.3
      }
    }
  };

  const cardVariants = {
    initial: { opacity: 0, y: 40, scale: 0.9 },
    animate: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    hover: {
      y: -12,
      scale: 1.05,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    },
    tap: { scale: 0.95 }
  };

  const progressVariants = {
    initial: { width: 0, opacity: 0 },
    animate: {
      width: '100%',
      opacity: 1,
      transition: {
        duration: 2,
        delay: 1,
        ease: 'easeInOut'
      }
    }
  };

  const scoreVariants = {
    initial: { scale: 0, opacity: 0 },
    animate: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.8,
        delay: 0.5,
        ease: 'easeOut'
      }
    }
  };

  // Subject data with enhanced information
  const subjectData = [
    {
      id: 1,
      name: 'OVERALL',
      score: 226,
      maxScore: 300,
      percentage: 75.3,
      icon: CheckCircle2,
      color: 'blue',
      bgGradient: 'from-blue-500 to-indigo-600',
      bgLight: 'from-blue-50 to-indigo-50',
      textColor: 'text-blue-600',
      borderColor: 'border-blue-200',
      progressColor: 'bg-blue-500',
      shadowColor: 'shadow-blue-200',
      description: 'Your overall performance across all subjects',
      grade: 'A',
      rank: '12th',
      improvement: '+8%'
    },
    {
      id: 2,
      name: 'PHYSICS',
      score: 91,
      maxScore: 100,
      percentage: 91,
      icon: Atom,
      color: 'green',
      bgGradient: 'from-green-500 to-emerald-600',
      bgLight: 'from-green-50 to-emerald-50',
      textColor: 'text-green-600',
      borderColor: 'border-green-200',
      progressColor: 'bg-green-500',
      shadowColor: 'shadow-green-200',
      description: 'Excellent performance in Physics concepts',
      grade: 'A+',
      rank: '5th',
      improvement: '+12%'
    },
    {
      id: 3,
      name: 'CHEMISTRY',
      score: 80,
      maxScore: 100,
      percentage: 80,
      icon: FlaskConical,
      color: 'orange',
      bgGradient: 'from-orange-500 to-red-500',
      bgLight: 'from-orange-50 to-red-50',
      textColor: 'text-orange-600',
      borderColor: 'border-orange-200',
      progressColor: 'bg-orange-500',
      shadowColor: 'shadow-orange-200',
      description: 'Good grasp of Chemistry fundamentals',
      grade: 'B+',
      rank: '18th',
      improvement: '+5%'
    },
    {
      id: 4,
      name: 'MATHEMATICS',
      score: 55,
      maxScore: 100,
      percentage: 55,
      icon: Calculator,
      color: 'blue',
      bgGradient: 'from-blue-500 to-cyan-500',
      bgLight: 'from-blue-50 to-cyan-50',
      textColor: 'text-blue-600',
      borderColor: 'border-blue-200',
      progressColor: 'bg-blue-500',
      shadowColor: 'shadow-blue-200',
      description: 'Room for improvement in Mathematical concepts',
      grade: 'C+',
      rank: '45th',
      improvement: '-3%'
    }
  ];

  const getPerformanceLevel = (percentage) => {
    if (percentage >= 90)
      return { level: 'Excellent', color: 'text-green-600', bgColor: 'bg-green-100' };
    if (percentage >= 80)
      return { level: 'Very Good', color: 'text-blue-600', bgColor: 'bg-blue-100' };
    if (percentage >= 70)
      return { level: 'Good', color: 'text-orange-600', bgColor: 'bg-orange-100' };
    if (percentage >= 60)
      return { level: 'Average', color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
    return { level: 'Needs Improvement', color: 'text-red-600', bgColor: 'bg-red-100' };
  };

  const handleCardClick = (subjectId) => {
    setSelectedSubject(selectedSubject === subjectId ? null : subjectId);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-4 md:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Enhanced Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <div className="flex items-center justify-center gap-4 mb-6">
            <div className="p-4 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl shadow-lg">
              <BarChart3 className="w-10 h-10 text-white" />
            </div>
            <div className="text-left">
              <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-800 via-gray-700 to-gray-600 bg-clip-text text-transparent">
                Test Performance
              </h1>
              <p className="text-gray-600 text-lg mt-2 max-w-2xl">
                This shows your overall as well as subject-wise scores.
              </p>
            </div>
          </div>

          {/* Performance Summary */}
          <div className="flex flex-wrap justify-center gap-6 mt-8">
            <div className="flex items-center gap-2 bg-white/70 backdrop-blur-sm px-4 py-2 rounded-full shadow-md">
              <Award className="w-5 h-5 text-yellow-500" />
              <span className="text-sm font-medium text-gray-700">Overall Grade: A</span>
            </div>
            <div className="flex items-center gap-2 bg-white/70 backdrop-blur-sm px-4 py-2 rounded-full shadow-md">
              <Target className="w-5 h-5 text-green-500" />
              <span className="text-sm font-medium text-gray-700">Class Rank: 12th</span>
            </div>
            <div className="flex items-center gap-2 bg-white/70 backdrop-blur-sm px-4 py-2 rounded-full shadow-md">
              <TrendingUp className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium text-gray-700">Improvement: +8%</span>
            </div>
          </div>
        </motion.div>

        {/* Subject Cards Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          variants={containerVariants}
          initial="initial"
          animate="animate"
        >
          {subjectData.map((subject) => {
            const IconComponent = subject.icon;
            const isSelected = selectedSubject === subject.id;
            const performance = getPerformanceLevel(subject.percentage);

            return (
              <motion.div
                key={subject.id}
                variants={cardVariants}
                whileHover="hover"
                whileTap="tap"
                className="group cursor-pointer"
                onClick={() => handleCardClick(subject.id)}
                onMouseEnter={() => setHoveredCard(subject.id)}
                onMouseLeave={() => setHoveredCard(null)}
              >
                {/* Enhanced Card Design */}
                <div
                  className={`relative overflow-hidden bg-gradient-to-br ${subject.bgLight} border-2 ${subject.borderColor} rounded-2xl shadow-lg hover:shadow-2xl ${subject.shadowColor} transition-all duration-500 ${
                    isSelected ? 'ring-4 ring-indigo-300 ring-offset-2' : ''
                  } ${hoveredCard === subject.id ? 'shadow-2xl' : ''}`}
                >
                  {/* Animated Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white to-transparent transform rotate-45 translate-x-full group-hover:translate-x-[-100%] transition-transform duration-1000"></div>
                  </div>

                  <div className="relative p-6">
                    {/* Card Header */}
                    <div className="flex items-center justify-between mb-6">
                      <div
                        className={`p-4 bg-gradient-to-br ${subject.bgGradient} rounded-xl shadow-lg transform group-hover:scale-110 transition-transform duration-300`}
                      >
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>

                      {/* Info Button with Tooltip */}
                      <div
                        className="relative"
                        onMouseEnter={() => setShowTooltip(subject.id)}
                        onMouseLeave={() => setShowTooltip(null)}
                      >
                        <Info className="w-5 h-5 text-gray-400 hover:text-gray-600 transition-colors cursor-help" />
                        {showTooltip === subject.id && (
                          <div className="absolute right-0 top-8 z-50 w-64 p-3 bg-gray-900 text-white text-sm rounded-lg shadow-xl">
                            <p>{subject.description}</p>
                            <div className="absolute -top-2 right-3 w-4 h-4 bg-gray-900 rotate-45"></div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Subject Name */}
                    <h3 className="text-lg font-bold text-gray-800 mb-2 tracking-wide">
                      {subject.name}
                    </h3>

                    {/* Performance Badge */}
                    <div
                      className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${performance.bgColor} ${performance.color} mb-4`}
                    >
                      <Star className="w-3 h-3 mr-1" />
                      {performance.level}
                    </div>

                    {/* Score Display */}
                    <motion.div
                      className="mb-6"
                      variants={scoreVariants}
                      initial="initial"
                      animate="animate"
                    >
                      <div className="flex items-baseline gap-2 mb-2">
                        <span className={`text-4xl font-bold ${subject.textColor}`}>
                          {subject.score}
                        </span>
                        <span className="text-xl text-gray-500">/ {subject.maxScore}</span>
                      </div>
                      <div className="text-sm text-gray-600">
                        {subject.percentage.toFixed(1)}% • Grade: {subject.grade}
                      </div>
                    </motion.div>

                    {/* Enhanced Progress Bar */}
                    <div className="mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-xs font-medium text-gray-600">Progress</span>
                        <span className={`text-xs font-bold ${subject.textColor}`}>
                          {subject.percentage.toFixed(1)}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                        <motion.div
                          className={`h-full ${subject.progressColor} rounded-full relative`}
                          variants={progressVariants}
                          initial="initial"
                          animate="animate"
                          style={{ width: `${subject.percentage}%` }}
                        >
                          {/* Animated shine effect */}
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent transform -skew-x-12 animate-pulse"></div>
                        </motion.div>
                      </div>
                    </div>

                    {/* Expandable Details */}
                    <AnimatePresence>
                      {isSelected && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.4, ease: 'easeInOut' }}
                          className="border-t border-gray-200 pt-4 mt-4"
                        >
                          <div className="space-y-3">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600">Class Rank:</span>
                              <span className="text-sm font-semibold text-gray-800">
                                {subject.rank}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-600">Improvement:</span>
                              <span
                                className={`text-sm font-semibold ${
                                  subject.improvement.startsWith('+')
                                    ? 'text-green-600'
                                    : 'text-red-600'
                                }`}
                              >
                                {subject.improvement}
                              </span>
                            </div>
                            <button className="w-full mt-3 px-4 py-2 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-700 text-sm font-medium rounded-lg transition-all duration-200 flex items-center justify-center gap-2">
                              View Detailed Analysis
                              <ChevronRight className="w-4 h-4" />
                            </button>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {/* Hover Indicator */}
                    <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <ChevronRight className="w-5 h-5 text-gray-400" />
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Action Buttons */}
      </div>
    </div>
  );
};

export default TestPerformanceDashboard;
