import { useState, useMemo, useEffect } from 'react';
import PropTypes from 'prop-types';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  X,
  Edit,
  Trash2,
  Eye,
  FileText,
  ChevronLeft,
  ChevronRight,
  Table2,
  ArrowDown,
  ArrowUp,
  RefreshCw,
  Plus
} from 'lucide-react';

import clsx from 'clsx';

const DOTS = '...';

const Table = ({
  title = 'Table',
  titleIcon = <Table2 className="h-5 w-5" />,
  buttonName = 'Add New',
  onAddNew,
  header = [],
  data = [],
  searchBy = [],
  searchPlaceholder = 'Search...',
  onView,
  onEdit,
  onDelete,
  onPdf,
  customColumn,
  itemsPerPage = 10,
  loading = false,
  emptyMessage = 'No records found'
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState('');
  const [pageSize, setPageSize] = useState(itemsPerPage);
  const [sortConfig, setSortConfig] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hoverData, setHoverData] = useState({
    visible: false,
    content: '',
    relatedTopics: [],
    x: 0,
    y: 0,
    cellHeight: 0
  });

  // Sample related topics generator based on content
  const generateRelatedTopics = (content) => {
    // This is a simplified example; you can customize this logic
    const words = String(content).toLowerCase().split(' ');
    return words
      .filter((word) => word.length > 3)
      .slice(0, 3)
      .map((word) => `${word.charAt(0).toUpperCase() + word.slice(1)} Info`);
  };

  const visibleHeaders = useMemo(() => header.filter((col) => col.show), [header]);

  const processedData = useMemo(() => {
    let filtered = data;

    if (search.trim()) {
      const lowercasedSearchTerm = search.toLowerCase();
      filtered = data?.filter((item) => {
        const keysToSearch =
          searchBy?.length > 0 ? searchBy : visibleHeaders.map((h) => h.data).filter(Boolean);
        return keysToSearch.some((key) => {
          const value = key
            .split('.')
            .reduce((o, i) => (o && o[i] !== undefined ? o[i] : null), item);
          return String(value).toLowerCase().includes(lowercasedSearchTerm);
        });
      });
    }

    if (sortConfig !== null) {
      filtered.sort((a, b) => {
        const valA = sortConfig.key.split('.').reduce((o, i) => o?.[i], a);
        const valB = sortConfig.key.split('.').reduce((o, i) => o?.[i], b);

        if (valA < valB) return sortConfig.direction === 'ascending' ? -1 : 1;
        if (valA > valB) return sortConfig.direction === 'ascending' ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }, [search, data, visibleHeaders, searchBy, sortConfig]);

  const totalItems = processedData?.length;
  const totalPages = Math.ceil(totalItems / pageSize);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return processedData?.slice(startIndex, startIndex + pageSize);
  }, [processedData, currentPage, pageSize]);

  useEffect(() => {
    setCurrentPage(1);
  }, [search, pageSize, sortConfig]);

  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages);
    }
  }, [totalPages, currentPage]);

  const handleSort = (key) => {
    let direction = 'ascending';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  const range = (start, end) => {
    let length = end - start + 1;
    return Array.from({ length }, (_, idx) => idx + start);
  };

  const paginationRange = useMemo(() => {
    const totalPageNumbers = 7;
    if (totalPages <= totalPageNumbers) {
      return range(1, totalPages);
    }

    const siblingCount = 1;
    const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
    const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages);

    const shouldShowLeftDots = leftSiblingIndex > 2;
    const shouldShowRightDots = rightSiblingIndex < totalPages - 2;

    if (!shouldShowLeftDots && shouldShowRightDots) {
      let leftRange = range(1, 5);
      return [...leftRange, DOTS, totalPages];
    }

    if (shouldShowLeftDots && !shouldShowRightDots) {
      let rightRange = range(totalPages - 4, totalPages);
      return [1, DOTS, ...rightRange];
    }

    if (shouldShowLeftDots && shouldShowRightDots) {
      let middleRange = range(leftSiblingIndex, rightSiblingIndex);
      return [1, DOTS, ...middleRange, DOTS, totalPages];
    }
  }, [totalPages, currentPage]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white/80 backdrop-blur-md rounded-xl shadow-lg border border-white/20 overflow-hidden max-w-full relative"
    >
      {/* Header */}
      <div className="p-6 bg-indigo-600 border-b-4 border-indigo-700 shadow-lg">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-6">
          {/* Title Section with Floating Effect */}
          <motion.div
            className="flex items-center gap-4 group"
            initial={{ y: -20 }}
            animate={{ y: 0 }}
            transition={{ type: 'spring', stiffness: 300 }}
          >
            <motion.div
              whileHover={{
                rotate: [0, 15, -15, 0],
                scale: [1, 1.2, 1.2, 1],
                transition: { duration: 0.6 }
              }}
              className="p-3 bg-white backdrop-blur-sm rounded-xl shadow-lg border border-white/20"
            >
              <motion.div
                animate={{
                  y: [0, -5, 0],
                  transition: { repeat: Infinity, duration: 3 }
                }}
              >
                {titleIcon}
              </motion.div>
            </motion.div>

            <motion.h2
              className="text-3xl font-extrabold text-white drop-shadow-xl"
              initial={{ opacity: 0 }}
              animate={{
                opacity: 1
              }}
            >
              {title}
            </motion.h2>
          </motion.div>

          {/* Search and Actions Section */}
          <div className="w-full md:w-auto flex flex-col sm:flex-row items-stretch sm:items-center gap-9">
            {/* Search with Floating Label Effect */}
            <motion.div
              className="relative flex-1 sm:w-64 md:w-80"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="relative group">
                <input
                  placeholder=" "
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-full pt-5 pb-2 px-4 bg-white/95 rounded-xl border-2 border-white focus:outline-none focus:border-indigo-300 text-gray-800 shadow-lg peer"
                />
                <motion.label
                  className="absolute left-4  top-2 text-xs font-bold text-indigo-600 pointer-events-none"
                  animate={{
                    y: search ? -5 : 10,
                    scale: search ? 0.9 : 1,
                    opacity: search ? 1 : 0.7
                  }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  {searchPlaceholder}
                </motion.label>
                <Search className="absolute right-1 top-1 h-5 w-5 text-black group-hover:scale-110 transition-transform" />

                <AnimatePresence>
                  {search && (
                    <motion.button
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0, opacity: 0 }}
                      onClick={() => setSearch('')}
                      className="absolute right-0 top-1 h-6 w-6 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-600 hover:bg-indigo-200 shadow-sm"
                      whileHover={{ scale: 1.2 }}
                    >
                      <X className="h-3 w-3" />
                    </motion.button>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>

            {/* Action Buttons with Staggered Animation */}
            <motion.div
              className="flex gap-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ staggerChildren: 0.1 }}
            >
              <motion.button
                onClick={handleRefresh}
                className="p-3 bg-white/10 backdrop-blur-sm rounded-xl border-2 border-white/20 shadow-lg hover:bg-white/20 flex items-center justify-center"
                whileHover={{
                  scale: 1.1,
                  rotate: 360,
                  backgroundColor: 'rgba(255,255,255,0.2)'
                }}
                whileTap={{ scale: 0.9 }}
                disabled={isRefreshing}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
              >
                <RefreshCw className={`h-5 w-5 text-white ${isRefreshing ? 'animate-spin' : ''}`} />
              </motion.button>

              {onAddNew && (
                <motion.button
                  onClick={onAddNew}
                  className="relative overflow-hidden bg-white text-indigo-600 hover:text-indigo-700 px-6 py-3 rounded-xl font-bold flex items-center gap-2 shadow-lg"
                  whileHover={{
                    y: -3,
                    boxShadow: '0 10px 20px rgba(0,0,0,0.1)'
                  }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                >
                  <span className="relative z-10 flex items-center gap-2">
                    <Plus className="h-5 w-5" />
                    {buttonName}
                  </span>
                  <motion.span
                    className="absolute inset-0 bg-gradient-to-r from-white/30 to-transparent opacity-0"
                    whileHover={{
                      opacity: 1,
                      x: [0, 300],
                      transition: { duration: 0.8 }
                    }}
                  />
                </motion.button>
              )}
            </motion.div>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="relative max-h-[600px] overflow-y-auto overflow-x-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
        <AnimatePresence>
          {loading && (
            <motion.div
              key="loader"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-20 rounded-b-xl"
            >
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ repeat: Infinity, duration: 1, ease: 'linear' }}
                className="h-12 w-12 border-4 border-purple-500 border-t-transparent rounded-full"
              />
            </motion.div>
          )}
        </AnimatePresence>

        <table className="w-full table-auto whitespace-nowrap">
          <thead className="sticky top-0 z-10 bg-[var(--color-counselor)] backdrop-blur-sm border-b border-gray-200">
            <tr>
              {visibleHeaders.map((column) => (
                <th
                  key={column.data || column.header}
                  className={clsx(
                    'px-4 py-3 text-left text-sm font-semibold text-white uppercase tracking-wider',
                    'min-w-[150px] max-w-[250px]',
                    'border-r border-gray-200 last:border-r-0'
                  )}
                >
                  <motion.div
                    className={clsx('flex items-center gap-2', {
                      'cursor-pointer hover:text-blue': column.sortable
                    })}
                    onClick={() => column.sortable && handleSort(column.data)}
                    whileHover={{ x: column.sortable ? 3 : 0 }}
                  >
                    {column.header}
                    {column.sortable && sortConfig?.key === column.data && (
                      <motion.div initial={{ opacity: 0, y: -5 }} animate={{ opacity: 1, y: 0 }}>
                        {sortConfig.direction === 'ascending' ? (
                          <ArrowUp size={14} className="text-purple-500" />
                        ) : (
                          <ArrowDown size={14} className="text-purple-500" />
                        )}
                      </motion.div>
                    )}
                  </motion.div>
                </th>
              ))}
              {(onView || onEdit || onDelete || onPdf || customColumn) && (
                <th className="px-4 py-3 text-center text-sm font-semibold text-white uppercase tracking-wider min-w-[120px] max-w-[150px] border-r border-gray-200">
                  VIEW
                </th>
              )}
            </tr>
          </thead>

          <motion.tbody
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ staggerChildren: 0.05 }}
            className="divide-y divide-gray-200"
          >
            <AnimatePresence mode="sync">
              {paginatedData?.length > 0 ? (
                paginatedData.map((item, i) => {
                  const startIndex = (currentPage - 1) * pageSize;

                  return (
                    <motion.tr
                      key={item.id || startIndex + i}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="hover:bg-purple-50/50 transition-colors duration-200"
                      whileHover={{ backgroundColor: 'rgba(147, 51, 234, 0.1)' }}
                    >
                      {visibleHeaders.map((column) => (
                        <td
                          key={column.data}
                          className={clsx(
                            'px-4 py-3 text-sm truncate relative',
                            'min-w-[150px] max-w-[250px]',
                            column.className,
                            'border-r border-gray-200 last:border-r-0'
                          )}
                          onMouseEnter={(e) => {
                            const content = item[column.data] ?? '–';
                            if (content.length > 20) {
                              const rect = e.currentTarget.getBoundingClientRect();
                              setHoverData({
                                visible: true,
                                content,
                                relatedTopics: generateRelatedTopics(content),
                                x: rect.left + window.scrollX,
                                y: rect.top + window.scrollY + rect.height,
                                cellHeight: rect.height
                              });
                            }
                          }}
                          onMouseLeave={() =>
                            setHoverData({
                              visible: false,
                              content: '',
                              relatedTopics: [],
                              x: 0,
                              y: 0,
                              cellHeight: 0
                            })
                          }
                        >
                          <motion.div className="relative group" whileHover={{ x: 3 }}>
                            <span className="block truncate">
                              {column.render
                                ? column.render(item, startIndex + i)
                                : column.data === 'sno'
                                  ? startIndex + i + 1
                                  : (item[column.data] ?? '–')}
                            </span>
                          </motion.div>
                        </td>
                      ))}
                      {(onView || onEdit || onDelete || onPdf || customColumn) && (
                        <td className="px-4 py-3 text-center min-w-[120px] max-w-[150px] border-r border-gray-200">
                          <div className="flex justify-center gap-1">
                            {onView && (
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={() => onView(item)}
                                className="p-2 text-purple-600 hover:bg-purple-100 hover:cursor-pointer rounded-full"
                                title="View"
                              >
                                <Eye size={16} />
                              </motion.button>
                            )}
                            {onEdit && (
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={() => onEdit(item)}
                                className="p-2 text-green-600 hover:bg-green-100 rounded-full"
                                title="Edit"
                              >
                                <Edit size={16} />
                              </motion.button>
                            )}
                            {onPdf && (
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={() => onPdf(item)}
                                className="p-2 text-purple-600 hover:bg-purple-100 rounded-full"
                                title="PDF"
                              >
                                <FileText size={16} />
                              </motion.button>
                            )}
                            {onDelete && (
                              <motion.button
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                                onClick={() => onDelete(item)}
                                className="p-2 text-red-600 hover:bg-red-100 rounded-full"
                                title="Delete"
                              >
                                <Trash2 size={16} />
                              </motion.button>
                            )}
                          </div>
                        </td>
                      )}
                    </motion.tr>
                  );
                })
              ) : (
                <motion.tr initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
                  <td colSpan={visibleHeaders.length + 1} className="px-6 py-16 text-center">
                    <motion.div
                      className="flex flex-col items-center justify-center gap-4 text-gray-500"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.3 }}
                    >
                      <Search className="h-12 w-12 text-gray-300" />
                      <p className="text-lg">
                        {search ? `No results found for "${search}"` : emptyMessage}
                      </p>
                      {search && (
                        <motion.button
                          onClick={() => setSearch('')}
                          className="mt-2 px-4 py-2 bg-purple-50 text-purple-600 rounded-lg hover:bg-purple-100"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          Clear search
                        </motion.button>
                      )}
                    </motion.div>
                  </td>
                </motion.tr>
              )}
            </AnimatePresence>
          </motion.tbody>
        </table>

        {/* Hover Popup */}
        <AnimatePresence>
          {hoverData.visible && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 10 }}
              transition={{ type: 'spring', stiffness: 200, damping: 20 }}
              className="fixed bg-white/90 backdrop-blur-md rounded-lg shadow-xl border border-white/20 p-4 max-w-xs z-50"
              style={{
                left: `${hoverData.x}px`,
                top: `${hoverData.y + 5}px`
              }}
            >
              <div className="flex flex-col gap-3">
                <div className="text-gray-800 text-sm font-medium break-words">
                  {hoverData.content}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Footer */}
      {totalItems > 0 && (
        <motion.div
          className="p-4 border-t border-gray-200 bg-white/95 backdrop-blur-sm"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-3 text-sm text-gray-700">
              <span>Rows per page:</span>
              <select
                value={pageSize}
                onChange={(e) => setPageSize(Number(e.target.value))}
                className="border border-gray-200 rounded-md px-2 py-1 bg-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                {[10, 20, 50, 100].map((size) => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </select>
              <span>|</span>
              <span>
                {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, totalItems)}{' '}
                of {totalItems}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <motion.button
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
                className="p-2 border border-gray-200 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ChevronLeft size={16} />
              </motion.button>

              {paginationRange?.map((pageNumber, index) => {
                if (pageNumber === DOTS) {
                  return (
                    <span key={index} className="px-2 py-1 text-gray-500">
                      ...
                    </span>
                  );
                }

                return (
                  <motion.button
                    key={index}
                    onClick={() => setCurrentPage(pageNumber)}
                    className={clsx('w-10 h-10 rounded-md text-sm', {
                      'bg-[var(--color-student)] text-white font-medium':
                        pageNumber === currentPage,
                      'border border-gray-200 hover:bg-gray-50': pageNumber !== currentPage
                    })}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {pageNumber}
                  </motion.button>
                );
              })}

              <motion.button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="p-2 border border-gray-200 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ChevronRight size={16} />
              </motion.button>
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};

Table.propTypes = {
  title: PropTypes.string,
  titleIcon: PropTypes.node,
  buttonName: PropTypes.string,
  onAddNew: PropTypes.func,
  header: PropTypes.arrayOf(
    PropTypes.shape({
      header: PropTypes.string.isRequired,
      show: PropTypes.bool.isRequired,
      data: PropTypes.string,
      className: PropTypes.string,
      render: PropTypes.func,
      sortable: PropTypes.bool
    })
  ).isRequired,
  data: PropTypes.arrayOf(PropTypes.object).isRequired,
  searchBy: PropTypes.arrayOf(PropTypes.string),
  searchPlaceholder: PropTypes.string,
  onView: PropTypes.func,
  onEdit: PropTypes.func,
  onDelete: PropTypes.func,
  onPdf: PropTypes.func,
  customColumn: PropTypes.bool,
  itemsPerPage: PropTypes.number,
  loading: PropTypes.bool,
  emptyMessage: PropTypes.string
};

export default Table;
