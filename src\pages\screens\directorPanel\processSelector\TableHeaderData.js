const TopicMappingHeader = [
  {
    header: 'S.No',
    data: 'sno',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Course Name',
    data: 'course_name',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Subject Name',
    data: 'subject_name',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Topic Name',
    data: 'topic_name',
    className: 'overflow-text-wrap ',
    show: true
  }
];

const SubTopicMappingHeader = [
  {
    header: 'S.No',
    data: 'sno',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Course Name',
    data: 'course_name',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Subject Name',
    data: 'subject_name',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Topic Name',
    data: 'topic_name',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Sub Topic Name',
    data: 'sub_topic_name',
    className: 'overflow-text-wrap ',
    show: true
  }
];

const ContentMappingHeader = [
  {
    header: 'S.No',
    data: 'sno',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Course Name',
    data: 'course_name',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Subject Name',
    data: 'subject_name',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Topic Name',
    data: 'topic_name',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Sub Topic Name',
    data: 'sub_topic_name',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Process Selector URL',
    data: 'process_selector_url',
    className: 'overflow-text-wrap ',
    show: true
  },
  {
    header: 'Language',
    data: 'language',
    className: 'overflow-text-wrap ',
    show: true
  }
];
export { TopicMappingHeader, SubTopicMappingHeader, ContentMappingHeader };
