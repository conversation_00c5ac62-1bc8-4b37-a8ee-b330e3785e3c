import React from 'react';
import { motion } from 'framer-motion';
import { FileText, FileQuestion, Loader2 } from 'lucide-react';

function QuestionGenerationLoader() {
  return (
    <motion.div
      className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-white/95 backdrop-blur-sm"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Floating Document Stack */}
      <motion.div
        className="relative w-64 h-80 mb-8"
        animate={{
          y: [0, -5, 0]
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: 'easeInOut'
        }}
      >
        {/* Back Document */}
        <motion.div
          className="absolute inset-0 bg-[var(--color-trainee)]/10 border-2 border-[var(--color-trainee)]/20 rounded-lg shadow-md"
          style={{
            transform: 'rotate(2deg) translateY(10px)'
          }}
          animate={{
            rotate: [2, 3, 2],
            y: [10, 15, 10]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />

        {/* Main Document */}
        <motion.div
          className="absolute inset-0 bg-white border-2 border-[var(--color-trainee)] rounded-lg shadow-xl p-6 flex flex-col"
          animate={{
            rotate: [-1, 1, -1],
            y: [0, -5, 0]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        >
          {/* Document Header */}
          <div className="flex items-center gap-3 mb-4 pb-2 border-b border-[var(--color-trainee)]/20">
            <FileText className="w-5 h-5 text-[var(--color-trainee)]" />
            <span className="text-sm font-medium text-[var(--color-trainee)]">questions.pdf</span>
          </div>

          {/* Animated Content */}
          <div className="flex-1 space-y-3 overflow-hidden">
            {[1, 2, 3].map((i) => (
              <motion.div
                key={i}
                className="bg-[var(--color-trainee)]/5 rounded p-2"
                initial={{ opacity: 0, x: -20 }}
                animate={{
                  opacity: [0, 1, 0],
                  x: [-20, 0, 20]
                }}
                transition={{
                  duration: 2,
                  delay: i * 0.3,
                  repeat: Infinity,
                  repeatDelay: 1.5
                }}
              >
                <div className="h-3 bg-[var(--color-trainee)]/20 rounded w-3/4 mb-1"></div>
                <div className="h-2 bg-[var(--color-trainee)]/10 rounded w-full"></div>
              </motion.div>
            ))}
          </div>

          {/* Document Footer */}
          <div className="flex justify-between items-center mt-4 pt-2 border-t border-[var(--color-trainee)]/20 text-xs text-[var(--color-trainee)]/60">
            <span>Page 1 of 3</span>
            <motion.div
              className="flex items-center gap-1"
              animate={{ opacity: [0.6, 1, 0.6] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Loader2 className="w-3 h-3 animate-spin" />
              <span>Generating</span>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>

      {/* Progress Indicator */}
      <div className="w-64 max-w-full relative">
        <div className="h-1.5 bg-[var(--color-trainee)]/10 rounded-full overflow-hidden">
          <motion.div
            className="h-full bg-[var(--color-trainee)] rounded-full"
            initial={{ width: 0 }}
            animate={{ width: ['0%', '90%', '100%'] }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />
        </div>
        <motion.div
          className="absolute top-0 left-0 h-1.5 w-4 bg-white rounded-full shadow-lg"
          animate={{
            x: [0, 240, 0],
            opacity: [1, 0.8, 1]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />
      </div>

      {/* Status Text */}
      <motion.div
        className="mt-8 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <motion.h2
          className="text-2xl font-bold text-[var(--color-trainee)] mb-2"
          animate={{
            scale: [1, 1.02, 1],
            textShadow: [
              '0 0 0 rgba(245, 158, 11, 0)',
              '0 0 8px rgba(245, 158, 11, 0.2)',
              '0 0 0 rgba(245, 158, 11, 0)'
            ]
          }}
          transition={{
            duration: 2.5,
            repeat: Infinity
          }}
        >
          Building Your Paper Based Question Paper
        </motion.h2>
        <motion.p
          className="text-[var(--color-trainee)]/80"
          animate={{
            opacity: [0.8, 1, 0.8]
          }}
          transition={{
            duration: 2,
            repeat: Infinity
          }}
        >
          questions being prepared...
        </motion.p>
      </motion.div>

      {/* Floating Elements */}
      <motion.div
        className="absolute top-1/4 left-1/4 text-[var(--color-trainee)]/20"
        animate={{
          y: [0, -20, 0],
          rotate: [0, 360]
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: 'linear'
        }}
      >
        <FileText size={40} />
      </motion.div>
      <motion.div
        className="absolute top-1/4 right-1/4 text-[var(--color-trainee)]/20"
        animate={{
          y: [0, -20, 0],
          rotate: [0, 360]
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: 'linear'
        }}
      >
        <FileText size={40} />
      </motion.div>
      <motion.div
        className="absolute bottom-1/4 left-1/4 text-[var(--color-trainee)]/15"
        animate={{
          y: [0, 20, 0],
          rotate: [0, -360]
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          ease: 'linear'
        }}
      >
        <FileQuestion size={48} />
      </motion.div>
      <motion.div
        className="absolute bottom-1/4 right-1/4 text-[var(--color-trainee)]/15"
        animate={{
          y: [0, 20, 0],
          rotate: [0, -360]
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          ease: 'linear'
        }}
      >
        <FileQuestion size={48} />
      </motion.div>
    </motion.div>
  );
}

export default QuestionGenerationLoader;
