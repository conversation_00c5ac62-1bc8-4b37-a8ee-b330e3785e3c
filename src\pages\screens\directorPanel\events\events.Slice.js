import { createSlice } from '@reduxjs/toolkit';
import { directorApi } from '../../../../redux/api/api'; // Adjust path based on your structure

const initialState = {
  events: [],
  selectedEvent: null,
  kotaTeachers: [],
  upcomingEvents: [],
  eventDocuments: null
};

// Extend eventApi with endpoints
export const addEventSlice = directorApi.injectEndpoints({
  endpoints: (builder) => ({
    // Create a new event
    createEvent: builder.mutation({
      query: (body) => ({
        url: '/events',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Create Event Response:', response);
        return response.event || response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Events']
    }),
    // List all events with filters
    listEvents: builder.query({
      query: (params) => ({
        url: '/events',
        method: 'GET',
        params,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('List Events Response:', response);
        return response.events || [];
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Events']
    }),
    // Get a specific event
    getEvent: builder.query({
      query: (eventId) => ({
        url: `/events/${eventId}`,
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Get Event Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Events']
    }),
    // Update an event
    updateEvent: builder.mutation({
      query: ({ eventId, body }) => ({
        url: `/events/${eventId}`,
        method: 'PUT',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Update Event Response:', response);
        return response.event || response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['Events']
    }),
    // Delete an event
    deleteEvent: builder.mutation({
      query: (eventId) => ({
        url: `/events/${eventId}`,
        method: 'DELETE',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Delete Event Response:', response);
        return response.message || 'Event deleted';
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['Events']
    }),
    // Get list of Kota teachers
    getKotaTeachers: builder.query({
      query: () => ({
        url: '/kota-teachers',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Kota Teachers Response:', response);
        return response.teachers || [];
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['KotaTeachers']
    }),
    // Get events by teacher
    getEventsByTeacher: builder.query({
      query: (teacherId) => ({
        url: `/events/by-teacher/${teacherId}`,
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Events by Teacher Response:', response);
        return response.events || [];
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Events']
    }),
    // Get upcoming events
    getUpcomingEvents: builder.query({
      query: () => ({
        url: '/events/upcoming',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Upcoming Events Response:', response);
        return response.events || [];
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Events']
    }),
    // Download event document
    downloadEventDocument: builder.query({
      query: ({ eventId, documentType }) => ({
        url: `/events/${eventId}/documents/${documentType}/download`,
        method: 'GET',
        responseHandler: async (response) => {
          // Handle the response as a Blob
          const blob = await response.blob();
          return {
            blob: blob,
            filename: `${documentType}_document.pdf` // Adjust filename based on response or documentType
          };
        }
      }),
      transformResponse: (response) => {
        console.log('Download Document Response:', response);
        return response.download_url ? response : null;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['EventDocuments']
    }),
    // Delete event documents
    deleteEventDocuments: builder.mutation({
      query: ({ eventId, body }) => ({
        url: `/events/${eventId}/documents`,
        method: 'DELETE',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Delete Documents Response:', response);
        return response.message || 'Documents deleted';
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['EventDocuments']
    }),
    // List events for the logged-in teacher
    listTeacherEvents: builder.query({
      query: (params) => ({
        url: '/list_teacher_events',
        method: 'GET',
        params,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('List Teacher Events Response:', response);
        return response.events || [];
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Events']
    })
  })
});

// Create Redux slice
const eventSlice = createSlice({
  name: 'events',
  initialState,
  reducers: {
    setEvents: (state, action) => {
      state.events = action.payload;
    },
    setSelectedEvent: (state, action) => {
      state.selectedEvent = action.payload;
    },
    setKotaTeachers: (state, action) => {
      state.kotaTeachers = action.payload;
    },
    setUpcomingEvents: (state, action) => {
      state.upcomingEvents = action.payload;
    },
    setEventDocuments: (state, action) => {
      state.eventDocuments = action.payload;
    }
  }
});

export const {
  setEvents,
  setSelectedEvent,
  setKotaTeachers,
  setUpcomingEvents,
  setEventDocuments
} = eventSlice.actions;
export default eventSlice.reducer;

// Export RTK Query hooks
export const {
  useCreateEventMutation,
  useListEventsQuery,
  useGetEventQuery,
  useUpdateEventMutation,
  useDeleteEventMutation,
  useGetKotaTeachersQuery,
  useGetEventsByTeacherQuery,
  useGetUpcomingEventsQuery,
  useDownloadEventDocumentQuery,
  useDeleteEventDocumentsMutation,
  useListTeacherEventsQuery
} = addEventSlice;
