import { pdfQuestionGenratorApi } from '../../../../redux/api/api';

export const pdfQuestionGeneratorSlice = pdfQuestionGenratorApi.injectEndpoints({
  endpoints: (builder) => ({
    getPdfQuestionGeneratorUploadService: builder.mutation({
      query: (body) => ({
        url: '/upload_question',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['pdfQuestionGenratorApi']
    }),
    getPdfQuestionGeneratorCreateService: builder.query({
      query: (arg) => ({
        url: `/generate_pdf/${arg.question_id}`,
        method: 'GET',

        responseHandler: async (res) => res.blob()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['pdfQuestionGenratorApi']
    })
  })
});

export const {
  useGetPdfQuestionGeneratorUploadServiceMutation,
  useLazyGetPdfQuestionGeneratorCreateServiceQuery
} = pdfQuestionGeneratorSlice;
