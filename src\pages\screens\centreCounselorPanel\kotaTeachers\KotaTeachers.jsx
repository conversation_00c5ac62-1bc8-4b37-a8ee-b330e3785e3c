import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUserTie,
  faSearch,
  faSpinner,
  faExclamationTriangle,
  faEnvelope,
  faPhone,
  faCalendarAlt,
  faChevronDown,
  faChalkboardTeacher,
  faIdCard,
  faUserGraduate
} from '@fortawesome/free-solid-svg-icons';
import { useLazyGetListKotaTeachersQuery } from '../centreCounselorDashboard/centreCounselorDashboard.slice';

const KotaTeachers = () => {
  const [getListKotaTeachers, { data: kotaTeachersData, isLoading, error }] =
    useLazyGetListKotaTeachersQuery();
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredTeachers, setFilteredTeachers] = useState([]);
  const [expandedCard, setExpandedCard] = useState(null);
  const [activeFilter, setActiveFilter] = useState('all');

  useEffect(() => {
    getListKotaTeachers();
  }, [getListKotaTeachers]);

  const teachers = kotaTeachersData?.teachers || [];

  useEffect(() => {
    if (!teachers || teachers.length === 0) {
      setFilteredTeachers([]);
      return;
    }

    let results = [...teachers];

    // Apply search filter
    if (searchTerm.trim()) {
      const lowercasedSearch = searchTerm.toLowerCase();
      results = results.filter(
        (teacher) =>
          `${teacher.first_name} ${teacher.last_name}`.toLowerCase().includes(lowercasedSearch) ||
          teacher.email?.toLowerCase().includes(lowercasedSearch) ||
          teacher.username?.toLowerCase().includes(lowercasedSearch) ||
          teacher.phone?.toLowerCase().includes(lowercasedSearch)
      );
    }

    // Apply status filter (if you had status data)
    if (activeFilter !== 'all') {
      // Example: results = results.filter(teacher => teacher.status === activeFilter);
    }

    setFilteredTeachers(results);
  }, [searchTerm, teachers, activeFilter]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  const toggleCard = (id) => {
    setExpandedCard(expandedCard === id ? null : id);
  };

  const getInitials = (firstName, lastName) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };

  const getRandomColor = (str) => {
    const colors = [
      'bg-[var(--color-counselor)]',
      'bg-[var(--color-counselor)]',
      'bg-[var(--color-counselor)]',
      'bg-[var(--color-counselor)]',
      'bg-[var(--color-counselor)]'
    ];
    const index = str?.length ? str.charCodeAt(0) % colors.length : 0;
    return colors[index];
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8"
        >
          <div className="mb-4 md:mb-0">
            <h1 className="text-3xl font-bold text-gray-800 flex items-center">
              <FontAwesomeIcon
                icon={faChalkboardTeacher}
                className="mr-3 text-[var(--color-counselor)]"
              />
              Kota Teachers
            </h1>
            <p className="text-gray-600 mt-1">
              {teachers.length} {teachers.length === 1 ? 'teacher' : 'teachers'} registered
            </p>
          </div>

          {/* Search and Filters */}
          <div className="w-full md:w-auto space-y-3">
            <div className="relative">
              <input
                type="text"
                placeholder="Search teachers..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="w-full md:w-64 pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[var(--color-counselor)] focus:border-transparent shadow-sm"
              />
              <FontAwesomeIcon
                icon={faSearch}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              {searchTerm && (
                <button
                  onClick={clearSearch}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-[var(--color-counselor)]"
                >
                  ✕
                </button>
              )}
            </div>

            <div className="flex space-x-2">
              <button
                onClick={() => setActiveFilter('all')}
                className={`px-3 py-1 text-sm rounded-full ${activeFilter === 'all' ? 'bg-[var(--color-counselor)] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
              >
                All
              </button>
              {/* Add more filters as needed */}
            </div>
          </div>
        </motion.div>

        {/* Loading State */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex flex-col items-center justify-center py-20"
          >
            <FontAwesomeIcon
              icon={faSpinner}
              spin
              className="text-[var(--color-counselor)] text-4xl mb-4"
            />
            <p className="text-gray-700 text-lg">Loading Kota teachers...</p>
          </motion.div>
        )}

        {/* Error State */}
        {error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex flex-col items-center justify-center py-20 px-4 bg-red-50 rounded-xl"
          >
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-500 text-4xl mb-4" />
            <h3 className="text-xl font-medium text-gray-800 mb-2">Failed to load data</h3>
            <p className="text-gray-600 text-center max-w-md">
              {error?.data?.message || 'Please try again later or contact support'}
            </p>
            <button
              onClick={() => getListKotaTeachers()}
              className="mt-4 px-4 py-2 bg-[var(--color-counselor)] text-white rounded-lg hover:bg-[var(--color-counselor)] transition-colors"
            >
              Retry
            </button>
          </motion.div>
        )}

        {/* Teachers Grid */}
        {!isLoading && !error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 cursor-pointer lg:grid-cols-3 gap-6"
          >
            {filteredTeachers.length > 0 ? (
              filteredTeachers.map((teacher) => (
                <motion.div
                  key={teacher.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-shadow"
                >
                  <div
                    className={`p-6 ${expandedCard === teacher.id ? 'border-b border-gray-200' : ''}`}
                    onClick={() => toggleCard(teacher.id)}
                  >
                    <div className="flex items-start space-x-4">
                      <div
                        className={`flex-shrink-0 h-12 w-12 rounded-full flex items-center justify-center text-white ${getRandomColor(teacher.first_name)}`}
                      >
                        {getInitials(teacher.first_name, teacher.last_name)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-gray-800 truncate">
                          {teacher.first_name} {teacher.last_name}
                        </h3>
                        <p className="text-sm text-gray-500 truncate">
                          {teacher.username || 'No username'}
                        </p>
                      </div>
                      <button
                        className="text-gray-400 cursor-pointer hover:text-[var(--color-counselor)] transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleCard(teacher.id);
                        }}
                      >
                        <FontAwesomeIcon
                          icon={faChevronDown}
                          className={`transition-transform ${expandedCard === teacher.id ? 'rotate-180' : ''}`}
                        />
                      </button>
                    </div>

                    <div className="mt-4 grid grid-cols-2 gap-2">
                      <div className="flex items-center text-sm text-gray-600">
                        <FontAwesomeIcon
                          icon={faEnvelope}
                          className="mr-2 text-[var(--color-counselor)]"
                        />
                        <span className="truncate">{teacher.email || 'No email'}</span>
                      </div>

                      {/* <div className="flex items-center text-sm text-gray-600">
                        <FontAwesomeIcon
                          icon={faPhone}
                          className="mr-2 text-[var(--color-counselor)]"
                        />
                        <span>{teacher.phone || 'No phone'}</span>
                      </div> */}
                      <div className="flex items-center text-sm text-gray-600">
                        <FontAwesomeIcon
                          icon={faCalendarAlt}
                          className="mr-2 text-[var(--color-counselor)]"
                        />
                        <span>
                          {teacher.created_at
                            ? new Date(teacher.created_at).toLocaleDateString()
                            : 'Unknown date'}
                        </span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <FontAwesomeIcon
                          icon={faIdCard}
                          className="mr-2 text-[var(--color-counselor)]"
                        />
                        <span>{teacher.course || 'No course'}</span>
                      </div>
                    </div>
                  </div>

                  <AnimatePresence>
                    {expandedCard === teacher.id && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="px-6 py-4 bg-gray-50"
                      >
                        <div className="space-y-3">
                          <div>
                            <h4 className="text-sm font-medium text-center text-[var(--color-counselor)] mb-1">
                              Account Details
                            </h4>
                            <div className="space-y-2 pl-2">
                              <p className="text-sm text-gray-600">
                                <span className="font-medium">Username:</span> {teacher.username}
                              </p>
                              <p className="text-sm text-gray-600 flex items-center">
                                <b> EmailId : </b>
                                {teacher.email || 'No email'}
                              </p>
                              <p className="text-sm text-gray-600">
                                <span className="font-medium">Joined:</span>{' '}
                                {teacher.created_at
                                  ? new Date(teacher.created_at).toLocaleDateString('en-US', {
                                      year: 'numeric',
                                      month: 'long',
                                      day: 'numeric'
                                    })
                                  : 'Unknown date'}
                              </p>
                            </div>
                          </div>

                          {/* Add more details as needed */}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="col-span-full py-12 text-center"
              >
                <div className="inline-flex items-center justify-center w-20 h-20 bg-indigo-100 rounded-full mb-4">
                  <FontAwesomeIcon
                    icon={faUserGraduate}
                    className="text-[var(--color-counselor)] text-2xl"
                  />
                </div>
                <h3 className="text-xl font-medium text-gray-800 mb-2">
                  {teachers.length > 0 ? 'No matching teachers' : 'No Teachers Found'}
                </h3>
                <p className="text-gray-600 max-w-md mx-auto">
                  {teachers.length > 0
                    ? `No teachers match your search for "${searchTerm}". Try a different search term.`
                    : 'There are no teachers registered in the system yet.'}
                </p>
                {searchTerm && (
                  <button
                    onClick={clearSearch}
                    className="mt-4 px-4 py-2 bg-[var(--color-counselor)] text-white rounded-lg hover:bg-[var(--color-counselor)] transition-colors"
                  >
                    Clear Search
                  </button>
                )}
              </motion.div>
            )}
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default KotaTeachers;
