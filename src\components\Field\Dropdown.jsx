import PropTypes from 'prop-types';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown } from '@fortawesome/free-solid-svg-icons';

const Dropdown = ({
  name,
  value,
  onChange,
  options,
  placeholder,
  required,
  disabled,
  className,
  error,
  errorMessage,
  leftIcon
}) => {
  const isEmpty = !value;

  return (
    <div className="relative inline-block w-full">
      <motion.div
        animate={error ? { x: [0, -5, 5, -5, 5, 0] } : {}}
        transition={{ duration: 0.3 }}
        className="relative flex items-center"
      >
        {leftIcon && (
          <span className="absolute inset-y-0 left-3 flex items-center text-gray-500 z-10">
            {leftIcon}
          </span>
        )}
        <select
          name={name}
          value={value}
          onChange={onChange}
          required={required}
          disabled={disabled}
          className={`w-full py-3 text-black placeholder-transparent peer appearance-none
            ${leftIcon ? 'pl-12' : 'px-4'} pr-10 border-b-2
            ${className || ''}
            ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300 focus:border-black focus:ring-black'}
            ${disabled ? 'cursor-not-allowed bg-gray-200' : 'cursor-pointer'}`}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {/* {placeholder && (
          <motion.span
            className={`absolute left-4 text-gray-500 pointer-events-none
              ${isEmpty ? 'top-1/2 -translate-y-1/2 text-xl' : 'top-[-0.5rem] text-sm left-0 -translate-y-0'}
              transition-all duration-300 bg-white px-1 ${leftIcon ? 'pl-6' : ''}`}
            initial={{ y: isEmpty ? 0 : -12, fontSize: isEmpty ? '1.25rem' : '0.75rem' }}
            animate={{
              y: isEmpty ? 0 : -12,
              fontSize: isEmpty ? '1.25rem' : '0.75rem',
              top: isEmpty ? '50%' : '-0.5rem',
              transform: isEmpty ? 'translateY(-50%)' : 'translateY(0)',
              left: isEmpty ? (leftIcon ? '2.5rem' : '1rem') : '0'
            }}
            transition={{ duration: 0.3 }}>
            {placeholder}
          </motion.span>
        )} */}
        <span className="absolute inset-y-0 right-3 flex items-center text-gray-500 pointer-events-none">
          <FontAwesomeIcon icon={faChevronDown} />
        </span>
      </motion.div>
      {error && (
        <span className="text-red-500 text-sm mt-1">
          {errorMessage || 'This field is required'}
        </span>
      )}
    </div>
  );
};

Dropdown.propTypes = {
  name: PropTypes.string.isRequired,
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired
    })
  ).isRequired,
  placeholder: PropTypes.string,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  className: PropTypes.string,
  error: PropTypes.bool,
  errorMessage: PropTypes.string,
  leftIcon: PropTypes.node
};

export default Dropdown;
