/**
 * BULLETPROOF Connection Quality Monitor
 * Monitors and optimizes connection quality for seamless streaming
 * Prevents lag, disconnections, and quality degradation
 */

class ConnectionQualityMonitor {
  constructor() {
    this.isActive = false;
    this.room = null;
    this.qualityHistory = [];
    this.lagDetectionHistory = [];
    this.monitorInterval = null;
    this.optimizationInterval = null;
    this.lastOptimization = 0;
    this.connectionStats = {
      bytesReceived: 0,
      bytesSent: 0,
      packetsLost: 0,
      roundTripTime: 0,
      jitter: 0
    };
  }

  // Start monitoring connection quality
  start(room) {
    if (!room || this.isActive) return;
    
    console.log('📊 Starting BULLETPROOF connection quality monitoring...');
    
    this.room = room;
    this.isActive = true;
    
    // Start quality monitoring
    this.startQualityMonitoring();
    
    // Start lag detection
    this.startLagDetection();
    
    // Start automatic optimization
    this.startAutomaticOptimization();
    
    console.log('✅ Connection quality monitoring active');
  }

  // Stop monitoring
  stop() {
    console.log('🛑 Stopping connection quality monitoring...');
    
    this.isActive = false;
    this.room = null;
    
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
      this.optimizationInterval = null;
    }
    
    console.log('✅ Connection quality monitoring stopped');
  }

  // Start quality monitoring
  startQualityMonitoring() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
    }

    this.monitorInterval = setInterval(async () => {
      if (!this.isActive || !this.room) return;

      try {
        await this.checkConnectionQuality();
      } catch (error) {
        console.error('❌ Quality monitoring error:', error);
      }
    }, 5000); // Every 5 seconds
  }

  // Check connection quality
  async checkConnectionQuality() {
    try {
      const stats = await this.getConnectionStats();
      const quality = this.calculateQuality(stats);
      
      // Store quality history
      this.qualityHistory.push({
        timestamp: Date.now(),
        quality: quality,
        stats: stats
      });
      
      // Keep only last 20 measurements
      if (this.qualityHistory.length > 20) {
        this.qualityHistory.shift();
      }
      
      console.log('📊 Connection quality:', quality, 'Stats:', stats);
      
      // Take action based on quality
      if (quality === 'poor') {
        await this.handlePoorQuality(stats);
      } else if (quality === 'fair') {
        await this.handleFairQuality(stats);
      }
      
    } catch (error) {
      console.error('❌ Failed to check connection quality:', error);
    }
  }

  // Get connection statistics
  async getConnectionStats() {
    try {
      if (!this.room || !this.room.engine || !this.room.engine.client) {
        return this.connectionStats;
      }

      const pc = this.room.engine.client.pc;
      if (!pc) return this.connectionStats;

      const stats = await pc.getStats();
      const connectionStats = {
        bytesReceived: 0,
        bytesSent: 0,
        packetsLost: 0,
        roundTripTime: 0,
        jitter: 0,
        bandwidth: 0
      };

      stats.forEach((report) => {
        if (report.type === 'inbound-rtp') {
          connectionStats.bytesReceived += report.bytesReceived || 0;
          connectionStats.packetsLost += report.packetsLost || 0;
          connectionStats.jitter += report.jitter || 0;
        } else if (report.type === 'outbound-rtp') {
          connectionStats.bytesSent += report.bytesSent || 0;
        } else if (report.type === 'candidate-pair' && report.state === 'succeeded') {
          connectionStats.roundTripTime = report.currentRoundTripTime || 0;
        }
      });

      this.connectionStats = connectionStats;
      return connectionStats;
      
    } catch (error) {
      console.error('❌ Failed to get connection stats:', error);
      return this.connectionStats;
    }
  }

  // Calculate connection quality
  calculateQuality(stats) {
    let score = 100;
    
    // Penalize high round trip time
    if (stats.roundTripTime > 0.3) score -= 30; // > 300ms
    else if (stats.roundTripTime > 0.15) score -= 15; // > 150ms
    
    // Penalize packet loss
    if (stats.packetsLost > 10) score -= 40;
    else if (stats.packetsLost > 5) score -= 20;
    
    // Penalize high jitter
    if (stats.jitter > 0.05) score -= 20; // > 50ms
    else if (stats.jitter > 0.02) score -= 10; // > 20ms
    
    if (score >= 80) return 'excellent';
    if (score >= 60) return 'good';
    if (score >= 40) return 'fair';
    return 'poor';
  }

  // Handle poor quality
  async handlePoorQuality(stats) {
    console.warn('⚠️ Poor connection quality detected, optimizing...');
    
    try {
      // Strategy 1: Reduce video quality
      await this.reduceVideoQuality();
      
      // Strategy 2: Disable simulcast
      await this.disableSimulcast();
      
      // Strategy 3: Force reconnection if very poor
      if (stats.packetsLost > 20 || stats.roundTripTime > 0.5) {
        console.log('🔄 Very poor quality, forcing reconnection...');
        this.triggerReconnection();
      }
      
    } catch (error) {
      console.error('❌ Failed to handle poor quality:', error);
    }
  }

  // Handle fair quality
  async handleFairQuality(stats) {
    console.log('📊 Fair connection quality, applying optimizations...');
    
    try {
      // Strategy 1: Optimize video encoding
      await this.optimizeVideoEncoding();
      
    } catch (error) {
      console.error('❌ Failed to handle fair quality:', error);
    }
  }

  // Reduce video quality
  async reduceVideoQuality() {
    try {
      if (!this.room || !this.room.localParticipant) return;
      
      const videoTracks = Array.from(this.room.localParticipant.videoTracks.values());
      
      for (const publication of videoTracks) {
        if (publication.track) {
          // Reduce resolution and bitrate
          await publication.track.setVideoQuality({
            resolution: { width: 640, height: 480 },
            maxBitrate: 500000, // 500 kbps
            maxFramerate: 15
          });
          
          console.log('📉 Reduced video quality for better connection');
        }
      }
    } catch (error) {
      console.error('❌ Failed to reduce video quality:', error);
    }
  }

  // Disable simulcast
  async disableSimulcast() {
    try {
      if (!this.room || !this.room.localParticipant) return;
      
      const videoTracks = Array.from(this.room.localParticipant.videoTracks.values());
      
      for (const publication of videoTracks) {
        if (publication.track && publication.simulcasted) {
          // Disable simulcast for better stability
          await publication.track.setSimulcast(false);
          console.log('📡 Disabled simulcast for better stability');
        }
      }
    } catch (error) {
      console.error('❌ Failed to disable simulcast:', error);
    }
  }

  // Optimize video encoding
  async optimizeVideoEncoding() {
    try {
      if (!this.room || !this.room.localParticipant) return;
      
      const videoTracks = Array.from(this.room.localParticipant.videoTracks.values());
      
      for (const publication of videoTracks) {
        if (publication.track) {
          // Optimize encoding parameters
          await publication.track.setVideoQuality({
            resolution: { width: 1280, height: 720 },
            maxBitrate: 1500000, // 1.5 Mbps
            maxFramerate: 30
          });
          
          console.log('⚡ Optimized video encoding');
        }
      }
    } catch (error) {
      console.error('❌ Failed to optimize video encoding:', error);
    }
  }

  // Start lag detection
  startLagDetection() {
    setInterval(() => {
      if (!this.isActive) return;
      
      const now = Date.now();
      this.lagDetectionHistory.push(now);
      
      // Keep only last 10 measurements
      if (this.lagDetectionHistory.length > 10) {
        this.lagDetectionHistory.shift();
      }
      
      // Check for lag (irregular intervals)
      if (this.lagDetectionHistory.length >= 3) {
        const intervals = [];
        for (let i = 1; i < this.lagDetectionHistory.length; i++) {
          intervals.push(this.lagDetectionHistory[i] - this.lagDetectionHistory[i-1]);
        }
        
        const avgInterval = intervals.reduce((a, b) => a + b) / intervals.length;
        const maxDeviation = Math.max(...intervals.map(i => Math.abs(i - avgInterval)));
        
        if (maxDeviation > 2000) { // More than 2 second deviation
          console.warn('⚠️ Lag detected, taking corrective action...');
          this.handleLagDetection();
        }
      }
    }, 1000); // Every second
  }

  // Handle lag detection
  handleLagDetection() {
    console.log('🔧 Handling lag detection...');
    
    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }
    
    // Force WebSocket keep-alive
    if (window.bulletproofWebSocket) {
      window.bulletproofWebSocket.forceKeepAlive();
    }
    
    // Trigger optimization
    this.triggerOptimization();
  }

  // Start automatic optimization
  startAutomaticOptimization() {
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
    }

    this.optimizationInterval = setInterval(() => {
      if (!this.isActive) return;
      
      this.triggerOptimization();
    }, 30000); // Every 30 seconds
  }

  // Trigger optimization
  triggerOptimization() {
    const now = Date.now();
    if (now - this.lastOptimization < 10000) return; // Throttle optimizations
    
    this.lastOptimization = now;
    
    console.log('⚡ Triggering connection optimization...');
    
    // Optimize based on current quality
    if (this.qualityHistory.length > 0) {
      const latestQuality = this.qualityHistory[this.qualityHistory.length - 1];
      
      if (latestQuality.quality === 'poor') {
        this.handlePoorQuality(latestQuality.stats);
      } else if (latestQuality.quality === 'fair') {
        this.handleFairQuality(latestQuality.stats);
      }
    }
  }

  // Trigger reconnection
  triggerReconnection() {
    console.log('🔄 Triggering reconnection due to poor quality...');
    
    // Dispatch custom event for reconnection
    window.dispatchEvent(new CustomEvent('connection-quality-reconnection-required', {
      detail: {
        reason: 'poor-quality',
        timestamp: Date.now(),
        stats: this.connectionStats
      }
    }));
  }

  // Get current quality status
  getQualityStatus() {
    if (this.qualityHistory.length === 0) {
      return { quality: 'unknown', stats: this.connectionStats };
    }
    
    const latest = this.qualityHistory[this.qualityHistory.length - 1];
    return {
      quality: latest.quality,
      stats: latest.stats,
      history: this.qualityHistory.slice(-5) // Last 5 measurements
    };
  }
}

// Create singleton instance
const connectionQualityMonitor = new ConnectionQualityMonitor();

export default connectionQualityMonitor;
