import { chatApi } from '../../../../redux/api/api';
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  queryData: null
};

export const chatSlice = chatApi.injectEndpoints({
  endpoints: (builder) => ({
    StudentQueryService: builder.mutation({
      query: (formData) => ({
        url: '/query',
        method: 'POST',
        body: formData, // Use FormData for multipart/form-data
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Query Response:', response);
        return {
          success: true,
          user_id: response.user_id,
          session_id: response.session_id,
          ocr_text: response.ocr_text || null,
          transcribed_text: response.transcribed_text || null,
          response: response.response || 'No response provided'
        };
      },
      transformErrorResponse: (error) => ({
        status: error.status,
        message: error.data?.error || 'Error processing query',
        details: error.data?.details || null
      }),
      invalidatesTags: ['Query'] // Tag for query-related cache invalidation
    })
  })
});

const chatQuerySlice = createSlice({
  name: 'chatQuery',
  initialState,
  reducers: {
    setQueryData: (state, action) => {
      state.queryData = action.payload;
    }
  }
});

export const { setQueryData } = chatQuerySlice.actions;
export default chatQuerySlice.reducer;
export const { useStudentQueryServiceMutation } = chatSlice;
