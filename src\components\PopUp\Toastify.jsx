import { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Bounce, toast, ToastContainer } from 'react-toastify';

const Toastify = ({ res, resClear }) => {
  useEffect(() => {
    if (!res) return;

    if (res.status >= 200 && res.status <= 299) {
      toast.success(res.data?.message || res?.data || res?.message);
    } else if (res.status >= 400 && res.status <= 499) {
      const errorMessage =
        res?.data ||
        res?.data.message ||
        res?.response?.data?.message ||
        res?.response?.data ||
        `Client error: ${res.status} ${res.statusText}`;
      toast.warn(errorMessage || 'Something went wrong!');
    } else if (res.status >= 500 && res.status <= 599) {
      const errorMessage =
        res?.response?.data?.message ||
        res?.response?.data ||
        `Server error: ${res.status} ${res.statusText}`;
      toast.error(errorMessage || 'Something went wrong!');
    } else {
      toast.info(res?.message || 'Info');
    }
    resClear();
  }, [res, resClear]);

  return (
    <ToastContainer
      position="top-right"
      autoClose={2000}
      hideProgressBar={false}
      newestOnTop={false}
      closeOnClick={false}
      rtl={false}
      pauseOnFocusLoss
      draggable
      pauseOnHover
      theme="light"
      transition={Bounce}
    />
  );
};

Toastify.propTypes = {
  res: PropTypes.shape({
    status: PropTypes.number.isRequired,
    statusText: PropTypes.string,
    message: PropTypes.string,
    data: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
    response: PropTypes.shape({
      data: PropTypes.any,
      message: PropTypes.string
    })
  }),
  resClear: PropTypes.func.isRequired
};

export default Toastify;
