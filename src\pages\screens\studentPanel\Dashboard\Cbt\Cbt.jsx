import React, { useEffect, useState } from 'react';
import { setCbt, useLazyGetCbtServiceQuery } from '../dashboard.slice';
import Toastify from '../../../../../components/PopUp/Toastify';
import { useDispatch, useSelector } from 'react-redux';
import Table from '../../../../../components/Layout/Table';
import { MathsTableHeader } from './MathsTableHeader';
import PopUp from '../../../../../components/PopUp/PopUp';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Eye, BookOpen, ChevronDown, BookA } from 'lucide-react';

const Cbt = () => {
  const [res, setRes] = useState(null);
  const [open, setOpen] = useState(false);
  const [score, setScore] = useState({});
  const [selectedSubject, setSelectedSubject] = useState('maths');

  const [getCbtDataService] = useLazyGetCbtServiceQuery();
  const dispatch = useDispatch();
  const cbtData = useSelector((state) => state.studentDashboard.cbt);

  useEffect(() => {
    getCbtData();
  }, []);

  const getCbtData = async () => {
    try {
      const res = await getCbtDataService({ userId: sessionStorage.userId }).unwrap();
      dispatch(setCbt(res));
    } catch (error) {
      setRes(error);
    }
  };

  const handleViewCbtData = (testData) => {
    setScore(testData);
    setOpen(true);
  };

  const tableHeaders = {
    maths: MathsTableHeader,
    physics: MathsTableHeader,
    chemistry: MathsTableHeader,
    biology: MathsTableHeader
  };

  const combinedData = cbtData
    ? Object.keys(cbtData).reduce((acc, subject) => {
        const subjectData = cbtData[subject].map((test) => ({
          ...test,
          subject_name: subject.charAt(0).toUpperCase() + subject.slice(1),
          start_time: test.start_time || 'N/A',
          end_time: test.end_time || 'N/A',
          test_number: test.test_number || 'N/A'
        }));
        return [...acc, ...subjectData];
      }, [])
    : [];

  const filteredData = selectedSubject
    ? combinedData.filter((test) => test.subject_name.toLowerCase() === selectedSubject)
    : combinedData;

  const chartOptions = {
    chart: { type: 'column' },
    title: { text: `${score.subject_name || 'Selected Subject'} CBT Score` },
    credits: { enabled: false },
    xAxis: {
      categories: ['Correct', 'Incorrect', 'Unattempted'],
      title: { text: 'Question Type' }
    },
    yAxis: {
      min: 0,
      title: { text: 'Number of Questions' },
      gridLineColor: '#444'
    },
    legend: { enabled: false },
    tooltip: { pointFormat: '<b>{point.y} questions</b>' },
    series: [
      {
        name: 'Questions',
        data: [
          {
            name: 'Correct',
            y: score?.evaluation_results?.score_summary?.num_correct || 0,
            color: '#4caf50'
          },
          {
            name: 'Incorrect',
            y: score?.evaluation_results?.score_summary?.num_incorrect || 0,
            color: '#f44336'
          },
          {
            name: 'Unattempted',
            y: score?.evaluation_results?.score_summary?.num_unattempted || 0,
            color: '#9e9e9e'
          }
        ]
      }
    ],
    plotOptions: {
      column: {
        borderRadius: 5,
        dataLabels: { enabled: true, style: { color: '#ffffff' } }
      }
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Subject Selection Dropdown */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="mb-8"
      >
        <motion.div
          className="relative group"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <label
            htmlFor="subject-select"
            className="block text-lg font-medium text-[var(--color-student)] mb-2 ml-1 transition-all duration-200 

             group-hover:text-[var(--color-counselor)] group-hover:scale-100 group-hover:translate-x-1"
          >
            <span className="inline-block">Select Subject</span>
            <span className="inline-block ml-2 opacity-0 group-hover:opacity-100">→</span>
          </label>
          <div className="relative">
            <select
              id="subject-select"
              value={selectedSubject}
              onChange={(e) => setSelectedSubject(e.target.value)}
              className="w-full px-4 py-3 bg-white/90 backdrop-blur-sm border-2 border-gray-200 rounded-xl focus:outline-none focus:border-blue-400 text-gray-800 shadow-lg appearance-none transition-all duration-200 hover:shadow-md hover:border-blue-300 cursor-pointer"
            >
              <option value="" className="text-gray-400">
                All Subjects
              </option>
              <option value="maths" className="hover:bg-blue-50">
                Mathematics
              </option>
              <option value="physics">Physics</option>
              <option value="chemistry">Chemistry</option>
              <option value="biology">Biology</option>
            </select>

            <motion.div
              className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none"
              animate={{
                y: selectedSubject ? [-2, 2, -2] : 0,
                transition: selectedSubject ? { repeat: Infinity, duration: 1.5 } : {}
              }}
            >
              <ChevronDown className="h-5 w-5 text-gray-500 group-hover:text-blue-500 transition-colors duration-200" />
            </motion.div>
          </div>
        </motion.div>

        {/* Selected subject indicator */}
        {selectedSubject && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-2 flex items-center"
          >
            <div className=" rounded-full  mr-2">
              <BookA className="text-[var(--color-counselor)]" />
            </div>
            <span className="text-sm font-medium text-blue-600">
              {selectedSubject.charAt(0).toUpperCase() + selectedSubject.slice(1)} selected
            </span>
          </motion.div>
        )}
      </motion.div>

      {/* Popup for displaying chart */}
      <AnimatePresence>
        {open && (
          <PopUp
            title={score.unit_name}
            onClose={() => {
              setOpen(false);
              setScore({});
            }}
            width="lg"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              className="bg-white/80 backdrop-blur-md rounded-xl p-6 shadow-lg border border-white/20"
            >
              <HighchartsReact highcharts={Highcharts} options={chartOptions} />
            </motion.div>
          </PopUp>
        )}
      </AnimatePresence>

      {/* Toastify for error handling */}
      <Toastify res={res} resClear={() => setRes(null)} />

      {/* Table displaying test data */}
      <Table
        header={tableHeaders[selectedSubject] || MathsTableHeader}
        data={filteredData}
        title={
          selectedSubject
            ? `${selectedSubject.charAt(0).toUpperCase() + selectedSubject.slice(1)} Tests`
            : 'All Subject Tests'
        }
        onView={handleViewCbtData}
        searchBy={[
          'exam_name',
          'subject_name',
          'unit_name',
          'sub_topic',
          'start_time',
          'end_time',
          'test_number'
        ]}
        searchPlaceholder="Search by Subject, Unit Name, or Sub Topic Name"
      />
    </div>
  );
};

export default Cbt;
