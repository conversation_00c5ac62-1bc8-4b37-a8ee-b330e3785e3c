import { centerCounselorPanelApi } from '../../../../redux/api/api';

export const centreCounselorDashboardSlice = centerCounselorPanelApi.injectEndpoints({
  endpoints: (builder) => ({
    getCentreCounselorDashboard: builder.query({
      query: () => ({
        url: '/center-info',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),

      transformResponse: (response) => {
        console.log('Centre Counselor Dashboard Data:', response);

        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CenterCounselor']
    }),
    getListFaculty: builder.query({
      query: () => ({
        url: '/center-faculty',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('List Faculty Data:', response);

        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CenterCounselor']
    }),
    getListStudents: builder.query({
      query: () => ({
        url: '/center-students',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('List Students Data:', response);

        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CenterCounselor']
    }),
    getListKotaTeachers: builder.query({
      query: () => ({
        url: '/center_list-kota-teachers',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('List Kota Teachers Data:', response);

        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CenterCounselor']
    })
  })
});

export const {
  useLazyGetCentreCounselorDashboardQuery,
  useLazyGetListFacultyQuery,
  useLazyGetListStudentsQuery,
  useLazyGetListKotaTeachersQuery
} = centreCounselorDashboardSlice;
