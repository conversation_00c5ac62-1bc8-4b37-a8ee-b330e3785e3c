// src/notification/NotificationHandler.js
import React, { useEffect } from 'react';
import { requestNotificationPermission, onMessageListener } from './pushNotifications';

const NotificationHandler = () => {
  useEffect(() => {
    // Request notification permission and get FCM token
    requestNotificationPermission().then((token) => {
      if (token) {
        // Send token to your Flask backend
        fetch('https://sasthra.in/api/save-token', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ token })
        })
          .then((response) => response.json())
          .then((data) => console.log('Token saved:', data))
          .catch((error) => console.error('Error saving token:', error));
      }
    });

    // Listen for foreground notifications
    onMessageListener().then((payload) => {
      alert(`Notification: ${payload.notification.title} - ${payload.notification.body}`);
    });
  }, []);

  return null; // This component doesn't render anything
};

export default NotificationHandler;
