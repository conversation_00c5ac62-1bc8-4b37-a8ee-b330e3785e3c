'use client';

import { useState, useEffect, useRef } from 'react';
import { useStudentQueryServiceMutation } from './chat.Slice';
import Latex from 'react-latex';
import { motion, AnimatePresence } from 'framer-motion';
import botTwoGif from '../../../../assets/botTwo.gif';
import { toast } from 'react-toastify';
import Toastify from '../../../../components/PopUp/Toastify'; // Adjust the path to where your Toastify component is located

const ChatSupport = ({ isVisible = true, onClose }) => {
  const [userId] = sessionStorage.getItem('userId');
  const [sessionId] = useState('session_' + Date.now());
  const [text, setText] = useState('');
  const [file, setFile] = useState(null);
  const [messages, setMessages] = useState([]);
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [isDancing, setIsDancing] = useState(false);
  const [botClicked, setBotClicked] = useState(false);
  const [notification, setNotification] = useState(null);

  const audioChunks = useRef([]);
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);
  const [queryResult, { isLoading, error }] = useStudentQueryServiceMutation();

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    if (file) {
      handleSubmit(new Event('submit'));
    }
  }, [file]);

  useEffect(() => {
    if (messages.length > 0) {
      setIsDancing(true);
      setTimeout(() => setIsDancing(false), 2000);
    }
  }, [messages]);

  useEffect(() => {
    if (isLoading) {
      setIsDancing(true);
    } else {
      setTimeout(() => setIsDancing(false), 1000);
    }
  }, [isLoading]);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);
      setMediaRecorder(recorder);
      audioChunks.current = [];

      recorder.ondataavailable = (e) => {
        audioChunks.current.push(e.data);
      };

      recorder.onstop = () => {
        const audioBlob = new Blob(audioChunks.current, { type: 'audio/wav' });
        const audioFile = new File([audioBlob], 'recording.wav', { type: 'audio/wav' });
        setFile(audioFile);
      };

      recorder.start();
      setIsRecording(true);
      setIsDancing(true);
    } catch (err) {
      console.error('Error starting recording:', err);
      alert('Failed to access microphone. Please check permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorder) {
      mediaRecorder.stop();
      if (mediaRecorder.stream) {
        mediaRecorder.stream.getTracks().forEach((track) => track.stop());
      }
      setIsRecording(false);
      setIsDancing(false);
      setMediaRecorder(null);
    }
  };

  const handleBotClick = () => {
    setBotClicked(true);
    setIsDancing(true);
    setTimeout(() => {
      setBotClicked(false);
      setIsDancing(false);
    }, 3000);
  };

  const handleClose = () => {
    console.log('handleClose called in ChatSupport');
    setMessages([]);
    setText('');
    setFile(null);
    setIsDancing(false);
    setBotClicked(false);
    setIsRecording(false);
    if (mediaRecorder) {
      try {
        mediaRecorder.stop();
        console.log('MediaRecorder stopped');
      } catch (err) {
        console.error('Error stopping mediaRecorder:', err);
      }
      setMediaRecorder(null);
    }
    if (onClose) {
      console.log('Calling onClose to hide ChatSupport');
      onClose();
    } else {
      console.error('onClose prop is not defined');
    }
  };

  const cleanLatexText = (text) => {
    let cleaned = text
      .replace(/"(\$.*?\$|\$\$.*?\$\$)"/g, '$1')
      .replace(/"([^"]*?)"/g, (match, p1) => {
        if (p1.includes('$') || p1.includes('=') || p1.match(/^[a-zA-Z0-9\s+\-*/().]+$/)) {
          return p1.trim();
        }
        return match;
      })
      .replace(/\s+(?=\\\w+)/g, '')
      .replace(/\.(\s*)$/g, '')
      .replace(/\boxed\s*\(/g, '\\boxed{')
      // .replace(/([^}])(\s*)$/, '$1}')
      .trim();

    return cleaned;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const userId = sessionStorage.getItem('userId');
    if (!userId) {
      toast.warn('User ID not found. Please log in.');
      return;
    }
    if (!text.trim() && !file) {
      toast.warn('Please provide text, an image, or audio.');
      return;
    }

    setText(''); // Move setText here to clear input immediately on submit
    setIsDancing(true);

    const formData = new FormData();
    formData.append('user_id', userId);
    formData.append('session_id', sessionId);
    if (text.trim()) formData.append('text', text.trim());
    if (file) {
      const key = file.type.startsWith('image/') ? 'image' : 'audio';
      formData.append(key, file);
    }

    try {
      const userMessage = {
        type: 'user',
        text:
          text ||
          (file ? (file.type.startsWith('image/') ? 'Image uploaded' : 'Audio uploaded') : ''),
        timestamp: new Date().toISOString()
      };
      setMessages((prev) => [...prev, userMessage]);

      const response = await queryResult(formData).unwrap();
      const cleanedResponse = cleanLatexText(response.response);

      const aiMessage = {
        type: 'ai',
        text: cleanedResponse,
        ocr_text: response.ocr_text || null,
        transcribed_text: response.transcribed_text || null,
        image: file && file.type.startsWith('image/') ? URL.createObjectURL(file) : null,
        audio: file && !file.type.startsWith('image/') ? URL.createObjectURL(file) : null,
        timestamp: new Date().toISOString()
      };
      setMessages((prev) => [...prev, aiMessage]);
    } catch (err) {
      console.error('Query Error:', err);
      const errorMessage = {
        type: 'error',
        text: err.data?.message || err.message || 'Failed to process query',
        timestamp: new Date().toISOString()
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      setIsDancing(false);
      console.log(isRecording);
      if (isRecording && file && !file.type.startsWith('image/')) {
        stopRecording(); // Stop the microphone if recording and audio was submitted
      }
    }
  };
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success('Message copied to clipboard!');
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    });
  };

  const renderMessageText = (text) => {
    const paragraphs = text.split(/\n\n/).filter((p) => p.trim());
    return paragraphs.map((paragraph, paragraphIndex) => {
      if (paragraph.includes('\\boxed') || paragraph.includes('\\')) {
        return (
          <div key={paragraphIndex} className="mb-3 last:mb-0">
            <Latex>{paragraph.trim()}</Latex>
          </div>
        );
      }
      const sections = paragraph.split(/(Step \d+:|Final Answer:|Thus,)/g);
      return (
        <div key={paragraphIndex} className="mb-3 last:mb-0">
          {sections.map((part, index) => {
            if (part.match(/Step \d+:|Final Answer:|Thus,/)) {
              return (
                <p key={index} className="font-bold text-blue-600 mt-3 mb-2 text-base">
                  {part}
                </p>
              );
            }
            if (part.trim()) {
              return (
                <div key={index} className="latex-content-wrapper mb-2">
                  <Latex>{part.trim()}</Latex>
                </div>
              );
            }
            return null;
          })}
        </div>
      );
    });
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence mode="wait">
      <motion.div
        initial={{ opacity: 0, scale: 0.8, y: 50 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.8, y: 50 }}
        transition={{ duration: 0.5, ease: 'easeInOut' }}
        className="fixed bottom-0 right-6 z-50 flex flex-col h-[90vh] w-full max-w-md bg-white shadow-2xl rounded-3xl overflow-hidden border border-gray-100">
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        />

        <style>
          {`
            .latex-content-wrapper {
              display: block;
              max-width: 100%;
              overflow: hidden;
              line-height: 1.6;
              word-wrap: break-word;
              overflow-wrap: break-word;
            }
            
            .latex-content-wrapper .katex-display {
              margin: 0.5em 0;
              overflow-x: auto;
              overflow-y: hidden;
              max-width: 100%;
              padding: 2px 0;
            }
            
            .latex-content-wrapper .katex {
              font-size: 0.95em;
              line-height: 1.4;
              max-width: 100%;
              overflow-wrap: break-word;
            }
            
            .scrollbar-thin::-webkit-scrollbar {
              width: 3px;
            }
            .scrollbar-thin::-webkit-scrollbar-track {
              background: transparent;
            }
            .scrollbar-thin::-webkit-scrollbar-thumb {
              background: rgba(156, 163, 175, 0.5);
              border-radius: 10px;
            }
            .scrollbar-thin::-webkit-scrollbar-thumb:hover {
              background: rgba(156, 163, 175, 0.8);
            }

            @keyframes dance {
              0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
              25% { transform: translateY(-8px) rotate(-3deg) scale(1.05); }
              50% { transform: translateY(-4px) rotate(3deg) scale(1.08); }
              75% { transform: translateY(-12px) rotate(-2deg) scale(1.03); }
            }

            @keyframes bounce-dance {
              0%, 100% { transform: translateY(0px) scale(1); }
              50% { transform: translateY(-15px) scale(1.1); }
            }

            @keyframes wiggle {
              0%, 100% { transform: rotate(0deg); }
              25% { transform: rotate(-8deg); }
              75% { transform: rotate(8deg); }
            }

            @keyframes pulse-glow {
              0%, 100% { box-shadow: 0 0 25px rgba(139, 92, 246, 0.4); }
              50% { box-shadow: 0 0 45px rgba(139, 92, 246, 0.7); }
            }

            @keyframes float-particle {
              0% { transform: translate(0px, 0px) rotate(0deg); opacity: 0.6; }
              33% { transform: translate(25px, -25px) rotate(120deg); opacity: 1; }
              66% { transform: translate(-15px, -15px) rotate(240deg); opacity: 0.7; }
              100% { transform: translate(0px, 0px) rotate(360deg); opacity: 0.6; }
            }

            .dancing {
              animation: dance 1.2s ease-in-out infinite;
            }

            .bounce-dancing {
              animation: bounce-dance 0.9s ease-in-out infinite;
            }

            .wiggling {
              animation: wiggle 0.6s ease-in-out infinite;
            }

            .pulse-glowing {
              animation: pulse-glow 2.5s ease-in-out infinite;
            }

            .floating-particle {
              animation: float-particle 3.5s ease-in-out infinite;
            }

            .gradient-bg {
              background: linear-gradient(135deg, #8B5CF6 0%, #3B82F6 100%);
            }

            .glass-effect {
              backdrop-filter: blur(10px);
              background: rgba(255, 255, 255, 0.1);
            }
          `}
        </style>

        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
          className="gradient-bg text-white px-6 py-4 relative overflow-hidden">
          <motion.button
            whileHover={{ scale: 1.1, rotate: 90 }}
            whileTap={{ scale: 0.9 }}
            onClick={handleClose}
            className="absolute top-4 right-4 w-10 h-10 bg-white/20 hover:bg-red-500/80 rounded-full flex items-center justify-center transition-all duration-300 z-20 group shadow-lg">
            <motion.i
              className="fas fa-times text-white text-lg group-hover:rotate-180 transition-transform duration-300"
              whileHover={{ scale: 1.2 }}></motion.i>
            <motion.div
              className="absolute inset-0 bg-red-500/30 rounded-full"
              initial={{ scale: 0, opacity: 0 }}
              whileHover={{ scale: 1.5, opacity: 0.4 }}
              transition={{ duration: 0.3 }}></motion.div>
          </motion.button>

          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-4 right-16 w-20 h-20 border border-white rounded-full"></div>
            <div className="absolute top-8 right-20 w-12 h-12 border border-white rounded-full"></div>
            <div className="absolute bottom-4 left-4 w-16 h-16 border border-white rounded-full"></div>
          </div>

          <div className="flex justify-center mb-4">
            <motion.div
              onClick={handleBotClick}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className={`
                relative cursor-pointer p-3 bg-white rounded-full shadow-2xl
                ${isDancing ? 'dancing pulse-glowing' : 'hover:shadow-xl'}
                ${botClicked ? 'bounce-dancing' : ''}
                ${isLoading ? 'wiggling' : ''}
                transition-all duration-300
              `}>
              {isDancing && (
                <>
                  <div
                    className="absolute -top-2 -right-2 w-2 h-2 bg-yellow-300 rounded-full floating-particle"
                    style={{ animationDelay: '0s' }}></div>
                  <div
                    className="absolute -bottom-2 -left-2 w-1.5 h-1.5 bg-pink-300 rounded-full floating-particle"
                    style={{ animationDelay: '1s' }}></div>
                  <div
                    className="absolute -top-2 -left-2 w-1 h-1 bg-green-300 rounded-full floating-particle"
                    style={{ animationDelay: '2s' }}></div>
                </>
              )}
              <img
                src={botTwoGif || '/placeholder.svg'}
                alt="DoubtDesk"
                className="w-16 h-16 rounded-full"
              />
              <div
                className={`
                absolute inset-0 rounded-full border-2 transition-all duration-300
                ${isLoading ? 'border-yellow-300 animate-spin' : isDancing ? 'border-green-300 animate-pulse' : 'border-purple-300'}
              `}></div>
              {(isLoading || isDancing) && (
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full flex items-center justify-center animate-bounce">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              )}
            </motion.div>
          </div>

          <div className="text-center relative z-10">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-2xl font-bold mb-2">
              DoubtDesk
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-white/90 text-sm mb-4">
              You can ask me anything what you want
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="flex items-center justify-center gap-2 text-sm">
              <div
                className={`w-2 h-2 rounded-full animate-pulse ${isDancing ? 'bg-green-500' : 'bg-green-500'}`}></div>
              <span className="text-white/80">
                {isLoading ? 'Thinking...' : isDancing ? 'Active' : 'Online'}
              </span>
            </motion.div>
          </div>

          {botClicked && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8, y: 10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: 10 }}
              className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-white/20 glass-effect px-4 py-2 rounded-xl shadow-lg border border-white/30 whitespace-nowrap">
              <div className="text-sm font-medium text-white">Ready to help! 🎉</div>
            </motion.div>
          )}
        </motion.div>

        <div className="flex-1 overflow-y-auto scrollbar-thin bg-gray-50">
          {messages.length === 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="p-4">
              <div className="text-center mb-4">
                <div className="text-xs text-gray-500 mb-2">
                  Today,{' '}
                  {new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })}
                </div>
              </div>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="flex items-start gap-3 mb-4">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center text-white shadow-md flex-shrink-0">
                  <i className="fas fa-robot text-sm"></i>
                </div>
                <div className="bg-white p-4 rounded-2xl rounded-tl-md shadow-sm border border-gray-100 max-w-xs">
                  <p className="text-gray-800 text-sm">
                    Hi There,
                    <br />
                    How can I help you today?
                  </p>
                </div>
              </motion.div>
            </motion.div>
          )}

          <div className="px-4 pb-4 space-y-3">
            <AnimatePresence mode="popLayout">
              {messages.map((msg, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className={`flex items-start gap-3 ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                  {msg.type !== 'user' && (
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center text-white shadow-md flex-shrink-0 mt-1">
                      <i className="fas fa-robot text-sm"></i>
                    </div>
                  )}

                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    className={`max-w-xs p-4 rounded-2xl shadow-sm transition-all duration-200 relative group ${
                      msg.type === 'user'
                        ? 'bg-gradient-to-br from-purple-500 to-blue-500 text-white rounded-tr-md'
                        : msg.type === 'ai'
                          ? 'bg-white text-gray-800 border border-gray-100 rounded-tl-md'
                          : 'bg-white-50 text-gray-700 border border-gray-100 rounded-tl-md'
                    }`}>
                    <div className="space-y-2">
                      {msg.type === 'ai' && msg.image && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="mt-3">
                          <img
                            src={msg.image || '/placeholder.svg'}
                            alt="Uploaded"
                            className="max-w-full rounded-xl shadow-sm border border-gray-200"
                          />
                        </motion.div>
                      )}
                      <div className="text-content text-sm leading-relaxed">
                        {renderMessageText(msg.text)}
                      </div>

                      {/* {msg.type === 'ai' && msg.image && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="mt-3">
                          <img
                            src={msg.image || '/placeholder.svg'}
                            alt="Uploaded"
                            className="max-w-full rounded-xl shadow-sm border border-gray-200"
                          />
                        </motion.div>
                      )} */}

                      {msg.type === 'ai' && msg.audio && (
                        <motion.div
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="mt-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 shadow-sm">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                              <i className="fas fa-microphone text-white text-xs"></i>
                            </div>
                            <span className="text-xs font-medium text-blue-900">Voice Message</span>
                          </div>
                          <audio src={msg.audio} controls className="w-full h-8 rounded-lg" />
                        </motion.div>
                      )}

                      {msg.transcribed_text && (
                        <div className="mt-2 p-2 bg-blue-50 rounded-lg border border-blue-200">
                          <div className="flex items-center gap-1 mb-1">
                            <i className="fas fa-closed-captioning text-blue-600 text-xs"></i>
                            <span className="text-xs text-blue-800 font-medium">Transcription</span>
                          </div>
                          <p className="text-xs text-blue-700 italic">{msg.transcribed_text}</p>
                        </div>
                      )}

                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs opacity-60">{formatTimestamp(msg.timestamp)}</span>
                        {msg.type === 'ai' && (
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => copyToClipboard(msg.text)}
                            className="opacity-0 group-hover:opacity-100 bg-gray-100 hover:bg-gray-200 p-1 rounded-full transition-all duration-200">
                            <i className="fas fa-copy text-gray-600 text-xs"></i>
                          </motion.button>
                        )}
                      </div>
                    </div>
                  </motion.div>

                  {msg.type === 'user' && (
                    <div className="w-8 h-8 rounded-full bg-gray-400 flex items-center justify-center text-white shadow-md flex-shrink-0 mt-1">
                      <i className="fas fa-user text-sm"></i>
                    </div>
                  )}
                </motion.div>
              ))}
            </AnimatePresence>

            {isLoading && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="flex justify-start">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center text-white shadow-md mr-3">
                  <motion.i
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
                    className="fas fa-cog text-sm"></motion.i>
                </div>
                <div className="bg-white p-4 rounded-2xl rounded-tl-md shadow-sm border border-gray-100">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm text-gray-600 font-medium">
                      DoubtDesk is thinking...
                    </span>
                  </div>
                  <div className="flex space-x-1">
                    {[0, 1, 2].map((i) => (
                      <motion.div
                        key={i}
                        animate={{
                          scale: [1, 1.3, 1],
                          opacity: [0.5, 1, 0.5]
                        }}
                        transition={{
                          duration: 1,
                          repeat: Number.POSITIVE_INFINITY,
                          delay: i * 0.2
                        }}
                        className="w-2 h-2 bg-purple-500 rounded-full"
                      />
                    ))}
                  </div>
                </div>
              </motion.div>
            )}
          </div>
          <div ref={messagesEndRef} />
        </div>

        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="bg-white border-t border-gray-100 p-4">
          <form onSubmit={handleSubmit} className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="flex-1 relative">
                <input
                  type="text"
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  placeholder="Type and press [enter]"
                  className="w-full pl-4 pr-12 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-800 placeholder-gray-500 transition-all duration-200 text-sm"
                />
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                  <motion.button
                    type="button"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => fileInputRef.current?.click()}
                    className="text-gray-400 hover:text-purple-500 transition-colors duration-200">
                    <i className="fas fa-paperclip text-sm"></i>
                  </motion.button>
                  <motion.button
                    type="button"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={isRecording ? stopRecording : startRecording}
                    className={`transition-colors duration-200 ${
                      isRecording ? 'text-red-500' : 'text-gray-400 hover:text-purple-500'
                    }`}>
                    {isRecording ? (
                      <motion.i
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 0.5, repeat: Number.POSITIVE_INFINITY }}
                        className="fas fa-stop text-sm"></motion.i>
                    ) : (
                      <i className="fas fa-microphone text-sm"></i>
                    )}
                  </motion.button>
                </div>
              </div>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                type="submit"
                disabled={isLoading}
                className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 disabled:opacity-50 text-white rounded-2xl shadow-md transition-all duration-200 flex items-center justify-center">
                {isLoading ? (
                  <motion.i
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
                    className="fas fa-spinner text-sm"></motion.i>
                ) : (
                  <i className="fas fa-paper-plane text-sm"></i>
                )}
              </motion.button>
            </div>

            <input
              type="file"
              accept="image/*,audio/*"
              onChange={(e) => setFile(e.target.files[0])}
              ref={fileInputRef}
              className="hidden"
            />

            {isRecording && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center justify-center gap-2 text-red-600 text-sm">
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 0.5, repeat: Number.POSITIVE_INFINITY }}
                  className="w-2 h-2 bg-red-500 rounded-full"></motion.div>
                <span>Recording...</span>
              </motion.div>
            )}
          </form>

          {/* {error && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-3 p-3 bg-red-50 border border-red-200 rounded-xl">
              <div className="flex items-center gap-2">
                <i className="fas fa-exclamation-triangle text-red-600"></i>
                <div>
                  <p className="text-red-800 font-medium text-sm">Error Occurred</p>
                  <p className="text-red-700 text-xs">
                    {error.message} {error.details && `(${error.details})`}
                  </p>
                </div>
              </div>
            </motion.div>
          )} */}
        </motion.div>
      </motion.div>
      <Toastify res={null} resClear={() => {}} />
    </AnimatePresence>
  );
};

export default ChatSupport;
