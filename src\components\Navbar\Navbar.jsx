import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom'; // Added useNavigate
import SideBar from './SideBar';
import TopBar from './TopBar';
import ChatBotSystem from '../../pages/screens/studentPanel/chatSupport/ChatBotSystem';
import { motion } from 'framer-motion';
import { ArrowLeftSquare } from 'lucide-react';

const Navbar = ({ children }) => {
  const [isSidebarOpen, setSidebarOpen] = useState(false);
  const [showNav, setShowNav] = useState(true);
  const userRole = sessionStorage.getItem('role');
  const location = useLocation();
  const navigate = useNavigate(); // Initialize useNavigate

  const hiddenNavPaths = [
    '/sasthra/student/mock-test-simulation',
    '/sasthra/student/create-your-own-test',
    '/sasthra/student/ai-tutor',
    '/sasthra/teacher/live-streaming',
    '/sasthra/faculty/live-viewer',
    '/sasthra/student/problem-solver',
    '/sasthra/student/learn-practically',
    '/sasthra/student/student-community',
    '/sasthra/faculty/ai-tutor'
  ];

  const isHiddenNavPage = hiddenNavPaths.includes(location.pathname);

  // Reset the "Back" button visibility when navigating to these special pages
  useEffect(() => {
    if (isHiddenNavPage) {
      setShowNav(true);
    }
  }, [location.pathname, isHiddenNavPage]);

  const toggleSidebar = () => {
    setSidebarOpen((prev) => !prev);
  };

  // Close sidebar on link click only on mobile devices
  const handleLinkClick = () => {
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  };

  const isNavVisible = !isHiddenNavPage || !showNav;

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {isNavVisible && <TopBar onToggleSidebar={toggleSidebar} />}

      <div className="flex flex-grow overflow-hidden relative">
        {/* Backdrop for mobile, hidden on desktop */}
        {isSidebarOpen && (
          <div
            onClick={toggleSidebar}
            className="fixed inset-0 bg-black/60 z-30 md:hidden"
            aria-hidden="true"
          ></div>
        )}

        {/* The Sidebar itself */}
        {isNavVisible && <SideBar isOpen={isSidebarOpen} onLinkClick={handleLinkClick} />}

        {/* Main Content Area */}
        <main className={`flex-grow overflow-y-auto transition-all duration-300 ease-in-out`}>
          {isHiddenNavPage && showNav && (
            <motion.div
              className="sticky top-0 left-0 z-50 mb-4"
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ type: 'spring', stiffness: 300, damping: 20 }}
            >
              <motion.button
                onClick={() => {
                  setShowNav(false);
                  navigate('/sasthra'); // Navigate to /sasthra
                }}
                className="group relative flex items-center px-4 py-3 bg-gradient-to-r hover:cursor-pointer from-blue-50 to-indigo-50 rounded-xl shadow-sm hover:shadow-md border border-blue-100 transition-all"
                whileHover={{
                  x: 4,
                  backgroundColor: 'rgba(239, 246, 255, 1)'
                }}
                whileTap={{
                  scale: 0.98,
                  backgroundColor: 'rgba(219, 234, 254, 1)'
                }}
              >
                {/* Animated arrow with trail effect */}
                <motion.div
                  className="relative"
                  animate={{
                    x: [0, -3, 0]
                  }}
                  transition={{
                    repeat: Infinity,
                    repeatType: 'reverse',
                    duration: 1.5,
                    ease: 'easeInOut'
                  }}
                >
                  <ArrowLeftSquare
                    size={24}
                    className="text-indigo-600 group-hover:text-indigo-700 transition-colors"
                  />

                  {/* Glow effect */}
                  <motion.div
                    className="absolute inset-0 bg-indigo-400 rounded-md opacity-0 group-hover:opacity-20 blur-sm"
                    initial={{ scale: 0.8 }}
                    animate={{
                      scale: [0.8, 1.1, 0.8],
                      opacity: [0, 0.2, 0]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: 'easeInOut'
                    }}
                  />
                </motion.div>
              </motion.button>
            </motion.div>
          )}

          {children}
        </main>

        {userRole === 'student' && isNavVisible && (
          <div className="fixed bottom-4 right-4 z-20">
            <ChatBotSystem />
          </div>
        )}
      </div>
    </div>
  );
};

export default Navbar;
