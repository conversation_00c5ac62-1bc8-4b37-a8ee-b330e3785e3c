import { createSlice } from '@reduxjs/toolkit';
import { onBroadingAssessmentApi } from '../../../../redux/api/api';

const initialState = {
  onBroadingAssessmentData: null,
  onBroadingAssessmentStatus: null
};

export const onBroadingAssessmentApiSlice = onBroadingAssessmentApi.injectEndpoints({
  endpoints: (builder) => ({
    getOnBroadingAssessmentPostService: builder.mutation({
      query: (body) => ({
        url: '/start-assessment',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['OnBroadingAssessment']
    }),
    postOnBroadingAssessmentService: builder.mutation({
      query: (body) => ({
        url: '/submit-assessment',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['OnBroadingAssessment']
    }),
    checkOnBroadingAssessmentStatus: builder.mutation({
      query: (body) => ({
        url: '/check-student-assessment',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['OnBroadingAssessment']
    }),
    completeOnBroadingAssessment: builder.mutation({
      query: (body) => ({
        url: '/complete-student-assessment',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['OnBroadingAssessment']
    })
  })
});

const onBroadingAssessmentSlice = createSlice({
  name: 'onBroadingAssessment',
  initialState,
  reducers: {
    setOnBroadingAssessmentData: (state, action) => {
      state.onBroadingAssessmentData = action.payload;
    },
    setOnBroadingAssessmentStatus: (state, action) => {
      state.onBroadingAssessmentStatus = action.payload;
    }
  }
});

export const {
  useGetOnBroadingAssessmentPostServiceMutation,
  usePostOnBroadingAssessmentServiceMutation,
  useCheckOnBroadingAssessmentStatusMutation,
  useCompleteOnBroadingAssessmentMutation
} = onBroadingAssessmentApiSlice;
export const { setOnBroadingAssessmentData, setOnBroadingAssessmentStatus } =
  onBroadingAssessmentSlice.actions;
export const selectOnBroadingAssessmentData = (state) =>
  state.onBroadingAssessment.onBroadingAssessmentData;
export default onBroadingAssessmentSlice.reducer;
