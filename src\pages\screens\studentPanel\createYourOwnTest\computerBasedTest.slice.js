// import {
//   createOwnTestBioApi,
//   createOwnTestChemistryApi,
//   createOwnTestMathApi,
//   createOwnTestPhysicsApi
// } from '../../../../redux/api/api';

// export const createOwnTestBioApiSlice = createOwnTestBioApi.injectEndpoints({
//   endpoints: (builder) => ({
//     createYourOwnTestBioStartTest: builder.mutation({
//       query: (body) => ({
//         url: '/start-test',
//         method: 'POST',
//         body,
//         responseHandler: async (res) => res.json()
//       }),
//       transformResponse: (response) => {
//         console.log('Create Your Own Test Bio Response:', response);
//         return response; // Ensure response has the expected structure
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       providesTags: ['CreateOwnTestBio']
//     }),
//     createYourOwnTestBioSubmitTest: builder.mutation({
//       query: (body) => ({
//         url: '/evaluate-test',
//         method: 'POST',
//         body,
//         responseHandler: async (res) => res.json()
//       }),
//       transformResponse: (response) => {
//         console.log('Create Your Own Test Bio Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       providesTags: ['CreateOwnTestBio']
//     })
//   })
// });
// export const {
//   useCreateYourOwnTestBioStartTestMutation,
//   useCreateYourOwnTestBioSubmitTestMutation
// } = createOwnTestBioApiSlice;

// export const createOwnTestMathApiSlice = createOwnTestMathApi.injectEndpoints({
//   endpoints: (builder) => ({
//     createYourOwnTestMathStartTest: builder.mutation({
//       query: (body) => ({
//         url: '/start-test-maths',
//         method: 'POST',
//         body,
//         responseHandler: async (res) => res.json()
//       }),
//       transformResponse: (response) => {
//         console.log('Create Your Own Test Math Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       providesTags: ['CreateOwnTestMath']
//     }),
//     createYourOwnTestMathSubmitTest: builder.mutation({
//       query: (body) => ({
//         url: '/evaluate-test-maths',
//         method: 'POST',
//         body,
//         responseHandler: async (res) => res.json()
//       }),
//       transformResponse: (response) => {
//         console.log('Create Your Own Test Math Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       providesTags: ['CreateOwnTestMath']
//     })
//   })
// });
// export const {
//   useCreateYourOwnTestMathStartTestMutation,
//   useCreateYourOwnTestMathSubmitTestMutation
// } = createOwnTestMathApiSlice;

// export const createOwnTestPhysicsApiSlice = createOwnTestPhysicsApi.injectEndpoints({
//   endpoints: (builder) => ({
//     createYourOwnTestPhysicsStartTest: builder.mutation({
//       query: (body) => ({
//         url: '/start-test-physics',
//         method: 'POST',
//         body,
//         responseHandler: async (res) => res.json()
//       }),
//       transformResponse: (response) => {
//         console.log('Create Your Own Test Physics Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       providesTags: ['CreateOwnTestPhysics']
//     }),
//     createYourOwnTestPhysicsSubmitTest: builder.mutation({
//       query: (body) => ({
//         url: '/evaluate-test-physics',
//         method: 'POST',
//         body,
//         responseHandler: async (res) => res.json()
//       }),
//       transformResponse: (response) => {
//         console.log('Create Your Own Test Physics Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       providesTags: ['CreateOwnTestPhysics']
//     })
//   })
// });
// export const {
//   useCreateYourOwnTestPhysicsStartTestMutation,
//   useCreateYourOwnTestPhysicsSubmitTestMutation
// } = createOwnTestPhysicsApiSlice;

// export const createOwnTestChemistryApiSlice = createOwnTestChemistryApi.injectEndpoints({
//   endpoints: (builder) => ({
//     createYourOwnTestChemistryStartTest: builder.mutation({
//       query: (body) => ({
//         url: '/start-test-chemistry',
//         method: 'POST',
//         body,
//         responseHandler: async (res) => res.json()
//       }),
//       transformResponse: (response) => {
//         console.log('Create Your Own Test Chemistry Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       providesTags: ['CreateOwnTestChemistry']
//     }),
//     createYourOwnTestChemistrySubmitTest: builder.mutation({
//       query: (body) => ({
//         url: '/evaluate-test-chemistry',
//         method: 'POST',
//         body,
//         responseHandler: async (res) => res.json()
//       }),
//       transformResponse: (response) => {
//         console.log('Create Your Own Test Chemistry Response:', response);
//         return response;
//       },
//       transformErrorResponse: ({ originalStatus, status, data }) => ({
//         status: originalStatus ?? status,
//         data
//       }),
//       providesTags: ['CreateOwnTestChemistry']
//     })
//   })
// });
// export const {
//   useCreateYourOwnTestChemistryStartTestMutation,
//   useCreateYourOwnTestChemistrySubmitTestMutation
// } = createOwnTestChemistryApiSlice;
