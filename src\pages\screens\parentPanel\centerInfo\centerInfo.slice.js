// import {parentApi} from '../../../../redux/api/api';
// import { createSlice } from '@reduxjs/toolkit';

//  const initialState ={
//   studentInfoData:null,
//  }

//  export const centerDetailsSlice = parentApi.injectEndpoints({
//   endpoints:(builder)=>({
//     centerDetailsService : builder.query({
//       query:(body)=>({
//         url:'/parent-dashboard',
//         method:'GET',
//         body,
//         responseHandler:async(res)=> res.json()
//       }),
//       transformResponse:(responce)=>{
//         console.log('Center Info:',responce);
//         return responce;
//       },
//       transformErrorResponse:({ originalStatus,status,data})=>({
//         status:originalStatus?? status,
//         data
//       }),
//       providesTags:['CenterInfo']
//     }),

//   })
//  });

//  const CenterInfoSlice =createSlice({
//   name:'CenterDetails',
//   initialState,
//   reducers:{
//     setcenterDetailsData:(state,action)=>{
//       state.centerInfoData = action.payload
//     }
//   }
//  });

//  export const { setcenterDetailsData}= CenterInfoSlice.actions

//  export default  CenterInfoSlice.reducer;

//  export const { useLazyCenterDetailsServiceQuery} = centerDetailsSlice;
