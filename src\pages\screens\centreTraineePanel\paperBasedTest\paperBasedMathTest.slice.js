import { PaperBasedMathsTestApi } from '../../../../redux/api/api';

export const PaperBasedMathTestApiSlice = PaperBasedMathsTestApi.injectEndpoints({
  endpoints: (builder) => ({
    paperBasedMathTestStartTest: builder.mutation({
      query: (body) => ({
        url: '/generate-math-paper',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Paper Based Math Test Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['PaperBasedMathTest']
    })
  })
});

export const { usePaperBasedMathTestStartTestMutation } = PaperBasedMathTestApiSlice;
