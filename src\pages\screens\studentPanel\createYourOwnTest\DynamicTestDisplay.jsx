import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTestStore } from './testStore';
import {
  FileText,
  X,
  Check,
  AlertTriangle,
  Award,
  ArrowRight,
  Clock,
  ChevronDown,
  Circle,
  Lightbulb,
  Star,
  TrendingUp,
  List,
  CheckCircle,
  XCircle,
  MinusCircle
} from 'lucide-react';
import { useCreateYourOwnTestBioSubmitTestMutation } from './bioTest.slice';
import { useCreateYourOwnTestChemistrySubmitTestMutation } from './chemistry.Slice';
import { useCreateYourOwnTestMathSubmitTestMutation } from './maths.Slice';
import { useCreateYourOwnTestPhysicsSubmitTestMutation } from './physics.Slice';
import { useNavigate } from 'react-router';
import Toastify from '../../../../components/PopUp/Toastify';

const DynamicTestDisplay = () => {
  const testData = useTestStore((state) => state.testData);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [res, setRes] = useState(null);
  const [userAnswers, setUserAnswers] = useState({});
  const testDuration = useTestStore((state) => state.testDuration);
  const [timeLeft, setTimeLeft] = useState(
    testDuration
      ? (() => {
          if (typeof testDuration === 'string') {
            if (testDuration.includes('hour')) {
              const hours = parseFloat(testDuration);
              return Math.round(hours * 60 * 60);
            }
            if (testDuration.includes('minute')) {
              const minutes = parseFloat(testDuration);
              return Math.round(minutes * 60);
            }
          }
          if (typeof testDuration === 'number') return testDuration;
          return 60 * 60; // Default to 1 hour
        })()
      : 60 * 60
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);
  const [feedbackData, setFeedbackData] = useState(null);
  const navigate = useNavigate();

  const [submitBioTest] = useCreateYourOwnTestBioSubmitTestMutation();
  const [submitChemistryTest] = useCreateYourOwnTestChemistrySubmitTestMutation();
  const [submitMathTest] = useCreateYourOwnTestMathSubmitTestMutation();
  const [submitPhysicsTest] = useCreateYourOwnTestPhysicsSubmitTestMutation();

  const q = testData && testData.questions[currentIndex];
  const selectedOption = q ? userAnswers[q.id] || null : null;

  useEffect(() => {
    if (timeLeft <= 0) return;
    const timer = setInterval(() => setTimeLeft((prev) => prev - 1), 1000);
    return () => clearInterval(timer);
  }, [timeLeft]);

  useEffect(() => {
    if (timeLeft <= 0 && !isSubmitting && !showFeedback) handleSubmitTest();
  }, [timeLeft]);

  if (!testData) return <div style={{ color: 'red' }}>No test data found</div>;
  if (!Array.isArray(testData.questions) || testData.questions.length === 0)
    return <div style={{ color: 'red' }}>No questions found</div>;

  const minutes = Math.floor(timeLeft / 60);
  const seconds = timeLeft % 60;

  const renderOptions = (options) => {
    if (Array.isArray(options)) {
      return options.map((opt, i) => (
        <motion.li
          key={i}
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: i * 0.1, type: 'spring', stiffness: 120 }}
          className="mb-3"
        >
          <label
            className={`flex items-center p-4 rounded-xl cursor-pointer transition-all duration-300 border-2 ${
              selectedOption === opt
                ? 'border-indigo-500 bg-indigo-50 shadow-md'
                : 'border-gray-200 hover:bg-gray-50 hover:shadow-sm'
            }`}
          >
            <input
              type="radio"
              name={`question-${q.id || currentIndex}`}
              value={opt}
              checked={selectedOption === opt}
              onChange={() => handleOptionChange(opt)}
              className="h-5 w-5 text-indigo-600 focus:ring-indigo-500"
            />
            <span className="ml-3 text-gray-800 text-lg">{opt}</span>
          </label>
        </motion.li>
      ));
    } else if (options && typeof options === 'object') {
      return Object.entries(options).map(([key, value], i) => (
        <motion.li
          key={key}
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: i * 0.1, type: 'spring', stiffness: 120 }}
          className="mb-3"
        >
          <label
            className={`flex items-start p-4 rounded-xl cursor-pointer transition-all duration-300 border-2 ${
              selectedOption === key
                ? 'border-indigo-500 bg-indigo-50 shadow-md'
                : 'border-gray-200 hover:bg-gray-50 hover:shadow-sm'
            }`}
          >
            <input
              type="radio"
              name={`question-${q.id || currentIndex}`}
              value={key}
              checked={selectedOption === key}
              onChange={() => handleOptionChange(key)}
              className="mt-1 h-5 w-5 text-indigo-600 focus:ring-indigo-500"
            />
            <div className="ml-3">
              <span className="font-semibold text-gray-800">{key}.</span>
              <span className="ml-2 text-gray-800 text-lg">{value}</span>
            </div>
          </label>
        </motion.li>
      ));
    } else {
      return (
        <motion.li
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-red-500 p-4 text-lg"
        >
          No options available
        </motion.li>
      );
    }
  };

  const handleOptionChange = (option) => {
    setUserAnswers((prev) => ({ ...prev, [q.id]: option }));
  };

  const handleNext = () => setCurrentIndex((i) => i + 1);
  const handlePrevious = () => setCurrentIndex((i) => i - 1);

  const handleSubmitTest = async () => {
    setIsSubmitting(true);
    try {
      const formattedAnswers = Object.entries(userAnswers).map(([questionId, selectedOption]) => ({
        question_id: questionId,
        selected_option: selectedOption
      }));

      const submissionPayload = {
        exam_id: testData.exam_id,
        user_id: testData.user_id,
        answers: formattedAnswers
      };

      const subject = (testData.subject || '').trim().toLowerCase();

      if (!subject) {
        console.error(
          'Subject is undefined or empty. testData:',
          JSON.stringify(testData, null, 2)
        );
        console('Error: Test subject is not defined.');
        return;
      }

      let response;
      if (['physics'].includes(subject))
        response = await submitPhysicsTest(submissionPayload).unwrap();
      else if (['chemistry'].includes(subject))
        response = await submitChemistryTest(submissionPayload).unwrap();
      else if (['mathematics', 'math', 'maths'].includes(subject))
        response = await submitMathTest(submissionPayload).unwrap();
      else if (['biology', 'bio'].includes(subject))
        response = await submitBioTest(submissionPayload).unwrap();
      else {
        console('Error: Unknown subject: ' + subject);
        return;
      }

      console.log('Test submission response:', JSON.stringify(response, null, 2));

      setRes(response);

      if (
        response &&
        (response.detailed_question_feedback || response.ai_feedback?.detailed_question_feedback)
      ) {
        setFeedbackData(response);
        setShowFeedback(true);
      } else {
        console.error('Invalid feedback data:', response);
      }
    } catch (error) {
      console.error('Test submission failed:', error);
      setRes(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleclose = () => {
    setShowFeedback(false);
    navigate('/sasthra');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-10 px-4 sm:px-6 lg:px-8">
      <Toastify res={res} resClear={() => setRes(null)} />
      <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-4 gap-8">
        <div className="lg:col-span-3 space-y-8">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ type: 'spring', stiffness: 120 }}
            className="bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-2xl shadow-lg p-6"
          >
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h1 className="text-2xl font-bold">{testData.exam_name}</h1>
                <div className="flex items-center mt-2 space-x-4 text-indigo-100">
                  <p>
                    Question <span className="font-bold">{currentIndex + 1}</span> of{' '}
                    {testData.num_questions}
                  </p>
                  <span className="text-indigo-200">|</span>
                </div>
              </div>
              <div className="flex items-center gap-2 bg-white/10 px-4 py-2 rounded-lg">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span className="font-mono">
                  {minutes.toString().padStart(2, '0')}:{seconds.toString().padStart(2, '0')}
                </span>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm text-indigo-100 mb-1">
                <span>Progress</span>
                <span>{Math.round(((currentIndex + 1) / testData.num_questions) * 100)}%</span>
              </div>
              <div className="h-2 bg-indigo-700/30 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-white"
                  initial={{ width: 0 }}
                  animate={{ width: `${((currentIndex + 1) / testData.num_questions) * 100}%` }}
                  transition={{ duration: 0.5, ease: 'easeOut' }}
                />
              </div>
            </div>
          </motion.div>

          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4, type: 'spring', stiffness: 100 }}
            className="bg-white rounded-2xl shadow-xl p-6 hover:shadow-2xl transition-shadow duration-300"
          >
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 bg-indigo-100 text-indigo-800 font-bold rounded-full h-12 w-12 flex items-center justify-center text-xl">
                {currentIndex + 1}
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-900">{q.question_text}</h3>
              </div>
            </div>

            {q.image_url && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2 }}
                className="mt-6"
              >
                <img
                  src={q.image_url}
                  alt="Question diagram"
                  className="max-w-full h-auto max-h-64 rounded-lg shadow-sm mx-auto"
                />
              </motion.div>
            )}

            <ul className="mt-6 space-y-2">{renderOptions(q.options)}</ul>
          </motion.div>

          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handlePrevious}
              disabled={currentIndex === 0}
              className={`px-6 py-3 rounded-xl font-semibold transition-colors ${
                currentIndex === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-indigo-100 text-indigo-600 hover:bg-indigo-200 hover:cursor-pointer'
              }`}
            >
              <div className="flex items-center gap-2">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
                Previous
              </div>
            </motion.button>

            {currentIndex === testData.questions.length - 1 ? (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleSubmitTest}
                disabled={isSubmitting}
                className="px-6 py-3 bg-green-600 hover:cursor-pointer text-white rounded-xl font-semibold hover:bg-green-700 transition-colors flex items-center gap-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                Submit Test
              </motion.button>
            ) : (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleNext}
                disabled={currentIndex === testData.questions.length - 1}
                className={`px-6 py-3 rounded-xl font-semibold transition-colors ${
                  currentIndex === testData.questions.length - 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-indigo-600 text-white hover:bg-indigo-700 hover:cursor-pointer'
                }`}
              >
                <div className="flex items-center gap-2">
                  Next
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </div>
              </motion.button>
            )}
          </div>
        </div>

        <div className="lg:col-span-1 bg-white rounded-2xl shadow-xl p-6 h-full overflow-y-auto">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Question Navigation</h3>
          <div className="grid grid-cols-3 sm:grid-cols-4 gap-2">
            {testData.questions.map((_, index) => {
              const isAnswered = !!userAnswers[testData.questions[index].id];
              const isCurrent = index === currentIndex;
              return (
                <motion.button
                  key={index}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => setCurrentIndex(index)}
                  className={`h-12 w-12 rounded-full hover:cursor-pointer flex items-center justify-center outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-lg font-semibold transition-colors ${
                    isCurrent
                      ? 'bg-indigo-600 text-white'
                      : isAnswered
                        ? 'bg-green-100 text-green-700'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  aria-label={`Go to question ${index + 1}, ${isAnswered ? 'answered' : 'unanswered'}`}
                >
                  {index + 1}
                </motion.button>
              );
            })}
          </div>
        </div>

        <AnimatePresence>
          {isSubmitting && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.4 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4 overflow-hidden"
              style={{
                background: 'radial-gradient(ellipse at 60% 40%, #2563eb 0%, #0a192f 100%)'
              }}
            >
              {[...Array(18)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute rounded-full"
                  style={{
                    width: `${Math.random() * 32 + 16}px`,
                    height: `${Math.random() * 32 + 16}px`,
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    background: 'rgba(255,255,255,0.08)',
                    filter: 'blur(2px)'
                  }}
                  animate={{
                    y: [0, Math.random() * 40 - 20, 0],
                    x: [0, Math.random() * 40 - 20, 0],
                    opacity: [0.7, 1, 0.7]
                  }}
                  transition={{ duration: 3 + Math.random() * 2, repeat: Infinity, delay: i * 0.2 }}
                />
              ))}
              <motion.div
                className="relative z-10 bg-white/10 backdrop-blur-2xl border border-blue-200/30 rounded-3xl shadow-2xl flex flex-col items-center px-12 py-10"
                initial={{ scale: 0.95, y: 30 }}
                animate={{ scale: 1, y: 0 }}
                transition={{ type: 'spring', stiffness: 200 }}
                style={{ boxShadow: '0 8px 40px 0 #2563eb44, 0 1.5px 8px 0 #0002' }}
              >
                <motion.div
                  className="mb-8"
                  animate={{ rotate: 360 }}
                  transition={{ repeat: Infinity, duration: 8, ease: 'linear' }}
                >
                  <svg width="100" height="100" viewBox="0 0 100 100">
                    <circle cx="50" cy="50" r="18" fill="#2563eb" fillOpacity="0.15" />
                    <ellipse
                      cx="50"
                      cy="50"
                      rx="32"
                      ry="18"
                      fill="none"
                      stroke="#2563eb"
                      strokeWidth="2"
                      opacity="0.5"
                    />
                    <ellipse
                      cx="50"
                      cy="50"
                      rx="18"
                      ry="32"
                      fill="none"
                      stroke="#fff"
                      strokeWidth="1.5"
                      opacity="0.3"
                    />
                    <motion.circle
                      cx="82"
                      cy="50"
                      r="4"
                      fill="#fff"
                      animate={{ cy: [50, 32, 50, 68, 50] }}
                      transition={{ repeat: Infinity, duration: 2, ease: 'easeInOut' }}
                    />
                    <motion.circle
                      cx="50"
                      cy="18"
                      r="3"
                      fill="#2563eb"
                      animate={{ cx: [50, 68, 50, 32, 50] }}
                      transition={{
                        repeat: Infinity,
                        duration: 2.5,
                        ease: 'easeInOut',
                        delay: 0.5
                      }}
                    />
                    <motion.circle
                      cx="18"
                      cy="50"
                      r="2.5"
                      fill="#fff"
                      animate={{ cy: [50, 68, 50, 32, 50] }}
                      transition={{ repeat: Infinity, duration: 2.2, ease: 'easeInOut', delay: 1 }}
                    />
                  </svg>
                </motion.div>
                <h2 className="text-2xl font-bold text-white mb-2 drop-shadow-lg">
                  Feedback Generating
                </h2>
                <p className="text-blue-100 mb-6 text-center max-w-xs">
                  Evaluating your answers with generating Feedback. Please wait...
                </p>
                <div className="w-64 h-4 bg-blue-900/40 rounded-full overflow-hidden mb-8 relative">
                  <motion.div
                    className="absolute left-0 top-0 h-full bg-gradient-to-r from-blue-400 via-blue-300 to-white"
                    initial={{ width: 0 }}
                    animate={{ width: '100%' }}
                    transition={{ duration: 2.5, repeat: Infinity, ease: 'easeInOut' }}
                  />
                  <svg className="absolute left-0 top-0 w-full h-full" viewBox="0 0 256 16">
                    <polyline
                      points="0,8 16,4 32,12 48,6 64,10 80,4 96,12 112,6 128,10 144,4 160,12 176,6 192,10 208,4 224,12 240,6 256,8"
                      fill="none"
                      stroke="#fff"
                      strokeWidth="2"
                      opacity="0.5"
                    />
                  </svg>
                </div>
                <div className="flex gap-6 mt-2">
                  {[
                    { name: 'Accuracy', icon: '✓', color: 'text-green-400' },
                    { name: 'Speed', icon: '⚡', color: 'text-yellow-300' },
                    { name: 'Depth', icon: '🔍', color: 'text-blue-200' }
                  ].map((metric, i) => (
                    <div
                      key={metric.name}
                      className="backdrop-blur-md bg-white/10 border border-blue-200/20 rounded-xl px-5 py-3 flex flex-col items-center shadow"
                    >
                      <span className={`text-2xl mb-1 ${metric.color}`}>{metric.icon}</span>
                      <span className="text-xs text-white/80">{metric.name}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        <AnimatePresence>
          {showFeedback && feedbackData && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-lg"
              onClick={() => setShowFeedback(false)}
            >
              {/* Main Container */}
              <motion.div
                className="relative bg-gradient-to-br from-white to-gray-50 rounded-3xl shadow-[0_10px_40px_-15px_rgba(0,0,0,0.3)] w-full max-w-4xl max-h-[90vh] overflow-hidden border border-gray-200/50"
                initial={{ scale: 0.96, y: 20, rotateX: 2 }}
                animate={{ scale: 1, y: 0, rotateX: 0 }}
                exit={{ scale: 0.96, y: 20 }}
                transition={{ type: 'spring', stiffness: 300, damping: 25 }}
                onClick={(e) => e.stopPropagation()}
              >
                {/* Floating Particles */}
                <div className="absolute inset-0 overflow-hidden pointer-events-none">
                  {[...Array(30)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute rounded-full bg-indigo-500/10"
                      style={{
                        width: `${Math.random() * 12 + 4}px`,
                        height: `${Math.random() * 12 + 4}px`,
                        left: `${Math.random() * 100}%`,
                        top: `${Math.random() * 100}%`
                      }}
                      animate={{
                        x: [0, Math.random() * 40 - 20],
                        y: [0, Math.random() * 40 - 20],
                        opacity: [0.2, 0.6, 0.2]
                      }}
                      transition={{
                        duration: 5 + Math.random() * 10,
                        repeat: Infinity,
                        repeatType: 'reverse'
                      }}
                    />
                  ))}
                </div>

                {/* Header Section */}
                <div className="relative px-6 py-4 bg-gradient-to-r from-indigo-600 to-blue-600 text-white">
                  {/* Close Button */}
                  <motion.button
                    onClick={handleclose}
                    className="absolute top-4 right-4 p-2 rounded-full hover:bg-white/20 transition-colors z-10"
                    whileHover={{ scale: 1.1, rotate: 90 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <X size={24} className="hover:cursor-pointer" />
                  </motion.button>

                  {/* Header Content */}
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="flex flex-col gap-4"
                  >
                    <div className="flex items-center gap-3">
                      <motion.div
                        className="p-2 bg-white/20 rounded-lg"
                        animate={{ rotateY: [0, 10, -10, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        <FileText size={28} />
                      </motion.div>
                      <h2 className="text-2xl  font-bold">{testData.exam_name} Results</h2>
                    </div>

                    {/* Score Cards */}
                    <motion.div
                      className="grid grid-cols-3 gap-3"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.3 }}
                    >
                      {/* Correct Card */}
                      <motion.div
                        className="bg-white/10 p-3 rounded-xl backdrop-blur-sm border border-white/20"
                        whileHover={{ y: -3 }}
                      >
                        <div className="flex items-center justify-center gap-2">
                          <CheckCircle className="text-green-300" size={20} />
                          <span className="font-medium">Correct</span>
                        </div>
                        <p className="text-3xl font-bold text-center mt-2">
                          {feedbackData.score_summary.num_correct}
                        </p>
                      </motion.div>

                      {/* Incorrect Card */}
                      <motion.div
                        className="bg-white/10 p-3 rounded-xl backdrop-blur-sm border border-white/20"
                        whileHover={{ y: -3 }}
                      >
                        <div className="flex items-center justify-center gap-2">
                          <XCircle className="text-red-300" size={20} />
                          <span className="font-medium">Incorrect</span>
                        </div>
                        <p className="text-3xl font-bold text-center mt-2">
                          {feedbackData.score_summary.num_incorrect}
                        </p>
                      </motion.div>

                      {/* Unattempted Card */}
                      <motion.div
                        className="bg-white/10 p-3 rounded-xl backdrop-blur-sm border border-white/20"
                        whileHover={{ y: -3 }}
                      >
                        <div className="flex items-center justify-center gap-2">
                          <MinusCircle className="text-amber-300" size={20} />
                          <span className="font-medium">Unattempted</span>
                        </div>
                        <p className="text-3xl font-bold text-center mt-2">
                          {feedbackData.score_summary.num_unattempted}
                        </p>
                      </motion.div>
                    </motion.div>

                    {/* Progress Bar */}
                    <motion.div
                      className="mt-0 h-2 bg-white/20 rounded-full overflow-hidden"
                      initial={{ width: 0 }}
                      animate={{ width: '100%' }}
                      transition={{ duration: 0.5 }}
                    >
                      <motion.div
                        className="h-full bg-green-400"
                        initial={{ width: 0 }}
                        animate={{
                          width: `${(feedbackData.score_summary.score / feedbackData.score_summary.total_questions) * 100}%`
                        }}
                        transition={{ duration: 1, ease: 'easeOut' }}
                      />
                    </motion.div>
                  </motion.div>
                </div>

                {/* Content Section */}
                <div className="overflow-y-auto max-h-[35vh] p-6">
                  {/* Performance Overview */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="mb-8"
                  >
                    <div className="flex items-center gap-3 mb-4">
                      <motion.div
                        className="p-2 bg-indigo-100 rounded-lg"
                        animate={{ rotate: [0, 10, -10, 0] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <Star className="text-indigo-600" size={20} />
                      </motion.div>
                      <h3 className="text-xl font-semibold">Performance Overview</h3>
                    </div>
                    <div className="bg-indigo-50/50 p-4 rounded-xl border border-indigo-100">
                      <p className="text-gray-700">{feedbackData.overall_assessment}</p>
                      <motion.p
                        className="mt-3 text-indigo-600 font-medium italic"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.6 }}
                      >
                        {feedbackData.motivational_closing}
                      </motion.p>
                    </div>
                  </motion.div>

                  {/* Question Breakdown */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="mb-8"
                  >
                    <div className="flex items-center gap-3 mb-4">
                      <motion.div
                        className="p-2 bg-blue-100 rounded-lg"
                        animate={{ scale: [1, 1.05, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <List className="text-blue-600" size={20} />
                      </motion.div>
                      <h3 className="text-xl font-semibold">Question Analysis</h3>
                    </div>

                    {/* Question Tabs */}
                    <div className="flex gap-2 mb-4">
                      <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium">
                        All Questions
                      </button>
                    </div>

                    {/* Questions List */}
                    <div className="space-y-3">
                      {feedbackData.detailed_question_feedback?.map((feedback, i) => (
                        <motion.div
                          key={feedback.question_id}
                          className={`p-4 rounded-xl border transition-all ${
                            feedback.validation
                              ? 'border-green-200 bg-green-50/50 hover:shadow-green-100'
                              : feedback.user_answer
                                ? 'border-red-200 bg-red-50/50 hover:shadow-red-100'
                                : 'border-amber-200 bg-amber-50/50 hover:shadow-amber-100'
                          } hover:shadow-md`}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 * i }}
                          whileHover={{ y: -3 }}
                        >
                          <div className="flex items-start gap-4">
                            <div
                              className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                                feedback.validation
                                  ? 'bg-green-100 text-green-600'
                                  : feedback.user_answer
                                    ? 'bg-red-100 text-red-600'
                                    : 'bg-amber-100 text-amber-600'
                              }`}
                            >
                              {feedback.validation ? (
                                <CheckCircle size={20} />
                              ) : feedback.user_answer ? (
                                <XCircle size={20} />
                              ) : (
                                <MinusCircle size={20} />
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="flex justify-between items-start">
                                <h4 className="font-semibold text-gray-800">Question {i + 1}</h4>
                                <span
                                  className={`text-xs px-2 py-1 rounded-full ${
                                    feedback.validation
                                      ? 'bg-green-100 text-green-800'
                                      : feedback.user_answer
                                        ? 'bg-red-100 text-red-800'
                                        : 'bg-amber-100 text-amber-800'
                                  }`}
                                >
                                  {feedback.validation
                                    ? 'Correct'
                                    : feedback.user_answer
                                      ? 'Incorrect'
                                      : 'Unattempted'}
                                </span>
                              </div>
                              <p className="text-sm text-gray-600 mt-1">
                                {feedback.feedback_on_answer}
                              </p>
                              <div className="grid grid-cols-2 gap-3 mt-3 text-sm">
                                <div className="bg-white p-2 rounded-lg border border-gray-200">
                                  <div className="text-gray-500 font-medium">Your Answer</div>
                                  <div
                                    className={`mt-1 font-medium ${
                                      feedback.validation ? 'text-green-600' : 'text-red-600'
                                    }`}
                                  >
                                    {feedback.user_answer || 'Not attempted'}
                                  </div>
                                </div>
                                <div className="bg-white p-2 rounded-lg border border-gray-200">
                                  <div className="text-gray-500 font-medium">Correct Answer</div>
                                  <div className="mt-1 font-medium text-green-600">
                                    {feedback.correct_answer}
                                  </div>
                                </div>
                              </div>
                              {feedback.reinforce_concept_from_solution && (
                                <div className="mt-3 flex items-start gap-2 text-sm text-blue-600 bg-blue-50/50 p-3 rounded-lg border border-blue-100">
                                  <Lightbulb size={16} className="flex-shrink-0 mt-0.5" />
                                  <span>{feedback.reinforce_concept_from_solution}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>

                  {/* Strengths & Improvements */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    {/* Strengths */}
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6 }}
                    >
                      <div className="flex items-center gap-3 mb-3">
                        <motion.div
                          className="p-2 bg-green-100 rounded-lg"
                          animate={{ rotate: [0, 5, -5, 0] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          <Award className="text-green-600" size={20} />
                        </motion.div>
                        <h3 className="text-xl font-semibold">Your Strengths</h3>
                      </div>
                      <div className="space-y-2">
                        {feedbackData.topic_strengths?.map((strength, i) => (
                          <motion.div
                            key={i}
                            className="flex items-start gap-3 p-3 bg-green-50/50 rounded-lg border border-green-100"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.1 * i }}
                            whileHover={{ x: 5 }}
                          >
                            <Check className="text-green-500 mt-0.5 flex-shrink-0" size={16} />
                            <span className="text-sm text-gray-700">{strength}</span>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>

                    {/* Improvements */}
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.7 }}
                    >
                      <div className="flex items-center gap-3 mb-3">
                        <motion.div
                          className="p-2 bg-amber-100 rounded-lg"
                          animate={{ scale: [1, 1.05, 1] }}
                          transition={{ duration: 1.5, repeat: Infinity }}
                        >
                          <TrendingUp className="text-amber-600" size={20} />
                        </motion.div>
                        <h3 className="text-xl font-semibold">Areas to Improve</h3>
                      </div>
                      <div className="space-y-2">
                        {feedbackData.ai_feedback?.areas_for_improvement?.map((item, i) => (
                          <motion.div
                            key={i}
                            className="flex items-start gap-3 p-3 bg-amber-50/50 rounded-lg border border-amber-100"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.1 * i }}
                            whileHover={{ x: 5 }}
                          >
                            <AlertTriangle
                              className="text-amber-500 mt-0.5 flex-shrink-0"
                              size={16}
                            />
                            <span className="text-sm text-gray-700">{item}</span>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>
                  </div>

                  {/* Study Tips */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                  >
                    <div className="flex items-center gap-3 mb-3">
                      <motion.div
                        className="p-2 bg-blue-100 rounded-lg"
                        animate={{ y: [0, -3, 0] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <Lightbulb className="text-blue-600" size={20} />
                      </motion.div>
                      <h3 className="text-xl font-semibold">Study Tips</h3>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {feedbackData.general_study_tips?.map((tip, i) => (
                        <motion.div
                          key={i}
                          className="p-3 bg-blue-50/50 rounded-lg border border-blue-100"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: 0.1 * i }}
                          whileHover={{ y: -3 }}
                        >
                          <div className="flex items-start gap-2">
                            <Circle className="text-blue-400 mt-0.5 flex-shrink-0" size={16} />
                            <span className="text-sm text-gray-700">{tip}</span>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                </div>

                {/* Footer */}
                <div className="p-6 bg-gradient-to-r from-indigo-50 to-blue-50 border-t border-gray-200/50">
                  <motion.button
                    onClick={handleclose}
                    className="w-full py-3 px-6 bg-gradient-to-r hover:cursor-pointer from-indigo-600 to-blue-600 text-white rounded-xl font-semibold flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all"
                    whileHover={{ y: -2, scale: 1.01 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Continue Learning
                    <motion.span
                      animate={{ x: [0, 4, 0] }}
                      transition={{ repeat: Infinity, duration: 1.5 }}
                    >
                      <ArrowRight size={20} />
                    </motion.span>
                  </motion.button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default DynamicTestDisplay;
