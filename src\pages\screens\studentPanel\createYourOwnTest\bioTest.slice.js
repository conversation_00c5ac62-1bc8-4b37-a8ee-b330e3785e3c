import { createOwnTestBioApi } from '../../../../redux/api/api';

export const createOwnTestBioApiSlice = createOwnTestBioApi.injectEndpoints({
  endpoints: (builder) => ({
    createYourOwnTestBioStartTest: builder.mutation({
      query: (body) => ({
        url: '/start-test',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Create Your Own Test Bio Response:', response);
        return response; // Ensure response has the expected structure
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CreateOwnTestBio']
    }),
    createYourOwnTestBioSubmitTest: builder.mutation({
      query: (body) => ({
        url: '/evaluate-test',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Create Your Own Test Bio Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CreateOwnTestBio']
    })
  })
});
export const {
  useCreateYourOwnTestBioStartTestMutation,
  useCreateYourOwnTestBioSubmitTestMutation
} = createOwnTestBioApiSlice;
