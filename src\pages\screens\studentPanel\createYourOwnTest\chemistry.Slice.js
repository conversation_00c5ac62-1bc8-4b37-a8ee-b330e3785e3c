import { createOwnTestChemistryApi } from '../../../../redux/api/api';

export const createOwnTestChemistryApiSlice = createOwnTestChemistryApi.injectEndpoints({
  endpoints: (builder) => ({
    createYourOwnTestChemistryStartTest: builder.mutation({
      query: (body) => ({
        url: '/start-test-chemistry',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Create Your Own Test Chemistry Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CreateOwnTestChemistry']
    }),
    createYourOwnTestChemistrySubmitTest: builder.mutation({
      query: (body) => ({
        url: '/evaluate-test-chemistry',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Create Your Own Test Chemistry Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CreateOwnTestChemistry']
    })
  })
});
export const {
  useCreateYourOwnTestChemistryStartTestMutation,
  useCreateYourOwnTestChemistrySubmitTestMutation
} = createOwnTestChemistryApiSlice;
