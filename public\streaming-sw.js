/**
 * ROBUST STREAMING SERVICE WORKER - ZOOM/GOOGLE MEET LEVEL
 * Maintains WebRTC streaming connections in background with multiple protection layers
 * Handles tab switching, app switching, and connection recovery
 */

// Import LiveKit client for direct WebRTC management in service worker
importScripts('https://unpkg.com/livekit-client@2.5.7/dist/livekit-client.umd.js');

// Streaming state management with enhanced protection
let streamingState = {
  isActive: false,
  sessionId: null,
  livekitToken: null,
  livekitUrl: null,
  roomName: null,
  streamUrl: null,
  connectionState: 'disconnected',
  lastHeartbeat: null,
  reconnectAttempts: 0,
  maxReconnectAttempts: 15,
  keepAliveInterval: null,
  connectionCheckInterval: null,
  tokenRefreshInterval: null,
  wakeLock: null,
  streamingMethod: 'webrtc',

  // Enhanced protection mechanisms
  livekitRoom: null,
  mediaStream: null,
  videoTrack: null,
  audioTrack: null,
  isTabVisible: true,
  forceKeepAlive: true,
  backgroundStreamingActive: false,
  lastMainThreadPing: Date.now(),
  emergencyMode: false
};

// WebSocket connection for backend communication
let backendWebSocket = null;
let wsReconnectAttempts = 0;
const maxWsReconnectAttempts = 5;

// Keep-alive and health monitoring intervals
const HEARTBEAT_INTERVAL = 30000; // 30 seconds
const CONNECTION_CHECK_INTERVAL = 60000; // 60 seconds
const TOKEN_REFRESH_INTERVAL = 45 * 60 * 1000; // 45 minutes
const HEALTH_CHECK_INTERVAL = 300000; // 5 minutes

console.log('🔧 Streaming Service Worker loaded');

// Listen for messages from main thread
self.addEventListener('message', async (event) => {
  const { type, data } = event.data;

  console.log('📨 SW received message:', type, data);

  // Update last main thread ping
  streamingState.lastMainThreadPing = Date.now();

  switch (type) {
    case 'START_BACKGROUND_STREAMING':
      await startBackgroundStreaming(data);
      break;

    case 'STOP_BACKGROUND_STREAMING':
      await stopBackgroundStreaming();
      break;

    case 'UPDATE_STREAMING_CONFIG':
      updateStreamingConfig(data);
      break;

    case 'PING':
      sendMessageToClient({ type: 'PONG', timestamp: Date.now() });
      break;

    case 'PING_RESPONSE':
      // Main thread is alive
      streamingState.lastMainThreadPing = Date.now();
      if (streamingState.emergencyMode) {
        console.log('✅ Main thread responded, exiting emergency mode');
        streamingState.emergencyMode = false;
      }
      break;

    case 'TAB_VISIBILITY_CHANGED':
      streamingState.isTabVisible = data.visible;
      console.log('👁️ Tab visibility changed:', data.visible);

      if (!data.visible && streamingState.isActive) {
        console.log('🔄 Tab hidden, ensuring background streaming continues...');
        // Force all protection mechanisms
        requestWakeLockWithRetry();
        if (streamingState.livekitRoom && streamingState.livekitRoom.state === 'disconnected') {
          attemptLiveKitReconnection();
        }
      }
      break;

    case 'GET_STREAMING_STATUS':
      sendStreamingStatus();
      break;

    case 'FORCE_RECONNECT':
      await attemptConnectionRecovery();
      break;

    case 'EMERGENCY_RECOVERY':
      streamingState.emergencyMode = true;
      startEmergencyRecovery();
      break;

    default:
      console.log('❓ Unknown message type:', type);
  }
});

// ROBUST Background Streaming with Direct WebRTC Management
async function startBackgroundStreaming(config) {
  try {
    console.log('🚀 Starting ROBUST background streaming...', config);

    streamingState = {
      ...streamingState,
      isActive: true,
      sessionId: config.sessionId,
      livekitToken: config.livekitToken,
      livekitUrl: config.livekitUrl,
      roomName: config.roomName,
      streamUrl: config.streamUrl,
      streamingMethod: config.streamingMethod || 'webrtc',
      connectionState: 'connecting',
      lastHeartbeat: Date.now(),
      lastMainThreadPing: Date.now(),
      reconnectAttempts: 0,
      backgroundStreamingActive: true,
      forceKeepAlive: true
    };

    // Initialize direct LiveKit connection in service worker
    if (config.livekitToken && config.livekitUrl) {
      await initializeDirectLiveKitConnection();
    }

    // Start ALL protection mechanisms
    startRobustKeepAlive();
    startConnectionMonitoring();
    startTokenRefresh();
    startMainThreadMonitoring();
    startEmergencyRecovery();

    // Connect to backend WebSocket (optional)
    try {
      await connectToBackendWebSocket();
    } catch (error) {
      console.warn('⚠️ Backend WebSocket connection failed, continuing without it:', error);
    }

    // Request wake lock with retry mechanism
    await requestWakeLockWithRetry();

    // Start aggressive tab visibility monitoring
    startTabVisibilityMonitoring();

    streamingState.connectionState = 'connected';

    sendMessageToClient({
      type: 'BACKGROUND_STREAMING_STARTED',
      status: 'success',
      state: streamingState
    });

    console.log('✅ ROBUST background streaming started successfully');

  } catch (error) {
    console.error('❌ Failed to start background streaming:', error);

    streamingState.connectionState = 'failed';

    sendMessageToClient({
      type: 'BACKGROUND_STREAMING_ERROR',
      error: error.message,
      state: streamingState
    });
  }
}

// Initialize Direct LiveKit Connection in Service Worker
async function initializeDirectLiveKitConnection() {
  try {
    console.log('🔗 Initializing direct LiveKit connection in service worker...');

    if (!window.LiveKit) {
      console.error('❌ LiveKit not available in service worker');
      return;
    }

    const { Room, RoomEvent } = window.LiveKit;

    streamingState.livekitRoom = new Room();

    // Set up event handlers for robust connection management
    streamingState.livekitRoom.on(RoomEvent.Connected, () => {
      console.log('✅ Service Worker LiveKit connected');
      streamingState.connectionState = 'connected';
      streamingState.reconnectAttempts = 0;
    });

    streamingState.livekitRoom.on(RoomEvent.Disconnected, (reason) => {
      console.log('❌ Service Worker LiveKit disconnected:', reason);
      streamingState.connectionState = 'disconnected';

      // Immediate reconnection attempt
      if (streamingState.isActive && reason !== 'CLIENT_INITIATED') {
        setTimeout(() => {
          attemptLiveKitReconnection();
        }, 1000);
      }
    });

    streamingState.livekitRoom.on(RoomEvent.Reconnecting, () => {
      console.log('🔄 Service Worker LiveKit reconnecting...');
      streamingState.connectionState = 'reconnecting';
    });

    streamingState.livekitRoom.on(RoomEvent.Reconnected, () => {
      console.log('✅ Service Worker LiveKit reconnected');
      streamingState.connectionState = 'connected';
      streamingState.reconnectAttempts = 0;
    });

    // Connect to LiveKit
    await streamingState.livekitRoom.connect(streamingState.livekitUrl, streamingState.livekitToken);

    console.log('✅ Direct LiveKit connection established in service worker');

  } catch (error) {
    console.error('❌ Failed to initialize direct LiveKit connection:', error);
  }
}

// Attempt LiveKit Reconnection
async function attemptLiveKitReconnection() {
  if (streamingState.reconnectAttempts >= streamingState.maxReconnectAttempts) {
    console.error('❌ Max LiveKit reconnection attempts reached');
    streamingState.emergencyMode = true;
    return;
  }

  streamingState.reconnectAttempts++;
  console.log(`🔄 Attempting LiveKit reconnection (${streamingState.reconnectAttempts}/${streamingState.maxReconnectAttempts})...`);

  try {
    if (streamingState.livekitRoom) {
      await streamingState.livekitRoom.connect(streamingState.livekitUrl, streamingState.livekitToken);
    }
  } catch (error) {
    console.error('❌ LiveKit reconnection failed:', error);

    // Exponential backoff
    setTimeout(() => {
      if (streamingState.isActive) {
        attemptLiveKitReconnection();
      }
    }, Math.min(30000, 2000 * Math.pow(2, streamingState.reconnectAttempts)));
  }
}

// Stop background streaming
async function stopBackgroundStreaming() {
  try {
    console.log('🛑 Stopping background streaming...');
    
    streamingState.isActive = false;
    streamingState.connectionState = 'disconnected';
    
    // Clear all intervals
    clearKeepAlive();
    clearConnectionMonitoring();
    clearTokenRefresh();
    
    // Disconnect backend WebSocket
    if (backendWebSocket) {
      backendWebSocket.close();
      backendWebSocket = null;
    }
    
    // Release wake lock
    await releaseWakeLock();
    
    // Reset state
    streamingState = {
      ...streamingState,
      isActive: false,
      sessionId: null,
      livekitToken: null,
      livekitUrl: null,
      roomName: null,
      streamUrl: null,
      connectionState: 'disconnected',
      reconnectAttempts: 0
    };
    
    sendMessageToClient({
      type: 'BACKGROUND_STREAMING_STOPPED',
      state: streamingState
    });
    
    console.log('✅ Background streaming stopped');
    
  } catch (error) {
    console.error('❌ Error stopping background streaming:', error);
  }
}

// Update streaming configuration
function updateStreamingConfig(config) {
  console.log('🔄 Updating streaming config:', config);

  if (config.livekitToken) {
    streamingState.livekitToken = config.livekitToken;
  }

  if (config.livekitUrl) {
    streamingState.livekitUrl = config.livekitUrl;
  }

  if (config.sessionId) {
    streamingState.sessionId = config.sessionId;
  }

  if (config.streamUrl) {
    streamingState.streamUrl = config.streamUrl;
  }

  if (config.streamingMethod) {
    streamingState.streamingMethod = config.streamingMethod;
  }

  sendMessageToClient({
    type: 'STREAMING_CONFIG_UPDATED',
    state: streamingState
  });
}

// ROBUST Keep-alive mechanism with multiple layers
function startRobustKeepAlive() {
  if (streamingState.keepAliveInterval) {
    clearInterval(streamingState.keepAliveInterval);
  }

  streamingState.keepAliveInterval = setInterval(() => {
    if (!streamingState.isActive) return;

    console.log('💓 ROBUST keep-alive heartbeat');

    streamingState.lastHeartbeat = Date.now();

    // 1. Send heartbeat to main thread
    sendMessageToClient({
      type: 'HEARTBEAT',
      timestamp: streamingState.lastHeartbeat,
      state: streamingState,
      backgroundActive: streamingState.backgroundStreamingActive
    });

    // 2. Maintain LiveKit connection
    if (streamingState.livekitRoom) {
      try {
        const connectionState = streamingState.livekitRoom.state;
        if (connectionState === 'disconnected') {
          console.log('🔄 LiveKit disconnected, attempting reconnection...');
          attemptLiveKitReconnection();
        }
      } catch (error) {
        console.warn('⚠️ Error checking LiveKit state:', error);
      }
    }

    // 3. Send heartbeat to backend if WebSocket is connected
    if (backendWebSocket && backendWebSocket.readyState === WebSocket.OPEN) {
      backendWebSocket.send(JSON.stringify({
        type: 'heartbeat',
        sessionId: streamingState.sessionId,
        timestamp: streamingState.lastHeartbeat,
        streamingMethod: streamingState.streamingMethod,
        backgroundActive: true
      }));
    }

    // 4. Force wake lock renewal
    if (!streamingState.wakeLock) {
      requestWakeLockWithRetry();
    }

    // 5. Check main thread communication
    const timeSinceMainThreadPing = Date.now() - streamingState.lastMainThreadPing;
    if (timeSinceMainThreadPing > 60000) { // 1 minute
      console.log('⚠️ Main thread not responding, entering emergency mode');
      streamingState.emergencyMode = true;
      startEmergencyRecovery();
    }

  }, 15000); // Every 15 seconds for aggressive monitoring

  console.log('✅ ROBUST Keep-alive started');
}

// Main Thread Monitoring
function startMainThreadMonitoring() {
  setInterval(() => {
    if (!streamingState.isActive) return;

    // Request ping from main thread
    sendMessageToClient({
      type: 'PING_REQUEST',
      timestamp: Date.now()
    });

  }, 30000); // Every 30 seconds
}

// Tab Visibility Monitoring
function startTabVisibilityMonitoring() {
  // Monitor page visibility changes
  setInterval(() => {
    sendMessageToClient({
      type: 'CHECK_TAB_VISIBILITY'
    });
  }, 5000); // Every 5 seconds
}

// Emergency Recovery System
function startEmergencyRecovery() {
  if (streamingState.emergencyRecoveryInterval) {
    clearInterval(streamingState.emergencyRecoveryInterval);
  }

  streamingState.emergencyRecoveryInterval = setInterval(() => {
    if (!streamingState.isActive || !streamingState.emergencyMode) return;

    console.log('🚨 Emergency recovery mode active');

    // Try to re-establish all connections
    if (streamingState.livekitRoom && streamingState.livekitRoom.state === 'disconnected') {
      attemptLiveKitReconnection();
    }

    // Force wake lock
    requestWakeLockWithRetry();

    // Notify main thread of emergency
    sendMessageToClient({
      type: 'EMERGENCY_MODE_ACTIVE',
      state: streamingState
    });

  }, 10000); // Every 10 seconds in emergency mode
}

function clearKeepAlive() {
  if (streamingState.keepAliveInterval) {
    clearInterval(streamingState.keepAliveInterval);
    streamingState.keepAliveInterval = null;
    console.log('🛑 Keep-alive stopped');
  }

  if (streamingState.emergencyRecoveryInterval) {
    clearInterval(streamingState.emergencyRecoveryInterval);
    streamingState.emergencyRecoveryInterval = null;
  }
}

// Connection monitoring
function startConnectionMonitoring() {
  if (streamingState.connectionCheckInterval) {
    clearInterval(streamingState.connectionCheckInterval);
  }
  
  streamingState.connectionCheckInterval = setInterval(async () => {
    if (!streamingState.isActive) return;
    
    console.log('🔍 Checking connection health...');
    
    const now = Date.now();
    const timeSinceLastHeartbeat = now - (streamingState.lastHeartbeat || 0);
    
    // Check if connection is stale
    if (timeSinceLastHeartbeat > HEARTBEAT_INTERVAL * 2) {
      console.log('⚠️ Connection appears stale, attempting recovery...');
      await attemptConnectionRecovery();
    }
    
    // Check backend WebSocket connection
    if (!backendWebSocket || backendWebSocket.readyState !== WebSocket.OPEN) {
      console.log('🔄 Backend WebSocket disconnected, reconnecting...');
      await connectToBackendWebSocket();
    }
    
    // Send status update to main thread
    sendMessageToClient({
      type: 'CONNECTION_STATUS',
      state: streamingState,
      timeSinceLastHeartbeat
    });
    
  }, CONNECTION_CHECK_INTERVAL);
  
  console.log('✅ Connection monitoring started');
}

function clearConnectionMonitoring() {
  if (streamingState.connectionCheckInterval) {
    clearInterval(streamingState.connectionCheckInterval);
    streamingState.connectionCheckInterval = null;
    console.log('🛑 Connection monitoring stopped');
  }
}

// Token refresh mechanism
function startTokenRefresh() {
  if (streamingState.tokenRefreshInterval) {
    clearInterval(streamingState.tokenRefreshInterval);
  }

  streamingState.tokenRefreshInterval = setInterval(async () => {
    if (!streamingState.isActive || !streamingState.sessionId) return;

    console.log('🔄 Refreshing LiveKit token...');

    try {
      const response = await fetch('https://sasthra.in/api/enhanced-stream/refresh-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: streamingState.sessionId,
          room_name: streamingState.roomName
        })
      });

      if (response.ok) {
        const data = await response.json();
        streamingState.livekitToken = data.livekit_token;

        // Notify main thread of token update
        sendMessageToClient({
          type: 'TOKEN_REFRESHED',
          token: data.livekit_token,
          state: streamingState
        });

        console.log('✅ LiveKit token refreshed successfully');
      } else {
        console.warn('⚠️ Failed to refresh token:', response.status);
      }
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
    }

  }, TOKEN_REFRESH_INTERVAL);

  console.log('✅ Token refresh started');
}

function clearTokenRefresh() {
  if (streamingState.tokenRefreshInterval) {
    clearInterval(streamingState.tokenRefreshInterval);
    streamingState.tokenRefreshInterval = null;
    console.log('🛑 Token refresh stopped');
  }
}

// Backend WebSocket connection for stream monitoring
async function connectToBackendWebSocket() {
  try {
    if (backendWebSocket && backendWebSocket.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    console.log('🔗 Connecting to backend WebSocket...');

    // Use the stream_url from backend response or fallback to Socket.IO
    const wsUrl = streamingState.streamUrl || 'wss://sasthra.in/socket.io/';
    console.log('📡 Using WebSocket URL:', wsUrl);

    // For Socket.IO URLs, we need to use the Socket.IO client approach
    if (wsUrl.includes('socket.io')) {
      await connectToSocketIO(wsUrl);
      return;
    }

    // For regular WebSocket URLs
    backendWebSocket = new WebSocket(wsUrl);

    backendWebSocket.onopen = () => {
      console.log('✅ Backend WebSocket connected');
      wsReconnectAttempts = 0;

      // Send initial connection message
      backendWebSocket.send(JSON.stringify({
        type: 'streaming_monitor_connect',
        sessionId: streamingState.sessionId,
        streamingMethod: streamingState.streamingMethod,
        timestamp: Date.now()
      }));
    };

    backendWebSocket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        handleBackendWebSocketMessage(message);
      } catch (error) {
        console.error('❌ Error parsing WebSocket message:', error);
      }
    };

    backendWebSocket.onerror = (error) => {
      console.error('❌ Backend WebSocket error:', error);
    };

    backendWebSocket.onclose = (event) => {
      console.log('🔌 Backend WebSocket disconnected:', event.code, event.reason);

      // Attempt reconnection if streaming is still active
      if (streamingState.isActive && wsReconnectAttempts < maxWsReconnectAttempts) {
        wsReconnectAttempts++;
        console.log(`🔄 Attempting WebSocket reconnection (${wsReconnectAttempts}/${maxWsReconnectAttempts})...`);

        setTimeout(() => {
          connectToBackendWebSocket();
        }, 2000 * wsReconnectAttempts); // Exponential backoff
      }
    };

  } catch (error) {
    console.error('❌ Failed to connect to backend WebSocket:', error);
  }
}

// Socket.IO connection for backend communication
async function connectToSocketIO(socketUrl) {
  try {
    console.log('🔗 Connecting to Socket.IO...', socketUrl);

    // Since we're in a service worker, we'll use a simplified approach
    // We'll create a regular WebSocket connection to the Socket.IO endpoint
    // with the proper Socket.IO protocol

    // Extract base URL and create Socket.IO WebSocket URL
    const baseUrl = socketUrl.replace('wss://', '').replace('ws://', '').replace('/socket.io/', '');
    const protocol = socketUrl.startsWith('wss://') ? 'wss://' : 'ws://';
    const socketIOWsUrl = `${protocol}${baseUrl}/socket.io/?EIO=4&transport=websocket`;

    console.log('📡 Socket.IO WebSocket URL:', socketIOWsUrl);

    backendWebSocket = new WebSocket(socketIOWsUrl);

    backendWebSocket.onopen = () => {
      console.log('✅ Socket.IO WebSocket connected');
      wsReconnectAttempts = 0;

      // Send Socket.IO connection message (EIO=4 protocol)
      backendWebSocket.send('40'); // Socket.IO connect message

      // Send streaming monitor connect message
      const message = JSON.stringify(['streaming_monitor_connect', {
        sessionId: streamingState.sessionId,
        streamingMethod: streamingState.streamingMethod,
        timestamp: Date.now()
      }]);
      backendWebSocket.send('42' + message); // Socket.IO event message
    };

    backendWebSocket.onmessage = (event) => {
      try {
        const data = event.data;
        console.log('📨 Socket.IO message received:', data);

        // Handle Socket.IO protocol messages
        if (data.startsWith('40')) {
          console.log('✅ Socket.IO connected');
        } else if (data.startsWith('42')) {
          // Parse Socket.IO event message
          const eventData = JSON.parse(data.substring(2));
          if (Array.isArray(eventData) && eventData.length >= 2) {
            const [eventName, eventPayload] = eventData;
            handleBackendMessage({ type: eventName, ...eventPayload });
          }
        } else if (data === '3') {
          // Pong response to ping
          console.log('🏓 Socket.IO pong received');
        }
      } catch (error) {
        console.error('❌ Error parsing Socket.IO message:', error);
      }
    };

    backendWebSocket.onerror = (error) => {
      console.error('❌ Socket.IO WebSocket error:', error);
    };

    backendWebSocket.onclose = (event) => {
      console.log('🔌 Socket.IO WebSocket disconnected:', event.code, event.reason);

      // Attempt reconnection if streaming is still active
      if (streamingState.isActive && wsReconnectAttempts < maxWsReconnectAttempts) {
        wsReconnectAttempts++;
        console.log(`🔄 Attempting Socket.IO reconnection (${wsReconnectAttempts}/${maxWsReconnectAttempts})...`);

        setTimeout(() => {
          connectToSocketIO(socketUrl);
        }, 2000 * wsReconnectAttempts); // Exponential backoff
      }
    };

    // Send periodic ping to keep connection alive
    setInterval(() => {
      if (backendWebSocket && backendWebSocket.readyState === WebSocket.OPEN) {
        backendWebSocket.send('2'); // Socket.IO ping
      }
    }, 25000); // Every 25 seconds

  } catch (error) {
    console.error('❌ Failed to connect to Socket.IO:', error);
  }
}

// Handle messages from backend WebSocket/Socket.IO
function handleBackendWebSocketMessage(message) {
  handleBackendMessage(message);
}

function handleBackendMessage(message) {
  console.log('📨 Backend message:', message);

  switch (message.type) {
    case 'streaming_status':
      // Update streaming state based on backend status
      if (message.status === 'disconnected' && streamingState.isActive) {
        console.log('⚠️ Backend reports stream disconnected, attempting recovery...');
        attemptConnectionRecovery();
      }
      break;

    case 'force_reconnect':
      console.log('🔄 Backend requesting force reconnect...');
      sendMessageToClient({
        type: 'FORCE_RECONNECT_REQUIRED',
        reason: message.reason
      });
      break;

    case 'token_expired':
      console.log('🔄 Backend reports token expired, refreshing...');
      // Trigger immediate token refresh
      if (streamingState.tokenRefreshInterval) {
        clearInterval(streamingState.tokenRefreshInterval);
        startTokenRefresh();
      }
      break;

    case 'stream_health_check':
      // Respond to health check
      if (backendWebSocket && backendWebSocket.readyState === WebSocket.OPEN) {
        backendWebSocket.send(JSON.stringify({
          type: 'stream_health_response',
          sessionId: streamingState.sessionId,
          state: streamingState.connectionState,
          lastHeartbeat: streamingState.lastHeartbeat,
          timestamp: Date.now()
        }));
      }
      break;

    default:
      console.log('❓ Unknown backend message type:', message.type);
  }
}

// Connection recovery mechanism
async function attemptConnectionRecovery() {
  if (streamingState.reconnectAttempts >= streamingState.maxReconnectAttempts) {
    console.error('❌ Max reconnection attempts reached');

    sendMessageToClient({
      type: 'CONNECTION_RECOVERY_FAILED',
      state: streamingState
    });

    return;
  }

  streamingState.reconnectAttempts++;
  streamingState.connectionState = 'reconnecting';

  console.log(`🔄 Attempting connection recovery (${streamingState.reconnectAttempts}/${streamingState.maxReconnectAttempts})...`);

  try {
    // Notify main thread to attempt reconnection
    sendMessageToClient({
      type: 'ATTEMPT_RECONNECTION',
      attempt: streamingState.reconnectAttempts,
      maxAttempts: streamingState.maxReconnectAttempts,
      state: streamingState
    });

    // Reset heartbeat
    streamingState.lastHeartbeat = Date.now();

    // Reconnect backend WebSocket
    await connectToBackendWebSocket();

    streamingState.connectionState = 'connected';

    console.log('✅ Connection recovery successful');

  } catch (error) {
    console.error('❌ Connection recovery failed:', error);

    streamingState.connectionState = 'failed';

    // Retry after delay
    setTimeout(() => {
      if (streamingState.isActive) {
        attemptConnectionRecovery();
      }
    }, 5000 * streamingState.reconnectAttempts); // Exponential backoff
  }
}

// Enhanced Wake Lock management with retry
async function requestWakeLockWithRetry() {
  let retryCount = 0;
  const maxRetries = 5;

  while (retryCount < maxRetries) {
    try {
      if ('wakeLock' in navigator && !streamingState.wakeLock) {
        streamingState.wakeLock = await navigator.wakeLock.request('screen');
        console.log('✅ Wake Lock acquired in service worker (attempt', retryCount + 1, ')');

        streamingState.wakeLock.addEventListener('release', () => {
          console.log('🔓 Wake Lock released, will retry...');
          streamingState.wakeLock = null;

          // Auto-retry after release
          if (streamingState.isActive) {
            setTimeout(() => {
              requestWakeLockWithRetry();
            }, 1000);
          }
        });

        sendMessageToClient({
          type: 'WAKE_LOCK_ACQUIRED',
          state: streamingState
        });

        return; // Success
      } else if (!('wakeLock' in navigator)) {
        console.warn('⚠️ Wake Lock API not supported');
        return;
      }
    } catch (error) {
      console.error(`❌ Failed to acquire Wake Lock (attempt ${retryCount + 1}):`, error);
      retryCount++;

      if (retryCount < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 2000 * retryCount)); // Exponential backoff
      }
    }
  }

  console.error('❌ Failed to acquire Wake Lock after', maxRetries, 'attempts');
}

// Legacy wake lock function for compatibility
async function requestWakeLock() {
  return requestWakeLockWithRetry();
}

async function releaseWakeLock() {
  try {
    if (streamingState.wakeLock) {
      await streamingState.wakeLock.release();
      streamingState.wakeLock = null;
      console.log('✅ Wake Lock released');

      sendMessageToClient({
        type: 'WAKE_LOCK_RELEASED',
        state: streamingState
      });
    }
  } catch (error) {
    console.error('❌ Failed to release Wake Lock:', error);
  }
}

// Utility functions
function sendMessageToClient(message) {
  self.clients.matchAll().then(clients => {
    clients.forEach(client => {
      client.postMessage(message);
    });
  });
}

function sendStreamingStatus() {
  sendMessageToClient({
    type: 'STREAMING_STATUS',
    state: streamingState,
    timestamp: Date.now()
  });
}

// Handle service worker lifecycle events
self.addEventListener('install', (event) => {
  console.log('🔧 Streaming Service Worker installing...');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('✅ Streaming Service Worker activated');
  event.waitUntil(self.clients.claim());
});

// Handle network status changes
self.addEventListener('online', () => {
  if (streamingState.isActive) {
    console.log('🌐 Network back online, checking stream health...');
    attemptConnectionRecovery();
  }
});

self.addEventListener('offline', () => {
  if (streamingState.isActive) {
    console.log('📴 Network offline, stream may be affected');

    sendMessageToClient({
      type: 'NETWORK_OFFLINE',
      state: streamingState
    });
  }
});

// Periodic health check (runs every 5 minutes)
setInterval(() => {
  if (streamingState.isActive) {
    console.log('🏥 Periodic health check...');

    const now = Date.now();
    const timeSinceLastHeartbeat = now - (streamingState.lastHeartbeat || 0);

    // If no heartbeat for more than 2 minutes, something is wrong
    if (timeSinceLastHeartbeat > 120000) {
      console.log('⚠️ No heartbeat for 2+ minutes, forcing recovery...');
      attemptConnectionRecovery();
    }

    sendMessageToClient({
      type: 'HEALTH_CHECK',
      state: streamingState,
      timeSinceLastHeartbeat
    });
  }
}, HEALTH_CHECK_INTERVAL);

// Debug: Log service worker status every 30 seconds
setInterval(() => {
  if (streamingState.isActive) {
    console.log('🔍 Service Worker Status Check:', {
      isActive: streamingState.isActive,
      sessionId: streamingState.sessionId,
      connectionState: streamingState.connectionState,
      streamingMethod: streamingState.streamingMethod,
      streamUrl: streamingState.streamUrl,
      lastHeartbeat: streamingState.lastHeartbeat ? new Date(streamingState.lastHeartbeat).toLocaleTimeString() : 'None',
      wsConnected: backendWebSocket ? backendWebSocket.readyState === WebSocket.OPEN : false
    });
  }
}, 30000);

console.log('🚀 Streaming Service Worker fully initialized');
