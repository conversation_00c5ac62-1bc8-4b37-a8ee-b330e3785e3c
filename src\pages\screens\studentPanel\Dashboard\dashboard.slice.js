import { createSlice } from '@reduxjs/toolkit';
import { AnalyticsApi } from '../../../../redux/api/api';

const initialState = {
  onboardAssessment: null,
  cbt: null
};

export const studentDashboardApiSlice = AnalyticsApi.injectEndpoints({
  endpoints: (builder) => ({
    getOnboardAssessmentService: builder.query({
      query: (query) => {
        return `/analytics/onboard-assessment/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    }),
    getCbtService: builder.query({
      query: (query) => {
        return `/analytics/cbt/${query.userId}`;
      },
      transformResponse: (res) => {
        return res;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['StudentDashboard']
    })
  })
});

const studentDashboardSlice = createSlice({
  name: 'studentDashboard',
  initialState,
  reducers: {
    setOnboardAssessment(state, action) {
      state.onboardAssessment = action.payload;
    },
    clearOnboardAssessment(state) {
      state.onboardAssessment = null;
    },
    setCbt(state, action) {
      state.cbt = action.payload;
    },
    clearCbt(state) {
      state.cbt = null;
    }
  }
});

export const { useLazyGetOnboardAssessmentServiceQuery, useLazyGetCbtServiceQuery } =
  studentDashboardApiSlice;
export const { setOnboardAssessment, clearOnboardAssessment, setCbt, clearCbt } =
  studentDashboardSlice.actions;
export const selectStudentOverview = (state) => state.studentDashboard.onboardAssessment;
export default studentDashboardSlice.reducer;
