// Subject to API mapping configuration
// This file contains the mapping between subjects and their corresponding API functions

export const SUBJECT_API_MAPPING = {
  // Biology subjects
  biology: 'biology',
  bio: 'biology',
  'biological science': 'biology',
  'life science': 'biology',

  // Chemistry subjects
  chemistry: 'chemistry',
  chem: 'chemistry',
  'chemical science': 'chemistry',

  // Physics subjects
  physics: 'physics',
  phy: 'physics',
  'physical science': 'physics',

  // Mathematics subjects
  mathematics: 'math',
  math: 'math',
  maths: 'math',
  'mathematical science': 'math'
};

// Helper function to get the correct API function based on subject
export const getSubjectApiFunction = (subject, apiFunctions) => {
  const normalizedSubject = subject.toLowerCase().trim();

  // Check for exact matches first
  if (SUBJECT_API_MAPPING[normalizedSubject]) {
    const apiKey = SUBJECT_API_MAPPING[normalizedSubject];
    return apiFunctions[apiKey];
  }

  // Check for partial matches
  for (const [key, value] of Object.entries(SUBJECT_API_MAPPING)) {
    if (normalizedSubject.includes(key) || key.includes(normalizedSubject)) {
      return apiFunctions[value];
    }
  }

  // If no match found, return null
  return null;
};

// Helper function to validate if a subject is supported
export const isSubjectSupported = (subject) => {
  const normalizedSubject = subject.toLowerCase().trim();

  // Check for exact matches
  if (SUBJECT_API_MAPPING[normalizedSubject]) {
    return true;
  }

  // Check for partial matches
  for (const key of Object.keys(SUBJECT_API_MAPPING)) {
    if (normalizedSubject.includes(key) || key.includes(normalizedSubject)) {
      return true;
    }
  }

  return false;
};

// Get all supported subjects
export const getSupportedSubjects = () => {
  return Object.keys(SUBJECT_API_MAPPING);
};
