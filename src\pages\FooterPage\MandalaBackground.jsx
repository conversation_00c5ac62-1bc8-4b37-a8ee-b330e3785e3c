import React from 'react';

const MandalaBackground = () => (
  <div
    className="absolute inset-0 opacity-10"
    style={{
      backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f97316' fill-opacity='0.05'%3E%3Cpath d='M50 0a50 50 0 0 1 50 50c0 27.614-22.386 50-50 50S0 77.614 0 50 22.386 0 50 0zm0 10a40 40 0 0 0-40 40 40 40 0 0 0 40 40 40 40 0 0 0 40-40 40 40 0 0 0-40-40zm0 10a30 30 0 0 1 30 30 30 30 0 0 1-30 30 30 30 0 0 1-30-30 30 30 0 0 1 30-30zm0 10a20 20 0 0 0-20 20 20 20 0 0 0 20 20 20 20 0 0 0 20-20 20 20 0 0 0-20-20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
      backgroundSize: '200px 200px'
    }}
  />
);

export default React.memo(MandalaBackground);
