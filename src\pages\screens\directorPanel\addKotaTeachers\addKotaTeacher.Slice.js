import { createSlice } from '@reduxjs/toolkit';
import { directorApi } from '../../../../redux/api/api';

const initialState = {
  addKotaTeacherData: null,
  listKotaTeacherData: null,
  mapKotaTeacherData: null
};

export const addKotaTeacherSlice = directorApi.injectEndpoints({
  endpoints: (builder) => ({
    directorAddKotaTeacherService: builder.mutation({
      query: (body) => ({
        url: '/add-kota-teacher',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Add Kota Teacher Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providerTags: ['AddKotaTeacher']
    }),
    directorListKotaTeacherService: builder.query({
      query: () => ({
        url: '/list-kota-teachers',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('List Kota Teachers Response:', response);
        // Extract teachers array from response
        if (response && Array.isArray(response.teachers)) {
          return response.teachers;
        }
        if (Array.isArray(response)) {
          return response;
        }
        if (response && Array.isArray(response.data)) {
          return response.data;
        }
        return [];
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providerTags: ['ListKotaTeacher']
    }),
    directorMapKotaTeacherService: builder.mutation({
      query: ({ teacherId, centerCode }) => ({
        url: '/map-teacher-to-center',
        method: 'POST',
        body: { teacher_id: teacherId, center_code: centerCode }
      }),
      transformResponse: (response) => {
        console.log('Map Kota Teacher Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providerTags: ['MapKotaTeacher']
    })
  })
});

const KotaTeacherSlice = createSlice({
  name: 'kotaTeacher',
  initialState,
  reducers: {
    setKotaTeacherData(state, action) {
      state.addKotaTeacherData = action.payload;
    },
    setListKotaTeacherData(state, action) {
      state.listKotaTeacherData = action.payload;
    },
    setMapKotaTeacherData(state, action) {
      state.mapKotaTeacherData = action.payload;
    }
  }
});

export const {
  useDirectorAddKotaTeacherServiceMutation,
  useDirectorListKotaTeacherServiceQuery,
  useDirectorMapKotaTeacherServiceMutation
} = addKotaTeacherSlice;
export const { setKotaTeacherData, setListKotaTeacherData, setMapKotaTeacherData } =
  KotaTeacherSlice.actions;
export default KotaTeacherSlice.reducer;
