import React, { useState, useRef } from 'react';

const VisionBot = ({ onClose }) => {
  const [shareUrl, setShareUrl] = useState(
    'https://sasthra-ai-teacher-671317745974.us-west1.run.app/'
  );
  const [showIframe, setShowIframe] = useState(false);
  const [inputUrl, setInputUrl] = useState('');
  const iframeRef = useRef(null);

  const handleButtonClick = () => {
    const url = prompt('Please enter the share URL for VisionBot:', shareUrl);
    if (url) {
      setShareUrl(url);
      setShowIframe(true);
    }
  };

  const handleClose = () => {
    if (iframeRef.current) {
      iframeRef.current.contentWindow.postMessage({ type: 'STOP_AUDIO' }, '*');
    }
    setShowIframe(false);
    onClose();
  };

  return (
    <div className="relative w-full h-[600px] bg-gradient-to-br from-purple-50 to-indigo-100 rounded-xl shadow-2xl overflow-hidden">
      {showIframe ? (
        <iframe
          ref={iframeRef}
          src={shareUrl}
          title="Sasthra VisionBot"
          width="100%"
          height="100%"
          style={{ border: 'none' }}
          allow="microphone; clipboard-write; fullscreen"
          className="rounded-xl"
        ></iframe>
      ) : (
        <div className="flex flex-col items-center justify-center h-full bg-gradient-to-br from-purple-100 to-indigo-200">
          <div className="text-center p-8">
            <div className="mb-6">
              <svg
                className="w-24 h-24 mx-auto text-purple-600"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-3">VisionBot Ready</h3>
            <p className="text-gray-600 mb-6 max-w-md">
              Experience the power of AI vision technology. Click below to start your interactive
              session with VisionBot.
            </p>
            <button
              className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-3 rounded-full hover:from-purple-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-200 shadow-lg font-semibold"
              onClick={handleButtonClick}
            >
              Load VisionBot
            </button>
          </div>
        </div>
      )}
      <button
        className="absolute top-4 right-4 bg-gradient-to-r from-red-500 to-pink-500 text-white px-6 py-2 rounded-full hover:from-red-600 hover:to-pink-600 transform hover:scale-105 transition-all duration-200 shadow-lg font-medium"
        onClick={handleClose}
      >
        Close
      </button>
    </div>
  );
};

export default VisionBot;
