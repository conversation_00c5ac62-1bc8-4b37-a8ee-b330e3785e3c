import React, { useEffect, useState } from 'react';
import Button from '../../../../components/Field/Button';
import PopUp from '../../../../components/PopUp/PopUp';
import SearchableDropdown from '../../../../components/Field/SearchableDropdown';
import Input from '../../../../components/Field/Input';
import {
  setCouresData,
  setSubjectData,
  setTopicData,
  useCreateTopicServiceMutation,
  useDeleteTopicServiceMutation,
  useLazyGetAllTopicsServiceQuery,
  useLazyGetCourseServiceQuery,
  useLazyGetSubjectServiceQuery,
  useUpdateTopicServiceMutation
} from './processSelector.slice';
import { useDispatch, useSelector } from 'react-redux';
import Toastify from '../../../../components/PopUp/Toastify';
import Table from '../../../../components/Layout/Table';
import { TopicMappingHeader } from './TableHeaderData';

const TopicMapping = () => {
  const [popUp, setPopUp] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState({ id: '', name: '' });
  const [selectedSubject, setSelectedSubject] = useState({ id: '', name: '' });
  const [topic, setTopic] = useState('');
  const [res, setRes] = useState(null);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [isEditId, setIsEditId] = useState('');
  const [confirmPopUp, setConfirmPopUp] = useState(false);

  const [getCourseData] = useLazyGetCourseServiceQuery();
  const [getSubjectData] = useLazyGetSubjectServiceQuery();
  const [createTopicApi] = useCreateTopicServiceMutation();
  const [getTopicDataService] = useLazyGetAllTopicsServiceQuery();
  const [updateTopicDataService] = useUpdateTopicServiceMutation();
  const [deleteTopicDataService] = useDeleteTopicServiceMutation();

  const dispatch = useDispatch();

  const courseData = useSelector((state) => state.processSelector.courseData);
  const subjectData = useSelector((state) => state.processSelector.subjectData);
  const topicData = useSelector((state) => state.processSelector?.topicsData);

  console.log(subjectData);

  useEffect(() => {
    fetchCourse();
    fetchSubject();
    fetchAllTopicsService();
  }, []);

  const fetchCourse = async () => {
    try {
      const res = await getCourseData().unwrap();
      dispatch(setCouresData(res));
    } catch (error) {
      setRes(error);
    }
  };

  const fetchSubject = async () => {
    try {
      const res = await getSubjectData().unwrap();
      dispatch(setSubjectData(res));
    } catch (error) {
      setRes(error);
    }
  };

  const fetchAllTopicsService = async () => {
    try {
      const res = await getTopicDataService().unwrap();
      dispatch(setTopicData(res));
    } catch (error) {
      setRes(error);
    }
  };

  const createTopicPostService = async () => {
    try {
      const res = await createTopicApi({
        course_id: selectedCourse.id,
        course_name: selectedCourse.name,
        subject_id: selectedSubject.id,
        subject_name: selectedSubject.name,
        topic_name: topic
      }).unwrap();
      fetchAllTopicsService();
      setRes(res);
    } catch (error) {
      setRes(error);
    } finally {
      setPopUp(false);
      setSelectedCourse({ id: '', name: '' });
      setSelectedSubject({ id: '', name: '' });
      setTopic('');
    }
  };

  const handleEditTopic = async (e) => {
    setPopUp(true);
    setIsEdit(true);
    setSelectedCourse({ id: e.course_id, name: e.course_name });
    setSelectedSubject({ id: e.subject_id, name: e.subject_name });
    setTopic(e.topic_name);
    setIsEditId(e.topic_id);
  };

  const updateTopicPostService = async () => {
    try {
      const res = await updateTopicDataService({
        topic_id: isEditId,
        course_id: selectedCourse.id,
        course_name: selectedCourse.name,
        subject_id: selectedSubject.id,
        subject_name: selectedSubject.name,
        topic_name: topic
      }).unwrap();
      fetchAllTopicsService();
      setRes(res);
    } catch (error) {
      setRes(error);
    } finally {
      setPopUp(false);
      setIsEdit(false);
      setIsEditId('');
    }
  };

  const handleDeleteTopic = async (e) => {
    setConfirmPopUp(true);
    setIsDelete(true);
    setIsEditId(e.topic_id);
  };

  const handleDeleteTopicService = async () => {
    try {
      const res = await deleteTopicDataService(isEditId).unwrap();
      fetchAllTopicsService();
      setRes(res);
    } catch (error) {
      setRes(error);
    } finally {
      setConfirmPopUp(false);
      setIsDelete(false);
      setIsEditId('');
    }
  };

  return (
    <div>
      <Toastify res={res} resClear={() => setRes(null)} />
      {popUp && (
        <PopUp
          title={isEdit ? 'Update Topic Mapping' : 'Create Topic Mapping'}
          onClose={() => {
            setPopUp(false);
            setIsEdit(false);
            setSelectedCourse({ id: '', name: '' });
            setSelectedSubject({ id: '', name: '' });
            setTopic('');
          }}
          post={isEdit ? updateTopicPostService : createTopicPostService}
          isEdit={isEdit}
        >
          <div className="grid grid-cols-2 gap-2 mb-4">
            <SearchableDropdown
              label={'Course Name'}
              value={selectedCourse?.id}
              placeholder="Select/Search the Course Name"
              options={courseData}
              onChange={(e) => setSelectedCourse(e)}
              required
            />
            <SearchableDropdown
              label={'Subject Name'}
              value={selectedSubject?.id}
              placeholder={'Select/Search the Subject Name'}
              options={subjectData}
              onChange={(e) => setSelectedSubject(e)}
              disabled={!selectedCourse?.id}
              required
            />
          </div>
          <Input
            label={'Topic Name'}
            value={topic}
            onChange={(e) => setTopic(e.target.value)}
            className={'border rounded bg-white disabled:bg-gray-200'}
            disabled={!selectedCourse?.id || !selectedSubject?.id}
            required
          />
        </PopUp>
      )}
      {confirmPopUp && (
        <PopUp
          onClose={() => {
            setConfirmPopUp(false);
            setIsDelete(false);
          }}
          width="sm"
          isDelete={isDelete}
          title={'Delete Topic Mapping'}
          post={handleDeleteTopicService}
        >
          <p className="text-xl text-center my-8">Are you want to delete the Topic Mapping?</p>
        </PopUp>
      )}

      <Table
        title="Topic Mapping"
        onAddNew={() => setPopUp(true)}
        buttonName="Create Topic Mapping"
        header={TopicMappingHeader}
        data={topicData}
        searchBy={['course_name', 'subject_name', 'topic_name']}
        searchPlaceholder="Search by Course, Subject or Topic Name"
        onEdit={(e) => handleEditTopic(e)}
        onDelete={(e) => handleDeleteTopic(e)}
      />
    </div>
  );
};

export default TopicMapping;
