import { createSlice } from '@reduxjs/toolkit';
import { authApi } from '../../redux/api/api';

const initialState = {
  authData: null
};

export const authApiSlice = authApi.injectEndpoints({
  endpoints: (builder) => ({
    userLoginService: builder.mutation({
      query: (body) => ({
        url: `/login`,
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Login']
    }),
    userLogoutService: builder.mutation({
      query: (body) => ({
        url: `/logout`,
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['Login']
    }),
    verfiyOptService: builder.mutation({
      query: (body) => ({
        url: `/verify-otp`,
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Login']
    }),
    forgotPasswordService: builder.mutation({
      query: (body) => ({
        url: '/forgot_password',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Login']
    }),
    resetPasswordService: builder.mutation({
      query: (body) => ({
        url: '/reset_password',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Login']
    })
  })
});

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setAuthData(state, action) {
      state.authData = action.payload;
    },
    clearAuthData(state) {
      state.authData = null;
    }
  }
});

export const {
  useUserLoginServiceMutation,
  useVerfiyOptServiceMutation,
  useForgotPasswordServiceMutation,
  useResetPasswordServiceMutation,
  useUserLogoutServiceMutation
} = authApiSlice;
export const { setAuthData, clearAuthData } = authSlice.actions;
export const selectAuthData = (state) => state.auth.authData;
export default authSlice.reducer;
