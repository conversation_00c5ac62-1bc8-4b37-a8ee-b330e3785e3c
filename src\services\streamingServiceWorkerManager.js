/**
 * Streaming Service Worker Manager
 * Handles communication between main thread and streaming service worker
 * Manages background streaming functionality
 */

class StreamingServiceWorkerManager {
  constructor() {
    this.serviceWorker = null;
    this.isRegistered = false;
    this.messageHandlers = new Map();
    this.streamingState = null;
    this.onStateChange = null;
    this.onError = null;
    this.onReconnectRequired = null;
    
    // Bind methods
    this.handleServiceWorkerMessage = this.handleServiceWorkerMessage.bind(this);
  }

  // Register the streaming service worker
  async register() {
    try {
      if (!('serviceWorker' in navigator)) {
        throw new Error('Service Worker not supported');
      }

      console.log('🔧 Registering streaming service worker...');

      const registration = await navigator.serviceWorker.register('/streaming-sw.js', {
        scope: '/'
      });

      console.log('✅ Streaming service worker registered:', registration);

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready;

      // Get the active service worker
      this.serviceWorker = registration.active || registration.waiting || registration.installing;

      if (!this.serviceWorker) {
        throw new Error('No active service worker found');
      }

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', this.handleServiceWorkerMessage);

      this.isRegistered = true;
      console.log('✅ Streaming service worker manager ready');

      return true;
    } catch (error) {
      console.error('❌ Failed to register streaming service worker:', error);
      throw error;
    }
  }

  // Start background streaming
  async startBackgroundStreaming(config) {
    try {
      if (!this.isRegistered) {
        await this.register();
      }

      console.log('🚀 Starting background streaming...', config);

      const message = {
        type: 'START_BACKGROUND_STREAMING',
        data: {
          sessionId: config.sessionId,
          livekitToken: config.livekitToken,
          livekitUrl: config.livekitUrl,
          roomName: config.roomName,
          streamUrl: config.streamUrl,
          streamingMethod: config.streamingMethod || 'webrtc'
        }
      };

      this.sendMessageToServiceWorker(message);

      return true;
    } catch (error) {
      console.error('❌ Failed to start background streaming:', error);
      if (this.onError) {
        this.onError(error);
      }
      throw error;
    }
  }

  // Stop background streaming
  async stopBackgroundStreaming() {
    try {
      console.log('🛑 Stopping background streaming...');

      const message = {
        type: 'STOP_BACKGROUND_STREAMING'
      };

      this.sendMessageToServiceWorker(message);

      return true;
    } catch (error) {
      console.error('❌ Failed to stop background streaming:', error);
      if (this.onError) {
        this.onError(error);
      }
      throw error;
    }
  }

  // Update streaming configuration
  updateStreamingConfig(config) {
    try {
      console.log('🔄 Updating streaming config...', config);

      const message = {
        type: 'UPDATE_STREAMING_CONFIG',
        data: config
      };

      this.sendMessageToServiceWorker(message);
    } catch (error) {
      console.error('❌ Failed to update streaming config:', error);
      if (this.onError) {
        this.onError(error);
      }
    }
  }

  // Get current streaming status
  getStreamingStatus() {
    try {
      const message = {
        type: 'GET_STREAMING_STATUS'
      };

      this.sendMessageToServiceWorker(message);
    } catch (error) {
      console.error('❌ Failed to get streaming status:', error);
    }
  }

  // Force reconnection
  forceReconnect() {
    try {
      console.log('🔄 Forcing reconnection...');

      const message = {
        type: 'FORCE_RECONNECT'
      };

      this.sendMessageToServiceWorker(message);
    } catch (error) {
      console.error('❌ Failed to force reconnect:', error);
    }
  }

  // Send ping to service worker
  ping() {
    try {
      const message = {
        type: 'PING'
      };

      this.sendMessageToServiceWorker(message);
    } catch (error) {
      console.error('❌ Failed to ping service worker:', error);
    }
  }

  // Send message to service worker
  sendMessageToServiceWorker(message) {
    if (!this.serviceWorker) {
      console.error('❌ Service worker not available');
      return;
    }

    try {
      this.serviceWorker.postMessage(message);
      console.log('📤 Sent message to service worker:', message.type);
    } catch (error) {
      console.error('❌ Failed to send message to service worker:', error);
    }
  }

  // Handle messages from service worker
  handleServiceWorkerMessage(event) {
    const { type, ...data } = event.data;
    
    console.log('📨 Received message from service worker:', type, data);

    switch (type) {
      case 'BACKGROUND_STREAMING_STARTED':
        this.streamingState = data.state;
        if (this.onStateChange) {
          this.onStateChange('started', data);
        }
        break;

      case 'BACKGROUND_STREAMING_STOPPED':
        this.streamingState = data.state;
        if (this.onStateChange) {
          this.onStateChange('stopped', data);
        }
        break;

      case 'BACKGROUND_STREAMING_ERROR':
        if (this.onError) {
          this.onError(new Error(data.error));
        }
        break;

      case 'HEARTBEAT':
        this.streamingState = data.state;
        if (this.onStateChange) {
          this.onStateChange('heartbeat', data);
        }
        break;

      case 'CONNECTION_STATUS':
        this.streamingState = data.state;
        if (this.onStateChange) {
          this.onStateChange('connection_status', data);
        }
        break;

      case 'ATTEMPT_RECONNECTION':
        if (this.onReconnectRequired) {
          this.onReconnectRequired(data);
        }
        break;

      case 'FORCE_RECONNECT_REQUIRED':
        if (this.onReconnectRequired) {
          this.onReconnectRequired(data);
        }
        break;

      case 'TOKEN_REFRESHED':
        this.streamingState = data.state;
        if (this.onStateChange) {
          this.onStateChange('token_refreshed', data);
        }
        break;

      case 'WAKE_LOCK_ACQUIRED':
        console.log('✅ Wake lock acquired by service worker');
        break;

      case 'WAKE_LOCK_RELEASED':
        console.log('🔓 Wake lock released by service worker');
        break;

      case 'NETWORK_OFFLINE':
        if (this.onStateChange) {
          this.onStateChange('network_offline', data);
        }
        break;

      case 'HEALTH_CHECK':
        if (this.onStateChange) {
          this.onStateChange('health_check', data);
        }
        break;

      case 'STREAMING_STATUS':
        this.streamingState = data.state;
        if (this.onStateChange) {
          this.onStateChange('status_update', data);
        }
        break;

      case 'PONG':
        console.log('🏓 Received pong from service worker');
        break;

      default:
        console.log('❓ Unknown message from service worker:', type);
    }
  }

  // Set event handlers
  setOnStateChange(handler) {
    this.onStateChange = handler;
  }

  setOnError(handler) {
    this.onError = handler;
  }

  setOnReconnectRequired(handler) {
    this.onReconnectRequired = handler;
  }

  // Get current state
  getCurrentState() {
    return this.streamingState;
  }

  // Check if service worker is active
  isActive() {
    return this.isRegistered && this.serviceWorker;
  }

  // Cleanup
  cleanup() {
    if (navigator.serviceWorker) {
      navigator.serviceWorker.removeEventListener('message', this.handleServiceWorkerMessage);
    }
    
    this.serviceWorker = null;
    this.isRegistered = false;
    this.streamingState = null;
    this.messageHandlers.clear();
  }
}

// Create singleton instance
const streamingServiceWorkerManager = new StreamingServiceWorkerManager();

export default streamingServiceWorkerManager;
