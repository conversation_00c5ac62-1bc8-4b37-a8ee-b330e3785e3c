import { createSlice } from '@reduxjs/toolkit';
import { mentorApi } from '../../../../redux/api/api';

const initialState = {
  addMentorData: null,
  listMentorData: null
};

export const addMentorSlice = mentorApi.injectEndpoints({
  endpoints: (builder) => ({
    directorAddMentorService: builder.mutation({
      query: (body) => ({
        url: '/add-mentor',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Add Mentor Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['AddMentor']
    }),
    directorListMentorService: builder.query({
      query: () => ({
        url: '/list-mentor',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('List Mentors Response:', response);
        // Extract mentors array from response
        if (response && Array.isArray(response.mendor)) {
          return response.mendor;
        }
        if (Array.isArray(response)) {
          return response;
        }
        if (response && Array.isArray(response.data)) {
          return response.data;
        }
        return [];
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ListMentor']
    })
  })
});

const MentorSlice = createSlice({
  name: 'mentor',
  initialState,
  reducers: {
    setAddMentorData(state, action) {
      state.addMentorData = action.payload;
    },
    setListMentorData(state, action) {
      state.listMentorData = action.payload;
    }
  }
});

export const { useDirectorAddMentorServiceMutation, useDirectorListMentorServiceQuery } =
  addMentorSlice;

export const { setAddMentorData, setListMentorData } = MentorSlice.actions;

export default MentorSlice.reducer;
