import { DashboardUserDetailsApi } from '../../../redux/api/api';

export const dashboardApiSlice = DashboardUserDetailsApi.injectEndpoints({
  endpoints: (builder) => ({
    getUserStudentDashboardData: builder.query({
      query: (body) => ({
        url: '/student-dashboard',
        method: 'GET',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log(response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['DashboardUserDetails']
    })
  })
});

export const { useLazyGetUserStudentDashboardDataQuery } = dashboardApiSlice;
