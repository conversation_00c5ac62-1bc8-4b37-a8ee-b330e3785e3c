import { createSlice } from '@reduxjs/toolkit';
import { UpcomingEventsApi } from '../../redux/api/api';

const initialState = {
  registrationData: null,
  paymentStatus: null
};

// Inject endpoints
export const eventRegistrationApiSlice = UpcomingEventsApi.injectEndpoints({
  endpoints: (builder) => ({
    registerEvent: builder.mutation({
      query: (body) => ({
        url: '/register',
        method: 'POST',
        body
      }),
      transformResponse: (response) => {
        console.log('Register Event Response:', response);
        if (!response.payment_id || !response.payment_url || !response.qr_base64) {
          throw new Error('Missing required fields in response');
        }
        return response;
      },
      transformErrorResponse: (error) => {
        console.error('Register Event Error:', error);
        return {
          status: error.status || 500,
          data: error.data?.error || 'Registration failed'
        };
      },
      invalidatesTags: ['PaymentStatus']
    }),

    checkPaymentStatus: builder.query({
      query: (paymentId) => ({
        url: `/check-status/${paymentId}`,
        method: 'GET'
      }),

      // ✅ No need for responseHandler anymore
      // ✅ No need for transformResponse anymore

      transformErrorResponse: (error) => {
        console.error('Check Payment Status Error:', error);
        return {
          status: error.status || 500,
          data: error.data?.error || 'Failed to check payment status'
        };
      },

      providesTags: ['PaymentStatus']
    })
  })
});

// Redux state slice
const EventRegistrationSlice = createSlice({
  name: 'eventRegistration',
  initialState,
  reducers: {
    setRegistrationData: (state, action) => {
      console.log('Setting registrationData:', action.payload);
      state.registrationData = action.payload;
    },
    setPaymentStatus: (state, action) => {
      console.log('Setting paymentStatus:', action.payload);
      state.paymentStatus = action.payload;
    },
    resetRegistration: (state) => {
      console.log('Resetting registration state');
      state.registrationData = null;
      state.paymentStatus = null;
    }
  }
});

export const { setRegistrationData, setPaymentStatus, resetRegistration } =
  EventRegistrationSlice.actions;

export default EventRegistrationSlice.reducer;

export const { useRegisterEventMutation, useCheckPaymentStatusQuery } = eventRegistrationApiSlice;
