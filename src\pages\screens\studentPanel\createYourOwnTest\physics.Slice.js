import { createOwnTestPhysicsApi } from '../../../../redux/api/api';

export const createOwnTestPhysicsApiSlice = createOwnTestPhysicsApi.injectEndpoints({
  endpoints: (builder) => ({
    createYourOwnTestPhysicsStartTest: builder.mutation({
      query: (body) => ({
        url: '/start-test-physics',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Create Your Own Test Physics Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CreateOwnTestPhysics']
    }),
    createYourOwnTestPhysicsSubmitTest: builder.mutation({
      query: (body) => ({
        url: '/evaluate-test-physics',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Create Your Own Test Physics Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CreateOwnTestPhysics']
    })
  })
});
export const {
  useCreateYourOwnTestPhysicsStartTestMutation,
  useCreateYourOwnTestPhysicsSubmitTestMutation
} = createOwnTestPhysicsApiSlice;
