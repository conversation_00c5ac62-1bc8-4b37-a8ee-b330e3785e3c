import { mentorApi } from '../../../../redux/api/api';

export const addMentorSlice = mentorApi.injectEndpoints({
  endpoints: (builder) => ({
    directorMentorDashboardService: builder.query({
      query: (body) => ({
        url: '/mentor-dashboard',
        method: 'GET',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log(' Mentor Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['mentorApi']
    })
  })
});

export const { useDirectorMentorDashboardServiceQuery } = addMentorSlice;
