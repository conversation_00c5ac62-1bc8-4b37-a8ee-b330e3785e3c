importScripts('https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js');

const firebaseConfig = {
  apiKey: 'AIzaSyDT7404eSoYJCMNtgY2xd_ZqEhzjAA2eUo',
  authDomain: 'sasthra-3a69e.firebaseapp.com',
  projectId: 'sasthra-3a69e',
  storageBucket: 'sasthra-3a69e.firebasestorage.app',
  messagingSenderId: '119837146788',
  appId: '1:119837146788:web:6f3c60365aadd3ad677031',
  measurementId: 'G-FYJXQ4S2VT'
};

firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  console.log('Background Message:', payload);
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/sasthra_logo.png' // Updated to reference the logo in the public folder
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

// ===== STREAMING SERVICE WORKER FUNCTIONALITY =====

// Streaming state management
let streamingState = {
  isActive: false,
  sessionId: null,
  livekitToken: null,
  livekitUrl: null,
  roomName: null,
  connectionState: 'disconnected',
  lastHeartbeat: null,
  reconnectAttempts: 0,
  maxReconnectAttempts: 10,
  keepAliveInterval: null,
  connectionCheckInterval: null,
  tokenRefreshInterval: null,
  wakeLock: null
};

// WebSocket connection for backend communication
let backendWebSocket = null;
let wsReconnectAttempts = 0;
const maxWsReconnectAttempts = 5;

// Keep-alive and health monitoring
const HEARTBEAT_INTERVAL = 30000; // 30 seconds
const CONNECTION_CHECK_INTERVAL = 60000; // 60 seconds
const TOKEN_REFRESH_INTERVAL = 45 * 60 * 1000; // 45 minutes

console.log('🔧 Streaming Service Worker loaded');

// Listen for messages from main thread
self.addEventListener('message', async (event) => {
  const { type, data } = event.data;

  console.log('📨 SW received message:', type, data);

  switch (type) {
    case 'START_BACKGROUND_STREAMING':
      await startBackgroundStreaming(data);
      break;

    case 'STOP_BACKGROUND_STREAMING':
      await stopBackgroundStreaming();
      break;

    case 'UPDATE_STREAMING_CONFIG':
      updateStreamingConfig(data);
      break;

    case 'PING':
      sendMessageToClient({ type: 'PONG', timestamp: Date.now() });
      break;

    case 'GET_STREAMING_STATUS':
      sendStreamingStatus();
      break;

    default:
      console.log('❓ Unknown message type:', type);
  }
});

// Start background streaming
async function startBackgroundStreaming(config) {
  try {
    console.log('🚀 Starting background streaming...', config);

    streamingState = {
      ...streamingState,
      isActive: true,
      sessionId: config.sessionId,
      livekitToken: config.livekitToken,
      livekitUrl: config.livekitUrl,
      roomName: config.roomName,
      connectionState: 'connecting',
      lastHeartbeat: Date.now(),
      reconnectAttempts: 0
    };

    // Start keep-alive mechanisms
    startKeepAlive();

    // Start connection monitoring
    startConnectionMonitoring();

    // Start token refresh
    startTokenRefresh();

    // Connect to backend WebSocket for stream monitoring
    await connectToBackendWebSocket();

    // Request wake lock if available
    await requestWakeLock();

    streamingState.connectionState = 'connected';

    sendMessageToClient({
      type: 'BACKGROUND_STREAMING_STARTED',
      status: 'success',
      state: streamingState
    });

    console.log('✅ Background streaming started successfully');

  } catch (error) {
    console.error('❌ Failed to start background streaming:', error);

    streamingState.connectionState = 'failed';

    sendMessageToClient({
      type: 'BACKGROUND_STREAMING_ERROR',
      error: error.message,
      state: streamingState
    });
  }
}

// Stop background streaming
async function stopBackgroundStreaming() {
  try {
    console.log('🛑 Stopping background streaming...');

    streamingState.isActive = false;
    streamingState.connectionState = 'disconnected';

    // Clear all intervals
    clearKeepAlive();
    clearConnectionMonitoring();
    clearTokenRefresh();

    // Disconnect backend WebSocket
    if (backendWebSocket) {
      backendWebSocket.close();
      backendWebSocket = null;
    }

    // Release wake lock
    await releaseWakeLock();

    // Reset state
    streamingState = {
      ...streamingState,
      isActive: false,
      sessionId: null,
      livekitToken: null,
      livekitUrl: null,
      roomName: null,
      connectionState: 'disconnected',
      reconnectAttempts: 0
    };

    sendMessageToClient({
      type: 'BACKGROUND_STREAMING_STOPPED',
      state: streamingState
    });

    console.log('✅ Background streaming stopped');

  } catch (error) {
    console.error('❌ Error stopping background streaming:', error);
  }
}

// Update streaming configuration
function updateStreamingConfig(config) {
  console.log('🔄 Updating streaming config:', config);

  if (config.livekitToken) {
    streamingState.livekitToken = config.livekitToken;
  }

  if (config.livekitUrl) {
    streamingState.livekitUrl = config.livekitUrl;
  }

  if (config.sessionId) {
    streamingState.sessionId = config.sessionId;
  }

  sendMessageToClient({
    type: 'STREAMING_CONFIG_UPDATED',
    state: streamingState
  });
}

// Keep-alive mechanism
function startKeepAlive() {
  if (streamingState.keepAliveInterval) {
    clearInterval(streamingState.keepAliveInterval);
  }

  streamingState.keepAliveInterval = setInterval(() => {
    if (!streamingState.isActive) return;

    console.log('💓 Sending keep-alive heartbeat');

    streamingState.lastHeartbeat = Date.now();

    // Send heartbeat to main thread
    sendMessageToClient({
      type: 'HEARTBEAT',
      timestamp: streamingState.lastHeartbeat,
      state: streamingState
    });

    // Send heartbeat to backend if WebSocket is connected
    if (backendWebSocket && backendWebSocket.readyState === WebSocket.OPEN) {
      backendWebSocket.send(JSON.stringify({
        type: 'heartbeat',
        sessionId: streamingState.sessionId,
        timestamp: streamingState.lastHeartbeat
      }));
    }

  }, HEARTBEAT_INTERVAL);

  console.log('✅ Keep-alive started');
}

function clearKeepAlive() {
  if (streamingState.keepAliveInterval) {
    clearInterval(streamingState.keepAliveInterval);
    streamingState.keepAliveInterval = null;
    console.log('🛑 Keep-alive stopped');
  }
}

// Connection monitoring
function startConnectionMonitoring() {
  if (streamingState.connectionCheckInterval) {
    clearInterval(streamingState.connectionCheckInterval);
  }

  streamingState.connectionCheckInterval = setInterval(async () => {
    if (!streamingState.isActive) return;

    console.log('🔍 Checking connection health...');

    const now = Date.now();
    const timeSinceLastHeartbeat = now - (streamingState.lastHeartbeat || 0);

    // Check if connection is stale
    if (timeSinceLastHeartbeat > HEARTBEAT_INTERVAL * 2) {
      console.log('⚠️ Connection appears stale, attempting recovery...');
      await attemptConnectionRecovery();
    }

    // Check backend WebSocket connection
    if (!backendWebSocket || backendWebSocket.readyState !== WebSocket.OPEN) {
      console.log('🔄 Backend WebSocket disconnected, reconnecting...');
      await connectToBackendWebSocket();
    }

    // Send status update to main thread
    sendMessageToClient({
      type: 'CONNECTION_STATUS',
      state: streamingState,
      timeSinceLastHeartbeat
    });

  }, CONNECTION_CHECK_INTERVAL);

  console.log('✅ Connection monitoring started');
}

function clearConnectionMonitoring() {
  if (streamingState.connectionCheckInterval) {
    clearInterval(streamingState.connectionCheckInterval);
    streamingState.connectionCheckInterval = null;
    console.log('🛑 Connection monitoring stopped');
  }
}

// Token refresh mechanism
function startTokenRefresh() {
  if (streamingState.tokenRefreshInterval) {
    clearInterval(streamingState.tokenRefreshInterval);
  }

  streamingState.tokenRefreshInterval = setInterval(async () => {
    if (!streamingState.isActive || !streamingState.sessionId) return;

    console.log('🔄 Refreshing LiveKit token...');

    try {
      const response = await fetch('https://sasthra.in/api/enhanced-stream/refresh-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: streamingState.sessionId,
          room_name: streamingState.roomName
        })
      });

      if (response.ok) {
        const data = await response.json();
        streamingState.livekitToken = data.livekit_token;

        // Notify main thread of token update
        sendMessageToClient({
          type: 'TOKEN_REFRESHED',
          token: data.livekit_token,
          state: streamingState
        });

        console.log('✅ LiveKit token refreshed successfully');
      } else {
        console.warn('⚠️ Failed to refresh token:', response.status);
      }
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
    }

  }, TOKEN_REFRESH_INTERVAL);

  console.log('✅ Token refresh started');
}

function clearTokenRefresh() {
  if (streamingState.tokenRefreshInterval) {
    clearInterval(streamingState.tokenRefreshInterval);
    streamingState.tokenRefreshInterval = null;
    console.log('🛑 Token refresh stopped');
  }
}

// Backend WebSocket connection for stream monitoring
async function connectToBackendWebSocket() {
  try {
    if (backendWebSocket && backendWebSocket.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    console.log('🔗 Connecting to backend WebSocket...');

    const wsUrl = 'wss://sasthra.in/ws/streaming';
    backendWebSocket = new WebSocket(wsUrl);

    backendWebSocket.onopen = () => {
      console.log('✅ Backend WebSocket connected');
      wsReconnectAttempts = 0;

      // Send initial connection message
      backendWebSocket.send(JSON.stringify({
        type: 'streaming_monitor_connect',
        sessionId: streamingState.sessionId,
        timestamp: Date.now()
      }));
    };

    backendWebSocket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        handleBackendWebSocketMessage(message);
      } catch (error) {
        console.error('❌ Error parsing WebSocket message:', error);
      }
    };

    backendWebSocket.onerror = (error) => {
      console.error('❌ Backend WebSocket error:', error);
    };

    backendWebSocket.onclose = (event) => {
      console.log('🔌 Backend WebSocket disconnected:', event.code, event.reason);

      // Attempt reconnection if streaming is still active
      if (streamingState.isActive && wsReconnectAttempts < maxWsReconnectAttempts) {
        wsReconnectAttempts++;
        console.log(`🔄 Attempting WebSocket reconnection (${wsReconnectAttempts}/${maxWsReconnectAttempts})...`);

        setTimeout(() => {
          connectToBackendWebSocket();
        }, 2000 * wsReconnectAttempts); // Exponential backoff
      }
    };

  } catch (error) {
    console.error('❌ Failed to connect to backend WebSocket:', error);
  }
}

// Handle messages from backend WebSocket
function handleBackendWebSocketMessage(message) {
  console.log('📨 Backend WebSocket message:', message);

  switch (message.type) {
    case 'streaming_status':
      // Update streaming state based on backend status
      if (message.status === 'disconnected' && streamingState.isActive) {
        console.log('⚠️ Backend reports stream disconnected, attempting recovery...');
        attemptConnectionRecovery();
      }
      break;

    case 'force_reconnect':
      console.log('🔄 Backend requesting force reconnect...');
      sendMessageToClient({
        type: 'FORCE_RECONNECT_REQUIRED',
        reason: message.reason
      });
      break;

    case 'token_expired':
      console.log('🔄 Backend reports token expired, refreshing...');
      // Trigger immediate token refresh
      if (streamingState.tokenRefreshInterval) {
        clearInterval(streamingState.tokenRefreshInterval);
        startTokenRefresh();
      }
      break;

    default:
      console.log('❓ Unknown backend message type:', message.type);
  }
}

// Connection recovery mechanism
async function attemptConnectionRecovery() {
  if (streamingState.reconnectAttempts >= streamingState.maxReconnectAttempts) {
    console.error('❌ Max reconnection attempts reached');

    sendMessageToClient({
      type: 'CONNECTION_RECOVERY_FAILED',
      state: streamingState
    });

    return;
  }

  streamingState.reconnectAttempts++;
  streamingState.connectionState = 'reconnecting';

  console.log(`🔄 Attempting connection recovery (${streamingState.reconnectAttempts}/${streamingState.maxReconnectAttempts})...`);

  try {
    // Notify main thread to attempt reconnection
    sendMessageToClient({
      type: 'ATTEMPT_RECONNECTION',
      attempt: streamingState.reconnectAttempts,
      maxAttempts: streamingState.maxReconnectAttempts,
      state: streamingState
    });

    // Reset heartbeat
    streamingState.lastHeartbeat = Date.now();

    // Reconnect backend WebSocket
    await connectToBackendWebSocket();

    streamingState.connectionState = 'connected';

    console.log('✅ Connection recovery successful');

  } catch (error) {
    console.error('❌ Connection recovery failed:', error);

    streamingState.connectionState = 'failed';

    // Retry after delay
    setTimeout(() => {
      if (streamingState.isActive) {
        attemptConnectionRecovery();
      }
    }, 5000 * streamingState.reconnectAttempts); // Exponential backoff
  }
}

// Wake Lock management
async function requestWakeLock() {
  try {
    if ('wakeLock' in navigator) {
      streamingState.wakeLock = await navigator.wakeLock.request('screen');
      console.log('✅ Wake Lock acquired in service worker');

      streamingState.wakeLock.addEventListener('release', () => {
        console.log('🔓 Wake Lock released');
        streamingState.wakeLock = null;
      });

      sendMessageToClient({
        type: 'WAKE_LOCK_ACQUIRED',
        state: streamingState
      });
    } else {
      console.warn('⚠️ Wake Lock API not supported');
    }
  } catch (error) {
    console.error('❌ Failed to acquire Wake Lock:', error);
  }
}

async function releaseWakeLock() {
  try {
    if (streamingState.wakeLock) {
      await streamingState.wakeLock.release();
      streamingState.wakeLock = null;
      console.log('✅ Wake Lock released');

      sendMessageToClient({
        type: 'WAKE_LOCK_RELEASED',
        state: streamingState
      });
    }
  } catch (error) {
    console.error('❌ Failed to release Wake Lock:', error);
  }
}

// Utility functions
function sendMessageToClient(message) {
  self.clients.matchAll().then(clients => {
    clients.forEach(client => {
      client.postMessage(message);
    });
  });
}

function sendStreamingStatus() {
  sendMessageToClient({
    type: 'STREAMING_STATUS',
    state: streamingState,
    timestamp: Date.now()
  });
}

// Handle service worker lifecycle events
self.addEventListener('install', (event) => {
  console.log('🔧 Streaming Service Worker installing...');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('✅ Streaming Service Worker activated');
  event.waitUntil(self.clients.claim());
});

// Handle page visibility changes
self.addEventListener('visibilitychange', () => {
  if (streamingState.isActive) {
    console.log('👁️ Page visibility changed, maintaining stream...');

    // Ensure keep-alive continues
    if (!streamingState.keepAliveInterval) {
      startKeepAlive();
    }

    // Check connection health
    if (!streamingState.connectionCheckInterval) {
      startConnectionMonitoring();
    }

    sendMessageToClient({
      type: 'VISIBILITY_CHANGE_HANDLED',
      state: streamingState
    });
  }
});

// Handle network status changes
self.addEventListener('online', () => {
  if (streamingState.isActive) {
    console.log('🌐 Network back online, checking stream health...');
    attemptConnectionRecovery();
  }
});

self.addEventListener('offline', () => {
  if (streamingState.isActive) {
    console.log('📴 Network offline, stream may be affected');

    sendMessageToClient({
      type: 'NETWORK_OFFLINE',
      state: streamingState
    });
  }
});

// Periodic health check (runs every 5 minutes)
setInterval(() => {
  if (streamingState.isActive) {
    console.log('🏥 Periodic health check...');

    const now = Date.now();
    const timeSinceLastHeartbeat = now - (streamingState.lastHeartbeat || 0);

    // If no heartbeat for more than 2 minutes, something is wrong
    if (timeSinceLastHeartbeat > 120000) {
      console.log('⚠️ No heartbeat for 2+ minutes, forcing recovery...');
      attemptConnectionRecovery();
    }

    sendMessageToClient({
      type: 'HEALTH_CHECK',
      state: streamingState,
      timeSinceLastHeartbeat
    });
  }
}, 300000); // 5 minutes

console.log('🚀 Streaming Service Worker fully initialized');
