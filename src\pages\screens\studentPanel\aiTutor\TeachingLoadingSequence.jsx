import { AnimatePresence, motion, useReducedMotion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Ruler,
  FlaskConical,
  Atom,
  Calculator,
  Brain,
  TestTube2,
  LandPlot,
  BookText,
  Globe2,
  Sigma
} from 'lucide-react';

const TeachingLoadingSequence = ({ showLaunchSequence, subject }) => {
  const shouldReduceMotion = useReducedMotion();

  // Chalkboard animation variants
  const chalkboardVariants = {
    animate: {
      scale: shouldReduceMotion ? 1 : [1, 1.02, 1],
      transition: { duration: 4, repeat: Infinity }
    }
  };

  // Chalk writing animation
  const chalkTextVariants = {
    initial: { pathLength: 0 },
    animate: {
      pathLength: 1,
      transition: { duration: 2, ease: 'easeInOut' }
    }
  };

  // Floating school items
  const floatingItemVariants = (delay) => ({
    animate: shouldReduceMotion
      ? {}
      : {
          y: [0, -15, 0],
          rotate: [0, 5, -5, 0],
          transition: {
            duration: 3 + Math.random() * 2,
            repeat: Infinity,
            delay: delay * 0.3
          }
        }
  });

  // Falling chalk dust
  const chalkDustVariants = {
    animate: shouldReduceMotion
      ? {}
      : {
          y: [0, 100],
          opacity: [1, 0],
          transition: {
            duration: 2,
            repeat: Infinity,
            delay: Math.random() * 1.5
          }
        }
  };

  // Subject-specific icons
  const subjectIcons = [
    { icon: <Atom size={32} className="text-blue-300" />, name: 'Physics' },
    { icon: <FlaskConical size={32} className="text-teal-300" />, name: 'Chemistry' },
    { icon: <Calculator size={32} className="text-purple-300" />, name: 'Math' },
    { icon: <Brain size={32} className="text-pink-300" />, name: 'Biology' },
    { icon: <LandPlot size={32} className="text-green-300" />, name: 'Geography' },
    { icon: <BookText size={32} className="text-amber-300" />, name: 'Literature' },
    { icon: <Globe2 size={32} className="text-indigo-300" />, name: 'History' },
    { icon: <Sigma size={32} className="text-red-300" />, name: 'Statistics' }
  ];

  return (
    <AnimatePresence>
      {showLaunchSequence && (
        <motion.div
          className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-blue-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.4 }}>
          {/* Chalkboard background with blue tint */}
          <motion.div
            className="absolute w-full h-full bg-[#1a2a3a] opacity-95"
            variants={chalkboardVariants}
            animate="animate">
            {/* Chalkboard texture */}
            <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2IiBoZWlnaHQ9IjYiPgo8cmVjdCB3aWR0aD0iNiIgaGVpZ2h0PSI2IiBmaWxsPSIjMWEyYTNhIi8+CjxwYXRoIGQ9Ik0wIDZMNiAwWiIgc3Ryb2tlLXdpZHRoPSIxIiBzdHJva2U9IiMxNDI0MzUiLz4KPC9zdmc+')] opacity-30" />
          </motion.div>
          <motion.svg
            className="absolute top-12 w-64 h-16"
            viewBox="0 0 200 50"
            initial="initial"
            animate="animate">
            <motion.text
              x="100"
              y="30"
              textAnchor="middle"
              fill="white"
              fontFamily="'Comic Sans MS', cursive"
              fontSize="16"
              variants={chalkTextVariants}>
              {subject || 'Teaching'} in Progress
            </motion.text>
          </motion.svg>
          <div className="relative w-full mt-5 max-w-2xl h-96 flex items-center justify-center">
            {/* Chalkboard frame with teacher blue */}
            <div className="absolute inset-0 border-8 border-[#2a5a8a] rounded-lg shadow-lg" />
            <div className="absolute inset-4 border-4 border-[#3a7aba] rounded" />

            {/* Hand-drawn title in white chalk */}

            {/* Floating subject icons */}
            {subjectIcons.map((item, i) => (
              <motion.div
                key={`subject-${i}`}
                className="absolute"
                style={{
                  left: `${20 + (i % 4) * 20}%`,
                  top: `${30 + Math.floor(i / 4) * 30}%`
                }}
                variants={floatingItemVariants(i)}
                animate="animate">
                {item.icon}
                <motion.div
                  className="absolute -bottom-6 left-0 right-0 text-center text-white text-xs"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: [0, 1, 0] }}
                  transition={{ duration: 3, repeat: Infinity, delay: i * 0.5 }}>
                  {item.name}
                </motion.div>
              </motion.div>
            ))}

            {/* Chalk dust particles */}

            {/* Progress bar with teacher blue */}
            <div className="absolute bottom-20 w-64 h-4 bg-[#2a3a4a] rounded-full overflow-hidden">
              <motion.div
                className="h-full bg-[#4a9afa] rounded-full"
                initial={{ width: 0 }}
                animate={{ width: ['0%', '30%', '70%', '100%'] }}
                transition={{ duration: 3, repeat: Infinity, repeatDelay: 0.5 }}
              />
            </div>

            {/* Teacher-style message */}
            <motion.div
              className="absolute bottom-8 text-center text-white font-comic"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}>
              <p className="text-lg" style={{ textShadow: '0 0 4px rgba(0,0,0,0.5)' }}>
                {
                  [
                    "Preparing today's lesson...",
                    'Grading assignments...',
                    'Creating engaging content...',
                    'Aligning lesson with curriculum standards...',
                    `Preparing today's learning objectives...`,
                    'Compiling resources...'
                  ][Math.floor(Math.random() * 6)]
                }
              </p>
            </motion.div>
          </div>

          {/* Teacher's desk with blue accents */}
          <div className="absolute bottom-0 w-full h-20 bg-[#3a4a5a]">
            <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2IiBoZWlnaHQ9IjYiPgo8cmVjdCB3aWR0aD0iNiIgaGVpZ2h0PSI2IiBmaWxsPSIjM2E0YTVhIi8+CjxwYXRoIGQ9Ik0wIDZMNiAwWiIgc3Ryb2tlLXdpZHRoPSIwLjUiIHN0cm9rZT0iIzJhM2E0YSIvPgo8L3N2Zz4=')] opacity-40" />
            <div className="absolute top-0 w-full h-2 bg-[#4a9afa]"></div>

            {/* Desk items */}
            <div className="absolute flex space-x-4 bottom-4 left-1/4">
              <motion.div
                className="w-8 h-8 bg-white rounded-sm flex items-center justify-center"
                animate={{
                  rotate: [0, 2, -2, 0],
                  y: [0, -2, 0]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  delay: 0.5
                }}>
                <BookOpen size={16} className="text-blue-800" />
              </motion.div>

              <motion.div
                className="w-8 h-2 bg-amber-100 rounded-full"
                animate={{
                  rotate: [0, 5, -5, 0]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: 0.8
                }}
              />

              <motion.div
                className="w-8 h-8 bg-white rounded-full flex items-center justify-center"
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  delay: 0.2
                }}>
                <Globe2 size={16} className="text-blue-800" />
              </motion.div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default TeachingLoadingSequence;
