import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  GraduationCap,
  BookOpen,
  ChevronDown,
  User,
  Tag,
  School,
  Phone,
  Target,
  RefreshCw
} from 'lucide-react';
import {
  useLazyGetCentreCounselorDashboardQuery,
  useLazyGetListFacultyQuery,
  useLazyGetListKotaTeachersQuery,
  useLazyGetListStudentsQuery
} from '../centreCounselorDashboard/centreCounselorDashboard.slice';

const getSessionUser = () => {
  return {
    name: sessionStorage.getItem('name') || 'Unknown',
    role: sessionStorage.getItem('role') || 'Unknown',
    centername: sessionStorage.getItem('centername') || 'Unknown',
    phone: sessionStorage.getItem('phone') || 'Unknown',
    centercode: sessionStorage.getItem('centercode') || 'Unknown'
  };
};

const Overview = () => {
  const [expanded, setExpanded] = useState({
    students: false,
    faculty: false,
    kotaTeachers: false
  });

  // Initialize lazy queries
  const [triggerCentreInfo, { data: centreInfo, isLoading: centreLoading, error: centreError }] =
    useLazyGetCentreCounselorDashboardQuery();
  const [
    triggerStudents,
    { data: studentsData, isLoading: studentsLoading, error: studentsError }
  ] = useLazyGetListStudentsQuery();
  const [triggerFaculty, { data: facultyData, isLoading: facultyLoading, error: facultyError }] =
    useLazyGetListFacultyQuery();
  const [
    triggerKotaTeachers,
    { data: kotaTeachersData, isLoading: kotaTeachersLoading, error: kotaTeachersError }
  ] = useLazyGetListKotaTeachersQuery();

  // Debug: Log kotaTeachersData to inspect response structure
  useEffect(() => {
    if (kotaTeachersData) {
      console.log('Kota Teachers Raw Data:', kotaTeachersData);
    }
  }, [kotaTeachersData]);

  // Fetch data on component mount
  useEffect(() => {
    triggerCentreInfo();
    triggerStudents();
    triggerFaculty();
    triggerKotaTeachers();
  }, [triggerCentreInfo, triggerStudents, triggerFaculty, triggerKotaTeachers]);

  const toggleExpand = (category) => {
    setExpanded((prev) => ({ ...prev, [category]: !prev[category] }));
  };

  // Map user data (use API data if available, fallback to sessionStorage)
  const user = centreInfo?.data?.user || getSessionUser();

  // Map data arrays (handle different response structures)
  const students = studentsData?.data?.students || studentsData?.students || studentsData || [];
  const faculty = facultyData?.data?.faculty || facultyData?.faculty || facultyData || [];
  const kotaTeachers =
    kotaTeachersData?.data?.kotaTeachers ||
    kotaTeachersData?.kota_teachers ||
    kotaTeachersData?.teachers || // Additional fallback for common naming
    kotaTeachersData?.kotaTeachersList || // Another possible structure
    kotaTeachersData ||
    [];

  const getPreview = (data, limit = 3) => {
    if (!data || data.length === 0) {
      return <p className="text-sm text-gray-700">No one is there</p>;
    }
    return data.slice(0, limit).map((item, index) => (
      <motion.p
        key={index}
        className="text-sm text-gray-700"
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: index * 0.1 }}
      >
        {item.first_name || item.name || 'Unknown'} {item.last_name || ''}
      </motion.p>
    ));
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    },
    hover: {
      scale: 1.02,
      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="space-y-8 p-4"
    >
      {/* User Details Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="rounded-xl p-6 bg-gradient-to-r from-[var(--color-counselor)]/5 to-white backdrop-blur-sm border border-gray-200/50 shadow-sm"
      >
        <motion.h2
          className="text-2xl font-bold mb-6 text-[var(--color-counselor)] flex items-center gap-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <User className="w-6 h-6" />
          User Details
        </motion.h2>

        {centreLoading && <p className="text-gray-500">Loading user details...</p>}
        {centreError && (
          <div className="flex flex-col gap-2">
            <p className="text-red-500">
              Error loading user details: {centreError.data?.message || 'Unknown error'}
            </p>
            <motion.button
              onClick={() => triggerCentreInfo()}
              className="flex items-center gap-1 text-sm text-[var(--color-counselor)] hover:text-gray-700"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <RefreshCw className="w-4 h-4" />
              Retry
            </motion.button>
          </div>
        )}

        {!centreLoading && !centreError && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-base">
            <div className="space-y-3">
              <motion.div
                className="flex items-center gap-3"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
              >
                <School className="w-5 h-5 text-[var(--color-counselor)]" />
                <p>
                  <strong>Center Name:</strong> {user.centername}
                </p>
              </motion.div>

              <motion.div
                className="flex items-center gap-3"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
              >
                <Tag className="w-5 h-5 text-[var(--color-counselor)]" />
                <p>
                  <strong>Role:</strong> {user.role}
                </p>
              </motion.div>
            </div>

            <div className="space-y-3">
              <motion.div
                className="flex items-center gap-3"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 }}
              >
                <Phone className="w-5 h-5 text-[var(--color-counselor)]" />
                <p>
                  <strong>Phone:</strong> {user.phone}
                </p>
              </motion.div>

              <motion.div
                className="flex items-center gap-3"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.8 }}
              >
                <Target className="w-5 h-5 text-[var(--color-counselor)]" />
                <p>
                  <strong>Center Code:</strong> {user.centercode}
                </p>
              </motion.div>
            </div>
          </div>
        )}
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Students Card */}
        <motion.div
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          whileHover="hover"
          className="rounded-xl p-6 hover:cursor-pointer border border-gray-200/50 bg-white/80 backdrop-blur-sm shadow-sm relative overflow-hidden group"
        >
          <div className="absolute -right-6 -top-6 w-24 h-24 bg-[var(--color-counselor)]/10 rounded-full"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800">Total Students</h3>
              <motion.div whileHover={{ rotate: 15 }} whileTap={{ scale: 0.9 }}>
                <GraduationCap className="text-[var(--color-counselor)] w-6 h-6" />
              </motion.div>
            </div>

            {studentsLoading && <p className="text-gray-500">Loading students...</p>}
            {studentsError && (
              <div className="flex flex-col gap-2">
                <p className="text-red-500">
                  Error loading students: {studentsError.data?.message || 'Unknown error'}
                </p>
                <motion.button
                  onClick={() => triggerStudents()}
                  className="flex items-center gap-1 text-sm text-[var(--color-counselor)] hover:text-gray-700"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <RefreshCw className="w-4 h-4" />
                  Retry
                </motion.button>
              </div>
            )}

            {!studentsLoading && !studentsError && (
              <>
                <motion.p
                  className="text-4xl font-extrabold text-[var(--color-counselor)] mb-2"
                  initial={{ scale: 0.8 }}
                  animate={{ scale: 1 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  {students.length}
                </motion.p>

                {students.length > 0 && (
                  <motion.button
                    onClick={() => toggleExpand('students')}
                    className="flex items-center gap-1 text-[var(--color-counselor)] hover:curssor-pointer hover:text-gray-700 transition-colors"
                    whileHover={{ x: 3 }}
                  >
                    <span>Preview {expanded.students ? 'Hide' : 'Show'}</span>
                    <motion.span
                      animate={{ rotate: expanded.students ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <ChevronDown className="w-4 h-4" />
                    </motion.span>
                  </motion.button>
                )}

                <AnimatePresence>
                  {expanded.students && students.length > 0 && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="mt-4 space-y-1"
                    >
                      {getPreview(students)}
                    </motion.div>
                  )}
                  {!expanded.students && students.length === 0 && (
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-sm text-gray-700 mt-2"
                    >
                      No one is there
                    </motion.p>
                  )}
                </AnimatePresence>
              </>
            )}
          </div>
        </motion.div>

        {/* Faculty Card */}
        <motion.div
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          whileHover="hover"
          className="rounded-xl p-6 hover:cursor-pointer border border-gray-200/50 bg-white/80 backdrop-blur-sm shadow-sm relative overflow-hidden group"
        >
          <div className="absolute -right-6 -top-6 w-24 h-24 bg-[var(--color-counselor)]/10 rounded-full"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800">Faculty Members</h3>
              <motion.div whileHover={{ rotate: 15 }} whileTap={{ scale: 0.9 }}>
                <Users className="text-[var(--color-counselor)] w-6 h-6" />
              </motion.div>
            </div>

            {facultyLoading && <p className="text-gray-500">Loading faculty...</p>}
            {facultyError && (
              <div className="flex flex-col gap-2">
                <p className="text-red-500">
                  Error loading faculty: {facultyError.data?.message || 'Unknown error'}
                </p>
                <motion.button
                  onClick={() => triggerFaculty()}
                  className="flex items-center gap-1 text-sm text-[var(--color-counselor)] hover:text-gray-700"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <RefreshCw className="w-4 h-4" />
                  Retry
                </motion.button>
              </div>
            )}

            {!facultyLoading && !facultyError && (
              <>
                <motion.p
                  className="text-4xl font-extrabold text-[var(--color-counselor)] mb-2"
                  initial={{ scale: 0.8 }}
                  animate={{ scale: 1 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  {faculty.length}
                </motion.p>

                {faculty.length > 0 && (
                  <motion.button
                    onClick={() => toggleExpand('faculty')}
                    className="flex items-center gap-1 text-[var(--color-counselor)] hover:curssor-pointer hover:text-gray-700 transition-colors"
                    whileHover={{ x: 3 }}
                  >
                    <span>Preview {expanded.faculty ? 'Hide' : 'Show'}</span>
                    <motion.span
                      animate={{ rotate: expanded.faculty ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <ChevronDown className="w-4 h-4" />
                    </motion.span>
                  </motion.button>
                )}

                <AnimatePresence>
                  {expanded.faculty && faculty.length > 0 && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="mt-4 space-y-1"
                    >
                      {getPreview(faculty)}
                    </motion.div>
                  )}
                  {!expanded.faculty && faculty.length === 0 && (
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-sm text-gray-700 mt-2"
                    >
                      No one is there
                    </motion.p>
                  )}
                </AnimatePresence>
              </>
            )}
          </div>
        </motion.div>

        {/* Kota Teachers Card */}
        <motion.div
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          whileHover="hover"
          className="rounded-xl p-6 hover:cursor-pointer border border-gray-200/50 bg-white/80 backdrop-blur-sm shadow-sm relative overflow-hidden group"
        >
          <div className="absolute -right-6 -top-6 w-24 h-24 bg-[var(--color-counselor)]/10 rounded-full"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800">Kota Teachers</h3>
              <motion.div whileHover={{ rotate: 15 }} whileTap={{ scale: 0.9 }}>
                <BookOpen className="text-[var(--color-counselor)] w-6 h-6" />
              </motion.div>
            </div>

            {kotaTeachersLoading && <p className="text-gray-500">Loading Kota teachers...</p>}
            {kotaTeachersError && (
              <div className="flex flex-col gap-2">
                <p className="text-red-500">
                  Error loading Kota teachers: {kotaTeachersError.data?.message || 'Unknown error'}
                </p>
                <motion.button
                  onClick={() => triggerKotaTeachers()}
                  className="flex items-center gap-1 text-sm text-[var(--color-counselor)] hover:text-gray-700"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <RefreshCw className="w-4 h-4" />
                  Retry
                </motion.button>
              </div>
            )}

            {!kotaTeachersLoading && !kotaTeachersError && (
              <>
                <motion.p
                  className="text-4xl font-extrabold text-[var(--color-counselor)] mb-2"
                  initial={{ scale: 0.8 }}
                  animate={{ scale: 1 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  {kotaTeachers.length}
                </motion.p>

                {kotaTeachers.length > 0 && (
                  <motion.button
                    onClick={() => toggleExpand('kotaTeachers')}
                    className="flex items-center gap-1 text-[var(--color-counselor)] hover:curssor-pointer hover:text-gray-700 transition-colors"
                    whileHover={{ x: 3 }}
                  >
                    <span>Preview {expanded.kotaTeachers ? 'Hide' : 'Show'}</span>
                    <motion.span
                      animate={{ rotate: expanded.kotaTeachers ? 180 : 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <ChevronDown className="w-4 h-4" />
                    </motion.span>
                  </motion.button>
                )}

                <AnimatePresence>
                  {expanded.kotaTeachers && kotaTeachers.length > 0 && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="mt-4 space-y-1"
                    >
                      {getPreview(kotaTeachers)}
                    </motion.div>
                  )}
                  {!expanded.kotaTeachers && kotaTeachers.length === 0 && (
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-sm text-gray-700 mt-2"
                    >
                      No one is there
                    </motion.p>
                  )}
                </AnimatePresence>
              </>
            )}
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Overview;
