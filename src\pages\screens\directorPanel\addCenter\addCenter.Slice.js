import { directorApi } from '../../../../redux/api/api';
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  addCenterData: null,
  listCenterData: null,
  inboxData: null,
  studentDetailsData: null
};

export const addCenterSlice = directorApi.injectEndpoints({
  endpoints: (builder) => ({
    directorAddCenterService: builder.mutation({
      query: (body) => ({
        url: '/add-center',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Add Center Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providerTags: ['CenterInfo']
    }),
    directorListCenterService: builder.query({
      query: () => ({
        url: '/list-centers',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        if (response && Array.isArray(response.centers)) {
          return response.centers;
        }
        if (Array.isArray(response)) {
          return response;
        }
        if (response && Array.isArray(response.data)) {
          return response.data;
        }
        return [];
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providerTags: ['ListCenter']
    }),
    directorListCenterStudentsService: builder.query({
      query: (centerCode) => ({
        url: `/director_center/${centerCode}/students`,
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Center Students Response:', response);
        if (response && response.success && Array.isArray(response.data?.students)) {
          return response.data.students;
        }
        if (response && Array.isArray(response.students)) {
          return response.students;
        }
        if (response && Array.isArray(response.data)) {
          return response.data;
        }
        if (Array.isArray(response)) {
          return response;
        }
        return [];
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providerTags: ['CenterStudents']
    }),
    directorInboxService: builder.query({
      query: () => ({
        url: '/inbox',
        method: 'GET', // Fixed typo: 'methos' to 'method'
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Inbox Response:', response);
        if (response && Array.isArray(response.requests)) {
          return response.requests;
        }
        if (response && response.data && Array.isArray(response.data.requests)) {
          return response.data.requests;
        }
        if (response && Array.isArray(response.data)) {
          return response.data;
        }
        if (Array.isArray(response)) {
          return response;
        }
        return [];
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['Inbox'] // Corrected typo: 'providerTags' to 'providesTags'
    }),
    directorApproveStudentService: builder.mutation({
      query: (requestId) => ({
        url: '/approve-student',
        method: 'POST',
        body: { request_id: requestId }
      }),
      transformResponse: (response) => {
        console.log('Approve Student Response:', response);
        return {
          message: response.message || 'Student approved successfully',
          credentials: response.data || response
        };
      },
      transformErrorResponse: (error) => ({
        status: error.status,
        message: error.data?.message || 'Error approving student'
      }),
      invalidatesTags: ['Inbox']
    }),
    directorApproveFacultyService: builder.mutation({
      query: (requestId) => ({
        url: '/approve-faculty',
        method: 'POST',
        body: { request_id: requestId }
      }),
      transformResponse: (response) => {
        console.log('Approve Faculty Response:', response);
        return {
          message: response.message || 'Faculty approved successfully',
          credentials: response.data || response
        };
      },
      transformErrorResponse: (error) => ({
        status: error.status,
        message: error.data?.message || 'Error approving faculty'
      }),
      invalidatesTags: ['Inbox']
    }),
    directorDropRequestService: builder.mutation({
      query: (requestId) => ({
        url: '/inbox',
        method: 'POST',
        body: { request_id: requestId, action: 'drop' }
      }),
      transformResponse: (response) => {
        console.log('Drop Request Response:', response);
        return {
          message: response.message || 'Request dropped successfully'
        };
      },
      transformErrorResponse: (error) => ({
        status: error.status,
        message: error.data?.message || 'Error dropping request'
      }),
      invalidatesTags: ['Inbox']
    }),
    directorViewStudentDetailsService: builder.query({
      query: (studentId) => ({
        url: `/director_student/${studentId}/details`,
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('View Student Details Response:', response);
        if (response && response.success) {
          return response.data;
        }
        return response;
      },
      transformErrorResponse: (error) => ({
        status: error.status,
        message: error.data?.message || 'Error viewing student details'
      }),
      providesTags: ['StudentDetails']
    })
  })
});

const CenterSlice = createSlice({
  name: 'addCenter',
  initialState,
  reducers: {
    setAddCenterData: (state, action) => {
      state.addCenterData = action.payload;
    },
    setListCenterData: (state, action) => {
      state.listCenterData = action.payload;
    },
    setInboxData: (state, action) => {
      state.inboxData = action.payload;
    },
    setstudentDetailsData: (state, action) => {
      state.studentDetailsData = action.payload;
    }
  }
});

export const { setAddCenterData, setListCenterData, setInboxData, setstudentDetailsData } =
  CenterSlice.actions;

export default CenterSlice.reducer;

export const {
  useDirectorAddCenterServiceMutation,
  useDirectorListCenterServiceQuery,
  useDirectorListCenterStudentsServiceQuery,
  useDirectorInboxServiceQuery,
  useDirectorApproveStudentServiceMutation,
  useDirectorApproveFacultyServiceMutation,
  useDirectorDropRequestServiceMutation, // Added new export
  useDirectorViewStudentDetailsServiceQuery
} = addCenterSlice;
