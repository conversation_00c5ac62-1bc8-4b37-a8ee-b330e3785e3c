import React from 'react';
import { motion } from 'framer-motion';
import { Mail, FileText, ChevronUp } from 'lucide-react';

const sectionContents = {
  1: (
    <div>
      <p className="text-gray-700 leading-relaxed">
        At Sasthra, an AI-driven educational platform by{' '}
        <span className="font-semibold text-saffron-600">DataSpark AI Solutions</span>, we are
        committed to safeguarding your privacy. This Privacy Policy outlines how we collect, use,
        share, and protect your information when you engage with our platform at{' '}
        <a
          href="https://sasthra.in"
          className="text-indigo-500 hover:text-indigo-700 transition-colors duration-300 underline"
        >
          sasthra.in
        </a>
        , including our website, mobile applications, or educational services. By using our
        services, you consent to this policy. If you do not agree, please refrain from accessing our
        platform.
      </p>
    </div>
  ),
  2: (
    <div>
      <p className="text-gray-700 leading-relaxed">
        We collect the following information to enhance your learning experience:
      </p>
      <ul className="list-disc pl-6 mt-2 space-y-2">
        <li>
          <strong>Personal Information:</strong> Name, email address, phone number, address, date of
          birth, or identification details (e.g., Passport, Aadhaar) provided during registration or
          interaction with our AI services.
        </li>
        <li>
          <strong>Usage Data:</strong> Anonymous data such as IP address, browser type, device IDs,
          and interaction patterns to optimize our AI algorithms and educational content.
        </li>
        <li>
          <strong>Billing Information:</strong> Payment details processed through secure gateways
          and user preferences to personalize your learning journey.
        </li>
      </ul>
      <p className="text-gray-700 leading-relaxed mt-2">
        Data is collected with your consent, to fulfill contracts, comply with legal obligations, or
        support our legitimate educational interests.
      </p>
    </div>
  ),
  3: (
    <div>
      <p className="text-gray-700 leading-relaxed">Your information is used to:</p>
      <ul className="list-disc pl-6 mt-2 space-y-2">
        <li>Deliver personalized AI-driven educational content and insights.</li>
        <li>Provide customer support and respond to inquiries.</li>
        <li>Analyze usage to improve platform performance and learning outcomes.</li>
        <li>Send educational updates or promotional content (with your consent).</li>
        <li>
          Celebrate your achievements (e.g., certifications or course completions) with your
          permission.
        </li>
      </ul>
      <p className="text-gray-700 leading-relaxed mt-2">
        We retain data only as long as necessary for these purposes or to meet legal requirements.
      </p>
    </div>
  ),
  4: (
    <div>
      <p className="text-gray-700 leading-relaxed">
        We do not sell your data. DataSpark AI Solutions may share your information with:
      </p>
      <ul className="list-disc pl-6 mt-2 space-y-2">
        <li>
          Trusted service providers (e.g., cloud hosting, payment processors) under strict
          confidentiality agreements to support our platform.
        </li>
        <li>Legal authorities when required by law or to protect our rights and users.</li>
        <li>Educational or financial partners, with your consent, to offer tailored services.</li>
        <li>
          Third parties during corporate restructuring or fraud prevention, on a need-to-know basis.
        </li>
      </ul>
    </div>
  ),
  5: (
    <div>
      <p className="text-gray-700 leading-relaxed">
        We employ robust security measures, including 256-bit SSL encryption, firewalls, and access
        controls, to protect your data. However, no system is entirely risk-free, and you
        acknowledge this by using our platform. Report suspected breaches to{' '}
        <a
          href="mailto:<EMAIL>"
          className="text-indigo-500 hover:text-indigo-700 transition-colors duration-300 underline"
        >
          <EMAIL>
        </a>
        .
      </p>
    </div>
  ),
  6: (
    <div>
      <p className="text-gray-700 leading-relaxed">You have the right to:</p>
      <ul className="list-disc pl-6 mt-2 space-y-2">
        <li>Access or correct your personal information.</li>
        <li>Request deletion or a portable copy of your data.</li>
        <li>Opt out of marketing communications.</li>
      </ul>
      <p className="text-gray-700 leading-relaxed mt-2">
        To exercise these rights, contact us at{' '}
        <a
          href="mailto:<EMAIL>"
          className="text-indigo-500 hover:text-indigo-700 transition-colors duration-300 underline"
        >
          <EMAIL>
        </a>
        .
      </p>
    </div>
  ),
  7: (
    <div>
      <p className="text-gray-700 leading-relaxed">
        We use cookies and similar technologies to enhance your learning experience, track
        preferences, and analyze usage trends. You can disable cookies in your browser settings, but
        this may limit functionality. We do not store personal data in cookies.
      </p>
    </div>
  ),

  8: (
    <div>
      <p className="text-gray-700 leading-relaxed">
        We may update this Privacy Policy as our AI technology and educational services evolve.
        Changes will be posted on this page with an updated date. Your continued use of Sasthra's
        services indicates acceptance of the updated policy.
      </p>
    </div>
  ),
  9: (
    <div className="space-y-4">
      <p className="text-gray-700 leading-relaxed">
        For questions or concerns about this Privacy Policy, please contact:
      </p>
      <div className="flex items-center">
        <Mail className="w-5 h-5 mr-2 text-saffron-500" />
        <a
          href="mailto:<EMAIL>"
          className="text-indigo-500 hover:text-indigo-700 transition-colors duration-300 underline"
        >
          <EMAIL>
        </a>
      </div>
      <div className="flex items-center">
        <FileText className="w-5 h-5 mr-2 text-saffron-500" />
        <span>Sasthra (A DataSpark AI Solutions Company), Bangalore, India</span>
      </div>
      <p className="text-gray-700 leading-relaxed">
        <strong>Grievance Officer:</strong> Priya Sharma,{' '}
        <a
          href="mailto:<EMAIL>"
          className="text-indigo-500 hover:text-indigo-700 transition-colors duration-300 underline"
        >
          <EMAIL>
        </a>
      </p>
    </div>
  )
};

const SectionCard = ({ section, onClose }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ type: 'spring', damping: 20 }}
      className="relative bg-gradient-to-br from-white to-gray-50 rounded-2xl p-8 shadow-lg border border-gray-200/50 overflow-hidden"
    >
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-indigo-100/30 rounded-full -mr-16 -mt-16" />
      <div className="absolute bottom-0 left-0 w-48 h-48 bg-saffron-100/20 rounded-full -ml-24 -mb-24" />

      {/* Header with animated close button */}
      <div className="flex justify-between items-start mb-6 relative z-10">
        <div className="flex items-center">
          <motion.div
            whileHover={{ rotate: 15, scale: 1.1 }}
            className="p-3 rounded-xl bg-gradient-to-br from-indigo-100 to-indigo-50 text-indigo-500 shadow-sm mr-4"
          >
            {section.icon}
          </motion.div>
          <div>
            <h2 className="text-2xl font-bold text-gray-800">{section.title}</h2>
            <div className="w-16 h-1 bg-gradient-to-r from-indigo-400 to-saffron-400 rounded-full mt-2" />
          </div>
        </div>
        <motion.button
          onClick={onClose}
          whileHover={{ scale: 1.1, rotate: 90 }}
          whileTap={{ scale: 0.9 }}
          className="p-2 rounded-full bg-white shadow-sm text-gray-500 hover:text-indigo-600 transition-colors"
        >
          <ChevronUp className="w-5 h-5" />
        </motion.button>
      </div>

      {/* Content with animated list items */}
      <motion.div
        className="relative z-10 text-gray-700 leading-relaxed space-y-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.1 }}
      >
        {sectionContents[section.id]}
      </motion.div>

      {/* Footer with decorative elements */}
      <div className="mt-8 pt-6 border-t border-gray-200/50 flex justify-between items-center relative z-10">
        <div className="flex space-x-2">
          {[1, 2, 3].map((dot) => (
            <motion.div
              key={dot}
              className="w-2 h-2 rounded-full bg-gray-300"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1.5, repeat: Infinity, delay: dot * 0.2 }}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default React.memo(SectionCard);
