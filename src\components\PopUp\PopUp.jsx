import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { motion, AnimatePresence } from 'framer-motion';
import { Info, X, Sparkles } from 'lucide-react';
import Button from '../Field/Button';

const PopUp = ({
  title,
  width = 'md',
  children,
  isDelete = false,
  isEdit = false,
  isDisabled = false,
  isScrollable = false,
  post,
  onClose
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isHoveringClose, setIsHoveringClose] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300);
  };

  const widthClasses = {
    sm: 'w-[28rem]',
    md: 'w-[40rem]',
    lg: 'w-[44rem]',
    xl: 'w-[72rem]'
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-gradient-to-br from-indigo-900/30 to-purple-900/30 backdrop-blur-lg flex items-center justify-center z-50 p-4"
        >
          {/* Floating particles background */}
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0 }}
                animate={{
                  opacity: [0, 0.6, 0],
                  x: Math.random() * 400 - 200,
                  y: Math.random() * 400 - 200
                }}
                transition={{
                  duration: Math.random() * 10 + 10,
                  repeat: Infinity,
                  repeatType: 'reverse'
                }}
                className="absolute rounded-full bg-white/10"
                style={{
                  width: Math.random() * 6 + 2 + 'px',
                  height: Math.random() * 6 + 2 + 'px',
                  left: '50%',
                  top: '50%'
                }}
              />
            ))}
          </div>

          <motion.div
            initial={{ scale: 0.95, y: 20 }}
            animate={{ scale: 1, y: 0 }}
            exit={{ scale: 0.95, y: 20 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            className={`relative bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-2xl max-h-[90vh] overflow-hidden border border-white/20 ${widthClasses[width]}`}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Glow effect */}
            <div className="absolute inset-0 bg-radial-gradient from-blue-400/10 to-transparent pointer-events-none" />

            {/* Header with glass morphism effect */}
            <div className="p-6 pb-4 flex justify-between items-center sticky top-0 z-10 bg-gray-900 border-b border-indigo-400/30">
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{
                    boxShadow: [
                      '0 0 5px #3b82f6',
                      '0 0 10px #3b82f6',
                      '0 0 15px #3b82f6',
                      '0 0 5px #3b82f6'
                    ]
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="p-2 rounded-lg border border-[var(--color-student)] bg-gray-800"
                >
                  <Info className="text-white" size={24} />
                </motion.div>

                <motion.h2
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-cyan-300 tracking-tight"
                >
                  {title}
                </motion.h2>
              </div>

              <motion.button
                whileHover={{ scale: 1.1 }}
                onClick={handleClose}
                className="p-2 rounded-lg border border-red-400/30 bg-gray-800 hover:cursor-pointer hover:bg-red-500/10 transition-colors group"
              >
                <X
                  className="text-red-400 group-hover:text-red-300 hover:cursor-pointer transition-colors"
                  size={20}
                />
                <span className="absolute inset-0 hover:cursor-pointer rounded-lg bg-red-500/0 group-hover:bg-red-500/10 transition-colors" />
              </motion.button>
            </div>

            {/* Content area */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1 }}
              className={`p-6 ${isScrollable ? 'max-h-[70vh] overflow-y-auto custom-scrollbar' : ''}`}
            >
              {children}
            </motion.div>

            {/* Footer with action buttons */}
            {post && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.15 }}
                className="p-6 pt-4 flex justify-end items-center gap-4 bg-gradient-to-t from-white via-white to-white/80 border-t border-white/30"
              >
                <Button
                  name="Close"
                  onClick={handleClose}
                  className="relative overflow-hidden group px-6 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 bg-gray-100 hover:bg-gray-200 text-gray-700"
                >
                  <span className="relative z-10">Close</span>
                  <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 -translate-x-full group-hover:translate-x-full" />
                </Button>

                <Button
                  name={isDelete ? 'Delete' : isEdit ? 'Update' : 'Save'}
                  onClick={post}
                  disabled={isDisabled}
                  className={`relative overflow-hidden group px-6 py-2.5 rounded-xl text-sm font-semibold transition-all duration-300 ${
                    isDisabled
                      ? 'cursor-not-allowed bg-gray-300 text-gray-500'
                      : `shadow-lg ${
                          isDelete
                            ? 'bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white'
                            : 'bg-gradient-to-br from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white'
                        }`
                  }`}
                >
                  <span className="relative z-10 flex items-center gap-1.5">
                    {!isDisabled && !isDelete && (
                      <motion.span
                        animate={{
                          scale: [1, 1.2, 1],
                          rotate: [0, 10, -10, 0]
                        }}
                        transition={{
                          duration: 1.5,
                          repeat: Infinity,
                          repeatDelay: 3
                        }}
                      >
                        <Sparkles size={16} />
                      </motion.span>
                    )}
                    {isDelete ? 'Delete' : isEdit ? 'Update' : 'Save'}
                  </span>
                  {!isDisabled && (
                    <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 -translate-x-full group-hover:translate-x-full" />
                  )}
                </Button>
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Add custom scrollbar styles to your global CSS
// .custom-scrollbar::-webkit-scrollbar { width: 8px; }
// .custom-scrollbar::-webkit-scrollbar-track { background: rgba(0,0,0,0.05); }
// .custom-scrollbar::-webkit-scrollbar-thumb { background: rgba(0,0,0,0.1); border-radius: 4px; }
// .custom-scrollbar::-webkit-scrollbar-thumb:hover { background: rgba(0,0,0,0.15); }

PopUp.propTypes = {
  title: PropTypes.string.isRequired,
  width: PropTypes.oneOf(['sm', 'md', 'lg', 'xl']),
  children: PropTypes.node.isRequired,
  isDelete: PropTypes.bool,
  isEdit: PropTypes.bool,
  isDisabled: PropTypes.bool,
  isScrollable: PropTypes.bool,
  post: PropTypes.func,
  onClose: PropTypes.func.isRequired
};

export default PopUp;
