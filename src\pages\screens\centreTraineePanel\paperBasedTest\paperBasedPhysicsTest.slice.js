import { PaperBasedPhysicsTestApi } from '../../../../redux/api/api';

export const PaperBasedPhysicsTestApiSlice = PaperBasedPhysicsTestApi.injectEndpoints({
  endpoints: (builder) => ({
    paperBasedPhysicsTestStartTest: builder.mutation({
      query: (body) => ({
        url: '/generate-paper-physics',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Paper Based Physics Test Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['PaperBasedPhysicsTest']
    })
  })
});

export const { usePaperBasedPhysicsTestStartTestMutation } = PaperBasedPhysicsTestApiSlice;
