import { createSlice } from '@reduxjs/toolkit';
import { directorApi } from '../../../../redux/api/api';

const initialState = {
  batchesData: null
};

export const createBatchesSlice = directorApi.injectEndpoints({
  endpoints: (builder) => ({
    addBatchesService: builder.mutation({
      query: (body) => ({
        url: '/add-batch',
        method: 'POST',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        // console.log('Create Batches Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['ListBatches']
    }),
    updateBatchService: builder.mutation({
      query: (body) => ({
        url: '/update-batch',
        method: 'PUT',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        // console.log('Update Batch Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['ListBatches']
    }),
    deleteBatchService: builder.mutation({
      query: (body) => ({
        url: '/delete-batch',
        method: 'DELETE',
        body,
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        // console.log('Delete Batches Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['ListBatches']
    }),
    listBatchService: builder.query({
      query: () => ({
        url: '/list-batches',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        // console.log('List Batches Raw Response:', response);
        const batches = response.batches || [];
        // Map batches to ensure batch_id is correctly named
        return batches.map((batch) => ({
          batch_id: batch.batch_id || batch.id, // Fallback to 'id' if batch_id is missing
          batch_name: batch.batch_name,
          description: batch.description,
          course_id: batch.course_id,
          course_name: batch.course_name
        }));
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['ListBatches']
    })
  })
});

const batchesSlice = createSlice({
  name: 'batches',
  initialState,
  reducers: {
    setBatchesData: (state, action) => {
      state.batchesData = action.payload;
    }
  }
});

export const { setBatchesData } = batchesSlice.actions;
export default batchesSlice.reducer;

export const {
  useAddBatchesServiceMutation,
  useUpdateBatchServiceMutation,
  useDeleteBatchServiceMutation,
  useListBatchServiceQuery
} = createBatchesSlice;
