import React, { useState } from 'react';
import ExamAndSubjectSelection from './ExamAndSubjectSelection';
import UnitAndSubtopicSelection from './UnitAndSubtopicSelection';
import TestDisplayWrapper from './TestDisplayWrapper';

const CreateYourOwnTestParent = () => {
  const [selectedExam, setSelectedExam] = useState('');
  const [selectedModule, setSelectedModule] = useState('');
  const [selectedUnits, setSelectedUnits] = useState([]);
  const [selectedSubtopics, setSelectedSubtopics] = useState([]);
  const [testStarted, setTestStarted] = useState(false);

  return (
    <>
      {!selectedModule && (
        <ExamAndSubjectSelection
          setSelectedExam={setSelectedExam}
          selectedExam={selectedExam}
          setSelectedModule={setSelectedModule}
          selectedModule={selectedModule}
        />
      )}
      {selectedModule && !testStarted && (
        <UnitAndSubtopicSelection
          selectedExam={selectedExam}
          selectedModule={selectedModule}
          setSelectedUnits={setSelectedUnits}
          selectedUnits={selectedUnits}
          setSelectedSubtopics={setSelectedSubtopics}
          selectedSubtopics={selectedSubtopics}
          setTestStarted={setTestStarted}
        />
      )}
      {testStarted && <TestDisplayWrapper />}
    </>
  );
};

export default CreateYourOwnTestParent;
