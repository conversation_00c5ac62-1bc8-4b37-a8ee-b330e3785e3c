import React, { useState } from 'react';
import Data from '../../../../assets/kota_details.json';
import { motion, AnimatePresence } from 'framer-motion';
import { FaMapMarkerAlt, FaPhone, FaUserGraduate } from 'react-icons/fa'; // Icons

const NewDirectorDashboard = () => {
  const [selectedCenterId, setSelectedCenterId] = useState(null);
  const [selectedStudentId, setSelectedStudentId] = useState(null);
  const [showCenterDetails, setShowCenterDetails] = useState(false);
  const [showStudentDetails, setShowStudentDetails] = useState(false);

  // Animation Variants (Keep these, they're good)
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
        duration: 0.5
      }
    },
    exit: { opacity: 0, transition: { duration: 0.3 } }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.4,
        type: 'spring',
        stiffness: 100
      }
    },
    exit: { y: -20, opacity: 0, transition: { duration: 0.3 } }
  };

  const detailVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    },
    exit: { scale: 0.8, opacity: 0, transition: { duration: 0.3 } }
  };

  const studentGridVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.2,
        staggerChildren: 0.1
      }
    },
    exit: { opacity: 0 }
  };

  const studentCardVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.4,
        type: 'spring',
        stiffness: 80
      }
    },
    exit: { y: 50, opacity: 0 }
  };

  // Helper Functions (These are good, keep them)
  const handleCenterClick = (centerId) => {
    setSelectedCenterId(centerId === selectedCenterId ? null : centerId);
    setSelectedStudentId(null);
    setShowCenterDetails(true);
    setShowStudentDetails(false);
  };

  const handleStudentClick = (studentId) => {
    setSelectedStudentId(studentId === selectedStudentId ? null : studentId);
    setShowStudentDetails(true);
    setShowCenterDetails(false);
  };

  const handleBackToList = () => {
    setSelectedCenterId(null);
    setSelectedStudentId(null);
    setShowCenterDetails(false);
    setShowStudentDetails(false);
  };

  const handleBackToCenter = () => {
    setShowStudentDetails(false);
    setSelectedStudentId(null);
  };

  // Data Selectors (Keep these)
  const selectedCenter = Data.kota_centers.find((center) => center.center_id === selectedCenterId);
  const selectedStudent = selectedCenter?.students.find(
    (student) => student.student_id === selectedStudentId
  );

  // Reusable Components
  const CenterCard = ({ center, onClick, isSelected }) => (
    <motion.article
      className={`bg-white shadow-md rounded-xl p-5 cursor-pointer hover:shadow-lg transition-shadow duration-300 border-l-4 ${
        isSelected ? 'border-blue-600' : 'border-blue-400'
      }`}
      variants={itemVariants}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
    >
      <h2 className="text-xl font-semibold mb-3 text-blue-800">{center.name}</h2>
      <div className="flex items-center text-gray-600 mb-1">
        <FaMapMarkerAlt className="mr-2" />
        <span>{center.location}</span>
      </div>
      <div className="flex items-center text-gray-600 mb-1">
        <FaPhone className="mr-2" />
        <span>{center.contact}</span>
      </div>
      <div className="flex items-center text-blue-700 font-medium mt-2">
        <FaUserGraduate className="mr-2" />
        <span>{center.students.length} Students</span>
      </div>
    </motion.article>
  );

  const StudentCard = ({ student, onClick }) => (
    <motion.div
      className="bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-300 hover:shadow-lg transition-shadow duration-300 cursor-pointer"
      variants={studentCardVariants}
      whileHover={{ scale: 1.03 }}
      whileTap={{ scale: 0.97 }}
      onClick={onClick}
    >
      <h5 className="text-lg font-semibold text-blue-700">{student.name}</h5>
      <p className="text-gray-600">{student.course}</p>
      <p className="text-sm text-gray-500">Batch: {student.batch}</p>
    </motion.div>
  );

  const StudentDetails = ({ student, onBackToCenter, onBackToList }) => (
    <motion.div
      className="mt-8 p-6 bg-white shadow-md rounded-xl border-l-4 border-green-400"
      variants={detailVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      key="student-details"
    >
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-2xl font-bold text-green-700">Student Details</h3>
        <div>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-700 transition-colors duration-300 mr-2"
            onClick={onBackToCenter}
          >
            Back to Center
          </button>
          <button
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-700 transition-colors duration-300"
            onClick={onBackToList}
          >
            Back to List
          </button>
        </div>
      </div>

      <div className="md:flex gap-6">
        <div className="md:w-1/3">
          <img
            src={'https://via.placeholder.com/150'}
            alt="Student Profile"
            className="rounded-xl shadow-md mb-4 w-full h-auto"
          />
        </div>
        <div className="md:w-2/3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <DetailItem label="Name" value={student?.name} />
              <DetailItem label="Age" value={student?.age} />
              <DetailItem label="Gender" value={student?.gender} />
              <DetailItem label="Course" value={student?.course} />
              <DetailItem label="Batch" value={student?.batch} />
            </div>
            <div>
              <DetailItem label="Contact" value={student?.contact} />
              <DetailItem label="Email" value={student?.email} />
              <DetailItem label="Address" value={student?.address} />
              <DetailItem label="Guardian Name" value={student?.guardian_name} />
              <DetailItem label="Guardian Contact" value={student?.guardian_contact} />
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );

  const DetailItem = ({ label, value }) => (
    <p>
      <strong className="font-medium">{label}:</strong> {value}
    </p>
  );

  // Main Return
  return (
    <motion.div
      className="container mx-auto p-6 bg-gray-50"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      <motion.h1
        className="text-3xl font-extrabold text-center text-blue-700 mb-8"
        variants={itemVariants}
      >
        Kota Centers Dashboard
      </motion.h1>

      {/* Breadcrumbs */}
      {showCenterDetails && (
        <div className="mb-4">
          <button onClick={handleBackToList} className="text-blue-500 hover:text-blue-700">
            All Centers
          </button>
          <span className="mx-2"></span>
          <span className="font-semibold">{selectedCenter.name}</span>
        </div>
      )}

      {showStudentDetails && (
        <div className="mb-4">
          <button onClick={handleBackToList} className="text-blue-500 hover:text-blue-700">
            All Centers
          </button>
          <span className="mx-2"></span>
          <button onClick={handleBackToCenter} className="text-blue-500 hover:text-blue-700">
            {selectedCenter.name}
          </button>
          <span className="mx-2"></span>
          <span className="font-semibold">{selectedStudent.name}</span>
        </div>
      )}

      <AnimatePresence>
        {!showCenterDetails && !showStudentDetails && (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            variants={containerVariants}
            exit="exit"
            key="center-list"
          >
            {Data.kota_centers.map((center) => (
              <CenterCard
                key={center.center_id}
                center={center}
                onClick={() => handleCenterClick(center.center_id)}
                isSelected={selectedCenterId === center.center_id}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showCenterDetails && selectedCenter && (
          <motion.div
            className="mt-8 p-6 bg-white shadow-md rounded-xl border-l-4 border-blue-400"
            variants={detailVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            key="center-details"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-blue-700">Center Details</h3>
              <button
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-700 transition-colors duration-300"
                onClick={handleBackToList}
              >
                Back to List
              </button>
            </div>
            <p>
              <strong className="font-medium">Name:</strong> {selectedCenter.name}
            </p>
            <p>
              <strong className="font-medium">Location:</strong> {selectedCenter.location}
            </p>
            <p>
              <strong className="font-medium">Contact:</strong> {selectedCenter.contact}
            </p>
            <p>
              <strong className="font-medium">Total Students:</strong>{' '}
              {selectedCenter.students.length}
            </p>

            <h4 className="text-xl font-semibold mt-4 mb-2 text-gray-800">Students</h4>
            {selectedCenter.students.length === 0 ? (
              <p className="text-gray-500">No students enrolled in this center yet.</p>
            ) : (
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                variants={studentGridVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                {selectedCenter.students.map((student) => (
                  <StudentCard
                    key={student.student_id}
                    student={student}
                    onClick={() => handleStudentClick(student.student_id)}
                  />
                ))}
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showStudentDetails && selectedStudent && (
          <StudentDetails
            student={selectedStudent}
            onBackToCenter={handleBackToCenter}
            onBackToList={handleBackToList}
          />
        )}
      </AnimatePresence>

      {!showCenterDetails && !showStudentDetails && (
        <motion.div className="mt-6 text-center" variants={itemVariants}>
          <p className="text-gray-500">Total Centers: {Data.kota_centers.length}</p>
        </motion.div>
      )}
    </motion.div>
  );
};

export default NewDirectorDashboard;
