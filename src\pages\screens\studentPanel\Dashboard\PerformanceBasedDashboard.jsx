'use client';

import { useState } from 'react';
// eslint-disable-next-line no-unused-vars
import { motion, AnimatePresence } from 'framer-motion';
import {
  Trophy,
  Target,
  CheckCircle,
  Clock,
  TrendingUp,
  AlertTriangle,
  Info,
  ChevronRight,
  Award,
  BarChart3
} from 'lucide-react';

const PerformanceBasedDashboard = () => {
  const [selectedCard, setSelectedCard] = useState(null);
  const [hoveredTooltip, setHoveredTooltip] = useState(null);

  // Animation Variants
  const containerVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const cardVariants = {
    initial: { opacity: 0, y: 30, scale: 0.95 },
    animate: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    hover: {
      y: -8,
      scale: 1.02,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    },
    tap: { scale: 0.98 }
  };

  const progressVariants = {
    initial: { width: 0 },
    animate: {
      width: '100%',
      transition: {
        duration: 1.5,
        delay: 0.5,
        ease: 'easeInOut'
      }
    }
  };

  // Enhanced card data with better structure
  const cardData = [
    {
      id: 1,
      title: 'Overall Score',
      description: 'Your total performance score',
      value: '226',
      maxValue: '300',
      percentage: 75.3,
      color: 'bg-gradient-to-br from-orange-400 to-orange-600',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-600',
      borderColor: 'border-orange-200',
      progressColor: 'bg-orange-500',
      icon: Trophy,
      trend: '+12%',
      status: 'excellent'
    },
    {
      id: 2,
      title: 'Questions Attempted',
      description: 'Total questions you tackled',
      value: '59',
      maxValue: '75',
      percentage: 78.7,
      color: 'bg-gradient-to-br from-blue-400 to-blue-600',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600',
      borderColor: 'border-blue-200',
      progressColor: 'bg-blue-500',
      icon: Target,
      trend: '+5%',
      status: 'good'
    },
    {
      id: 3,
      title: 'Accuracy Rate',
      description: 'Percentage of correct answers',
      value: '96.61',
      maxValue: '100',
      percentage: 96.61,
      color: 'bg-gradient-to-br from-green-400 to-green-600',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600',
      borderColor: 'border-green-200',
      progressColor: 'bg-green-500',
      icon: CheckCircle,
      trend: '****%',
      status: 'excellent'
    },
    {
      id: 4,
      title: 'Time Efficiency',
      description: 'Time management performance',
      value: '112',
      maxValue: '180',
      percentage: 62.2,
      color: 'bg-gradient-to-br from-yellow-400 to-yellow-600',
      bgColor: 'bg-yellow-50',
      textColor: 'text-yellow-600',
      borderColor: 'border-yellow-200',
      progressColor: 'bg-yellow-500',
      icon: Clock,
      trend: '-8%',
      status: 'average',
      unit: 'min'
    },
    {
      id: 5,
      title: 'Positive Score',
      description: 'Score from correct answers only',
      value: '228',
      maxValue: '300',
      percentage: 76,
      color: 'bg-gradient-to-br from-purple-400 to-purple-600',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-600',
      borderColor: 'border-purple-200',
      progressColor: 'bg-purple-500',
      icon: TrendingUp,
      trend: '+15%',
      status: 'excellent'
    },
    {
      id: 6,
      title: 'Marks Deducted',
      description: 'Points lost from incorrect answers',
      value: '2',
      maxValue: '50',
      percentage: 4,
      color: 'bg-gradient-to-br from-red-400 to-red-600',
      bgColor: 'bg-red-50',
      textColor: 'text-red-600',
      borderColor: 'border-red-200',
      progressColor: 'bg-red-500',
      icon: AlertTriangle,
      trend: '-50%',
      status: 'excellent'
    }
  ];

  const getStatusBadge = (status) => {
    const statusConfig = {
      excellent: { label: 'Excellent', className: 'bg-green-100 text-green-800 border-green-200' },
      good: { label: 'Good', className: 'bg-blue-100 text-blue-800 border-blue-200' },
      average: { label: 'Average', className: 'bg-yellow-100 text-yellow-800 border-yellow-200' }
    };

    const config = statusConfig[status] || statusConfig.average;
    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.className}`}
      >
        {config.label}
      </span>
    );
  };

  const handleCardClick = (cardId) => {
    setSelectedCard(selectedCard === cardId ? null : cardId);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4 md:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <motion.div
          className="mb-8 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg">
              <BarChart3 className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
              Performance Dashboard
            </h1>
          </div>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Comprehensive analysis of your test performance with detailed insights and metrics
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
          variants={containerVariants}
          initial="initial"
          animate="animate"
        >
          {cardData.map((card) => {
            const IconComponent = card.icon;
            const isSelected = selectedCard === card.id;

            return (
              <motion.div
                key={card.id}
                variants={cardVariants}
                whileHover="hover"
                whileTap="tap"
                className="group cursor-pointer"
                onClick={() => handleCardClick(card.id)}
              >
                {/* Custom Card */}
                <div
                  className={`relative overflow-hidden border-2 ${card.borderColor} ${card.bgColor} hover:shadow-xl transition-all duration-300 rounded-lg bg-white shadow-md ${
                    isSelected ? 'ring-2 ring-indigo-500 ring-offset-2' : ''
                  }`}
                >
                  <div className="p-6">
                    {/* Card Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className={`p-3 rounded-xl ${card.color} shadow-lg`}>
                          <IconComponent className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-800 text-lg">{card.title}</h3>
                          {getStatusBadge(card.status)}
                        </div>
                      </div>
                      {/* Custom Tooltip */}
                      <div
                        className="relative"
                        onMouseEnter={() => setHoveredTooltip(card.id)}
                        onMouseLeave={() => setHoveredTooltip(null)}
                      >
                        <Info className="w-4 h-4 text-gray-400 hover:text-gray-600 transition-colors cursor-help" />
                        {hoveredTooltip === card.id && (
                          <div className="absolute right-0 top-6 z-50 w-64 p-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg">
                            <p className="max-w-xs">{card.description}</p>
                            <div className="absolute -top-1 right-2 w-2 h-2 bg-gray-900 rotate-45"></div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Main Value */}
                    <div className="mb-4">
                      <div className="flex items-baseline gap-2 mb-2">
                        <span className="text-3xl font-bold text-gray-800">{card.value}</span>
                        {card.maxValue && (
                          <span className="text-lg text-gray-500">/{card.maxValue}</span>
                        )}
                        {card.unit && (
                          <span className="text-sm text-gray-500 ml-1">{card.unit}</span>
                        )}
                        {card.title === 'Accuracy Rate' && (
                          <span className="text-lg text-gray-500">%</span>
                        )}
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                        <motion.div
                          className={`h-2 rounded-full ${card.progressColor}`}
                          variants={progressVariants}
                          initial="initial"
                          animate="animate"
                          style={{ width: `${card.percentage}%` }}
                        />
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">
                          {card.percentage.toFixed(1)}% Complete
                        </span>
                        <span
                          className={`font-medium ${
                            card.trend.startsWith('+')
                              ? 'text-green-600'
                              : card.trend.startsWith('-')
                                ? 'text-red-600'
                                : 'text-gray-600'
                          }`}
                        >
                          {card.trend}
                        </span>
                      </div>
                    </div>

                    {/* Expandable Details */}
                    <AnimatePresence>
                      {isSelected && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="border-t border-gray-200 pt-4 mt-4"
                        >
                          <p className="text-sm text-gray-600 mb-3">{card.description}</p>
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">Last updated: 2 hours ago</span>
                            {/* Custom Button */}
                            <button className="inline-flex items-center px-3 py-1 text-xs font-medium text-gray-700 bg-transparent hover:bg-gray-100 rounded-md transition-colors duration-200">
                              View Details
                              <ChevronRight className="w-3 h-3 ml-1" />
                            </button>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {/* Hover Indicator */}
                    <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Action Buttons */}
      </div>
    </div>
  );
};

export default PerformanceBasedDashboard;
