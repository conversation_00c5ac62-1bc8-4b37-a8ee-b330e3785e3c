@import 'tailwindcss';

* {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
/* Custom Theme Variables */
@theme {
  --color-parents: #6e2fca;
  --color-counselor: #f4c430;
  --color-trainee: #f59e0b;
  --color-student: #2563eb;
  --color-director: #7d1e1c;
  --color-teacher: #000080;
  --color-mentor: #7c007c;
}

/* Blob Animation */
@keyframes blob {
  0% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0, 0) scale(1);
  }
}

/* Float Animation */
@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

/* Animation Utilities */
.animate-blob {
  animation: blob 7s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Animation Delay Utilities (scalable and uniform naming) */
.animation-delay-1000 {
  animation-delay: 1s;
}
.animation-delay-2000 {
  animation-delay: 2s;
}
.animation-delay-3000 {
  animation-delay: 3s;
}
.animation-delay-4000 {
  animation-delay: 4s;
}
